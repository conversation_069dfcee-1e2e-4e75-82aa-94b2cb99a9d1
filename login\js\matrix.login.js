$(document).ready(function () {

    var login = $('#loginform');
    var recover = $('#recoverform');
    var regform = $('#regform');
    var speed = 400;
    var inter_error = '';
    if(i18nLanguage==='en_US') {
        inter_error = "Internal service error, please contact us";
    }
    else {
        inter_error = "内部服务错误";
    }
    var pageSelect = {
        dev: "product_cls.html",
        face: "faceinfo.html",
        user: "user_mg.html",
        attendance: "attendance_mg.html"
    };


    $('#to-recover').click(function () {
        $('#reset_pwd').val('');
        $('#reset_phone').val('');
        $('#reset_code').val('');

        $("#loginform").slideUp();
        $("#recoverform").fadeIn();
        $("#regform").hide();
    });

    $('.to-login').click(function () {
        $("#recoverform").hide();
        $("#regform").hide();
        $("#loginform").fadeIn();
    });


    $('#to-register').click(function () {
        $('#reg_username').val('');
        $('#reg_pwd').val('');
        $('#reg_pwd2').val('');
        $('#reg_phone').val('');
        $('#reg_code').val('');
        $('#reg_license').val('');

        $("#recoverform").hide();
        $("#loginform").slideUp();
        $("#regform").fadeIn();
    });

    if ($.browser.msie == true && $.browser.version.slice(0, 3) < 10) {
        $('input[placeholder]').each(function () {
            var input = $(this);
            $(input).val(input.attr('placeholder'));

            $(input).focus(function () {
                if (input.val() == input.attr('placeholder')) {
                    input.val('');
                }
            });

            $(input).blur(function () {
                if (input.val() == '' || input.val() == input.attr('placeholder')) {
                    input.val(input.attr('placeholder'));
                }
            });
        });
    }

    // 阻止表单默认提交行为，改用ajax提交
    $('#loginform').submit(function(e) {
        e.preventDefault();
        
        var username = $('input[name="username"]').val();
        var password = $('input[name="password"]').val();
        
        // 表单验证
        if (!username || username.trim() === '') {
            $("#login_error span").html('用户名不能为空');
            $("#login_error").show();
            return false;
        }
        
        if (!password || password.trim() === '') {
            $("#login_error span").html('密码不能为空');
            $("#login_error").show();
            return false;
        }
        
        // 发送AJAX请求
        $.ajax({
            url: '/apis/login/',
            type: 'POST',
            data: {
                username: username,
                password: password
            },
            contentType: 'application/x-www-form-urlencoded',
            success: function(data) {
                // 处理返回数据
                if (typeof data === 'string') {
                    try {
                        data = JSON.parse(data);
                    } catch(e) {
                        console.error('解析响应数据失败:', e);
                        $("#login_error span").html('服务器返回数据格式错误');
                        $("#login_error").show();
                        return;
                    }
                }
                
                if (data.status === 'ok') {
                    // 登录成功
                    console.log('登录成功，准备跳转...');
                    // 设置登录状态
                    sessionStorage.setItem('adminLoggedIn', 'true');
                    
                    // 设置用户名cookie
                    try {
                        if (typeof $.cookie === 'function') {
                            $.cookie('username', username, { path: '/' });
                        } else {
                            console.warn('$.cookie不可用，使用sessionStorage代替');
                            sessionStorage.setItem('username', username);
                        }
                    } catch (e) {
                        console.error('设置cookie失败:', e);
                        sessionStorage.setItem('username', username);
                    }
                    
                    // 直接跳转到admin_home.html
                    window.location.href = '/admin/admin_home.html';
                } else {
                    // 登录失败
                    $("#login_error span").html(data.msg || '用户名或密码错误');
                    $("#login_error").show();
                }
            },
            error: function(xhr, status, error) {
                console.error('登录请求失败:', status, error);
                $("#login_error span").html('服务器连接错误，请稍后再试');
                $("#login_error").show();
            }
        });
        
        return false;
    });
});


