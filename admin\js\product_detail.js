/**
 * 产品详情管理JS
 * 实现产品详情的增删改查、排序、显示/隐藏等功能
 */

// 全局变量
var currentPage = 1;
var pageSize = 10;
var totalPage = 0;
var currentLang = '';
var currentInfoType = '';
// 新增：保存所有类型
window.allProductTypes = [];

/**
 * 从产品列表数据中提取唯一的产品类型
 * @param {Array} products 产品列表数据
 * @returns {Array} 产品类型列表
 */
function extractProductTypes(products) {
    if (!products || !Array.isArray(products)) return [];
    
    // 使用Set去重
    const typeSet = new Set();
    products.forEach(product => {
        if (product.info_type) {
            typeSet.add(product.info_type);
        }
    });
    
    // 转换为数组并排序
    return Array.from(typeSet).sort();
}

/**
 * 加载产品详情列表
 * @param {number} page 页码
 * @param {number} size 每页条数
 * @param {string} lang 语言筛选（可选）
 * @param {string} infoType 产品类型筛选（可选）
 */
function product_detail(page, size, lang, infoType) {
    currentPage = page || 1;
    pageSize = size || 10;
    currentLang = lang || '';
    currentInfoType = infoType || '';
    
    // 构建请求参数
    var params = {
        page: currentPage,
        size: pageSize
    };
    
    // 始终传递筛选条件（即使为空字符串）
        params.filters = JSON.stringify({
            lang: currentLang,
            info_type: currentInfoType
        });
    
    // 发送请求获取数据
    $.ajax({
        type: "get",
        url: "/apis/product_detail_list/",
        data: params,
        success: function (data) {
            if (data.status === 'ok') {
                render_product_detail(data.data);
                totalPage = Math.ceil(data.total / pageSize);
                page_ctrl(currentPage, totalPage);
                fetchProductTypesAndFill(); // 每次都刷新类型下拉框
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '加载产品详情列表失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，加载产品详情列表失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 获取所有可用的产品类型
 * @param {Function} callback 回调函数，参数为产品类型列表
 */
function fetchProductTypes() {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "get",
            url: "/apis/product_detail_types/",
            success: function (data) {
                if (data.status === 'ok') {
                    resolve(data.data);
                } else {
                    reject(new Error(data.msg || '获取产品类型失败'));
                }
            },
            error: function (xhr, status, error) {
                reject(new Error('网络错误，获取产品类型失败'));
            }
        });
    });
}

/**
 * 更新产品类型下拉选项
 * @param {Array} types 产品类型列表
 */
function updateProductTypeSelects(types) {
    // 记录当前选中值
    var currentFilter = $('#info_type_filter').val();
    var currentEdit = $('#edit_info_type').val();
    var currentAdd = $('#add_info_type').val();

    // 构建选项HTML
    var optionsHtml = '<option value="">所有类型</option>';
    types.forEach(function(type) {
        optionsHtml += '<option value="' + type + '">' + type + '</option>';
    });
    
    // 更新所有产品类型选择器，并恢复原有选中项
    $('#info_type_filter').html(optionsHtml).val(currentFilter);
    $('#edit_info_type').html(optionsHtml).val(currentEdit);
    $('#add_info_type').html(optionsHtml).val(currentAdd);
}

/**
 * 获取产品类型显示文本
 * @param {string} typeCode 产品类型代码
 * @param {Array} types 产品类型列表
 * @returns {string} 显示文本
 */
function getProductTypeText(typeCode, types) {
    if (!types || !typeCode) return '(无类型)';
    const type = types.find(t => t.type_code === typeCode);
    return type ? type.type_name : typeCode;
}

/**
 * 渲染产品详情列表
 * @param {Array} data 产品数据
 */
function render_product_detail(data) {
    var html = '';
    
    if (!data || data.length === 0) {
        html = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
    } else {
        $.each(data, function (index, item) {
            var langText = item.lang === 0 ? '<span class="label label-success">中文网站</span>' : '<span class="label label-info">英文网站</span>';
            var bg_style = item.lang === 0 ? ' style="background-color:#d2d2f7"' : '';
            var infoTypeText = item.info_type || '(无类型)';
            var titleDisplay = item.title || '(无标题)';
            var hasImage = item.image_path && item.image_path.trim() !== '';
            var hasText = item.content && item.content.trim() !== '';
            var imagePreview = hasImage ? '<div class="preview-image"><img src="' + item.image_path + '" alt="产品图片" style="max-height: 50px;"></div>' : '<div class="preview-empty">无图片</div>';
            var textPreview = hasText ? '<div class="preview-text">' + (item.content.length > 50 ? item.content.substring(0, 50) + '...' : item.content) + '</div>' : '<div class="preview-empty">无描述</div>';
            var contentPreview = '<div class="preview-container">';
            contentPreview += '<div class="preview-column image-column">' + imagePreview + '</div>';
            contentPreview += '<div class="preview-column text-column">' + textPreview + '</div>';
            contentPreview += '</div>';
            var displayOrder = item.display_order || 100;
            
            // 修复：统一处理show字段的各种可能值(布尔值、数字、字符串)
            var isShow = item.show === true || item.show === 1 || item.show === "1";
            var showText = isShow ? 
                '<span class="badge badge-success toggle-show-status" data-id="' + item.id + '" data-show="1" style="cursor:pointer" title="点击切换显示状态">显示</span>' : 
                '<span class="badge badge-important toggle-show-status" data-id="' + item.id + '" data-show="0" style="cursor:pointer" title="点击切换显示状态">不显示</span>';
            
            var updateTime = formatDateTime(item.update_time);
            html += '<tr>' +
                '<td' + bg_style + '>' + item.id + '</td>' +
                '<td' + bg_style + '>' + langText + '</td>' +
                '<td' + bg_style + '>' + infoTypeText + '</td>' +
                '<td' + bg_style + '>' + titleDisplay + '</td>' +
                '<td' + bg_style + '>' + contentPreview + '</td>' +
                '<td' + bg_style + '>' + displayOrder + '</td>' +
                '<td' + bg_style + '>' + showText + '</td>' +
                '<td' + bg_style + '>' + updateTime + '</td>' +
                '<td' + bg_style + '>' +
                '<button class="btn btn-primary btn-mini" onclick="show_edit_modal(' + item.id + ')">编辑</button> ' +
                '<button class="btn btn-danger btn-mini" onclick="show_delete_modal(' + item.id + ')">删除</button>' +
                '</td>' +
                '</tr>';
        });
    }
    
    $("#data_list").html(html);
    
    // 绑定显示状态切换事件
    $('.toggle-show-status').off('click').on('click', function(e) {
        e.preventDefault(); e.stopPropagation();
        var id = $(this).data('id');
        var currentShow = parseInt($(this).data('show'));
        var newShow = currentShow !== 1;
        toggle_show_status(id, newShow);
    });
}

/**
 * 显示编辑模态框
 * @param {number} id 产品ID
 */
function show_edit_modal(id) {
    console.log('显示编辑模态框，ID:', id);
    
    // 发送请求获取产品详情
    $.ajax({
        type: "get",
        url: "/apis/product_info_detail/",
        data: {
            id: Number(id)  // 使用Number()进行类型转换
        },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                
                // 填充表单数据
                $('#edit_id').val(item.id);
                // 修复：统一处理show字段的各种可能值(布尔值、数字、字符串)
                var isShow = item.show === true || item.show === 1 || item.show === "1";
                $('#edit_show').val(isShow ? "True" : "False");
                $('#edit_lang').val(item.lang);
                $('#edit_info_type').val(item.info_type || '');
                $('#edit_display_order').val(item.display_order || 100);
                $('#edit_title').val(item.title || '');
                $('#edit_content').val(item.content || '');
                $('#edit_url').val(item.url || '');
                
                // 处理图片
                if (item.image_path) {
                    $('#edit_image').attr('src', item.image_path);
                } else {
                    $('#edit_image').attr('src', '');
                }
                
                // 重置图片相关状态
                $('#edit_new_image').val('');
                $('#edit_image_removed').val('false');
                
                // 显示模态框
                $('#editProductAlert').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '获取产品详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，获取产品详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 编辑产品详情
 */
function edit_product() {
    // 检查是否点击了清除图片按钮
    var imageRemoved = $('#edit_image_removed').val() === 'true';
    var newImageFile = $('#edit_image_file')[0] ? $('#edit_image_file')[0].files[0] : null;

    // 使用FormData来提交数据，这样可以同时处理文件和普通数据
    var formData = new FormData();
    formData.append('id', $('#edit_id').val());
    formData.append('show', $('#edit_show').val() === "True");
    formData.append('lang', $('#edit_lang').val());
    formData.append('info_type', $('#edit_info_type').val());
    formData.append('display_order', $('#edit_display_order').val());
    formData.append('title', $('#edit_title').val());
    formData.append('content', $('#edit_content').val());
    formData.append('url', $('#edit_url').val());

    // 处理图片逻辑
    if (imageRemoved) {
        // 如果点击了清除图片，传递空字符串的image_path
        formData.append('image_path', '');
        console.log('编辑：清除图片，传递空的image_path');
    } else if (newImageFile) {
        // 如果有新上传的图片文件
        formData.append('image', newImageFile);
        console.log('编辑：上传新图片文件');
    }
    // 如果都没有，则不传递image相关字段，保持原有图片

    submitEditFormData(formData);
}

/**
 * 提交编辑表单数据
 */
function submitEditFormData(formData) {
    $.ajax({
        type: "post",
        url: "/apis/update_product_detail/",
        data: formData,
        processData: false,
        contentType: false,
        success: function (res) {
            if (res.status === 'ok') {
                // 关闭模态框
                $('#editProductAlert').modal('hide');

                // 显示成功消息
                $.gritter.add({
                    title: '成功',
                    text: '更新产品详情成功',
                    sticky: false,
                    time: 3000
                });

                // 重新加载数据
                product_detail(currentPage, pageSize, currentLang, currentInfoType);
            } else {
                $.gritter.add({
                    title: '错误',
                    text: res.msg || '更新产品详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function () {
            $.gritter.add({
                title: '错误',
                text: '网络错误，更新产品详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}



/**
 * 添加产品详情
 */
function add_product() {
    // 获取表单数据
    var formData = new FormData();
    // 修复：统一处理show字段，确保为正确的值
    var isShow = $('#add_show').val() === "True";
    formData.append('show', isShow);
    formData.append('lang', $('#add_lang').val());
    formData.append('info_type', $('#add_info_type').val());
    formData.append('display_order', $('#add_display_order').val());
    formData.append('title', $('#add_title').val());
    formData.append('content', $('#add_content').val());
    formData.append('url', $('#add_url').val());
    
    // 处理图片
    var imageRemoved = $('#add_image_removed').val() === 'true';
    var newImageFile = $('#add_image_file')[0] ? $('#add_image_file')[0].files[0] : null;

    if (imageRemoved) {
        // 如果点击了清除图片，传递空字符串的image_path
        formData.append('image_path', '');
        console.log('新增：清除图片，传递空的image_path');
    } else if (newImageFile) {
        // 如果有新图片文件
        formData.append('image', newImageFile);
        console.log('新增：添加图片文件');
    } else {
        // 没有图片
        console.log('新增：没有图片');
    }

    submitAddFormData(formData);
}

/**
 * 提交新增表单数据
 */
function submitAddFormData(formData) {
    $.ajax({
        type: "post",
        url: "/apis/add_product_detail/",
        data: formData,
        processData: false,
        contentType: false,
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#addProductAlert').modal('hide');

                // 显示成功消息
                $.gritter.add({
                    title: '成功',
                    text: '添加产品详情成功',
                    sticky: false,
                    time: 3000
                });

                // 重置表单
                resetAddForm();

                // 重新加载数据
                product_detail(currentPage, pageSize, currentLang, currentInfoType);
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '添加产品详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，添加产品详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 显示删除确认模态框
 * @param {number} id 产品ID
 */
function show_delete_modal(id) {
    console.log('显示删除模态框，ID:', id);
    
    // 发送请求获取产品详情
    $.ajax({
        type: "get",
        url: "/apis/product_info_detail/",
        data: {
            id: Number(id)  // 使用Number()进行类型转换
        },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                
                // 填充确认信息
                $('#del_id').val(item.id);
                $('#del_id_span').text(item.id);
                $('#del_title').text(item.title || '(无标题)');
                
                // 处理内容预览
                var contentPreview = item.content ? 
                    (item.content.length > 100 ? item.content.substring(0, 100) + '...' : item.content) : 
                    '(无内容)';
                $('#del_content').text(contentPreview);
                
                // 处理图片预览
                if (item.image_path) {
                    $('#del_image').attr('src', item.image_path);
                    $('#del_image').show();
                    $('#del_image_container').show();
                } else {
                    $('#del_image').hide();
                    $('#del_image_container').hide();
                }
                
                // 显示模态框
                $('#deleteProductAlert').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '获取产品详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，获取产品详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 删除产品详情
 */
function del_product() {
    var id = Number($('#del_id').val());  // 使用Number()进行类型转换
    
    // 发送请求
    $.ajax({
        type: "post",
        url: "/apis/delete_product_detail/",  // 使用正确的产品详情删除API
        data: {
            id: id
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#deleteProductAlert').modal('hide');
                
                // 显示成功消息
                $.gritter.add({
                    title: '成功',
                    text: '删除产品详情成功',
                    sticky: false,
                    time: 3000
                });
                
                // 重新加载数据
                product_detail(currentPage, pageSize, currentLang, currentInfoType);
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '删除产品详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，删除产品详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 切换产品详情显示状态
 * @param {number} id 产品ID
 * @param {boolean} newShow 新的显示状态
 */
function toggle_show_status(id, newShow) {
    // 修复：确保发送数字值而不是布尔值
    var showValue = newShow ? 1 : 0;
    
    $.ajax({
        type: "post",
        url: "/apis/toggle_product_detail_status/",
        data: JSON.stringify({ id: id, show: showValue }),
        contentType: "application/json",
        success: function (res) {
            if (res.status === 'ok') {
                $.gritter.add({
                    title: '成功',
                    text: '切换显示状态成功',
                    sticky: false,
                    time: 1500
                });
                product_detail(currentPage, pageSize, currentLang, currentInfoType);
            } else {
                $.gritter.add({
                    title: '错误',
                    text: res.msg || '切换显示状态失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function () {
            $.gritter.add({
                title: '错误',
                text: '网络错误，切换显示状态失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 分页控制
 * @param {number} currentPage 当前页码
 * @param {number} totalPage 总页数
 */
function page_ctrl(currentPage, totalPage) {
    var html = "";
    
    // 如果总页数小于等于1，不显示分页
    if (totalPage <= 1) {
        $("#page").html(html);
        return;
    }
    
    // 上一页按钮
    if (currentPage > 1) {
        html += '<a class="first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default" onclick="product_detail(' + (currentPage - 1) + ', ' + pageSize + ', \'' + currentLang + '\', \'' + currentInfoType + '\')">上一页</a>';
    } else {
        html += '<a class="first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default ui-state-disabled">上一页</a>';
    }
    
    // 页码按钮
    var startPage = Math.max(1, currentPage - 2);
    var endPage = Math.min(totalPage, startPage + 4);
    
    if (startPage > 1) {
        html += '<a class="fg-button ui-button ui-state-default" onclick="product_detail(1, ' + pageSize + ', \'' + currentLang + '\', \'' + currentInfoType + '\')">1</a>';
        if (startPage > 2) {
            html += '<span class="fg-button ui-button ui-state-default">...</span>';
        }
    }
    
    for (var i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            html += '<a class="fg-button ui-button ui-state-default ui-state-disabled">' + i + '</a>';
        } else {
            html += '<a class="fg-button ui-button ui-state-default" onclick="product_detail(' + i + ', ' + pageSize + ', \'' + currentLang + '\', \'' + currentInfoType + '\')">' + i + '</a>';
        }
    }
    
    if (endPage < totalPage) {
        if (endPage < totalPage - 1) {
            html += '<span class="fg-button ui-button ui-state-default">...</span>';
        }
        html += '<a class="fg-button ui-button ui-state-default" onclick="product_detail(' + totalPage + ', ' + pageSize + ', \'' + currentLang + '\', \'' + currentInfoType + '\')">' + totalPage + '</a>';
    }
    
    // 下一页按钮
    if (currentPage < totalPage) {
        html += '<a class="last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default" onclick="product_detail(' + (currentPage + 1) + ', ' + pageSize + ', \'' + currentLang + '\', \'' + currentInfoType + '\')">下一页</a>';
    } else {
        html += '<a class="last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default ui-state-disabled">下一页</a>';
    }
    
    $("#page").html(html);
}

/**
 * 重置添加表单
 */
function resetAddForm() {
    $('#add_show').val("True");
    $('#add_lang').val("0");
    $('#add_info_type').val("");
    $('#add_display_order').val("100");
    $('#add_title').val("");
    $('#add_content').val("");
    $('#add_url').val("");
    $('#add_image').attr('src', '');
    $('#add_new_image').val('');
    $('#add_image_removed').val('false');
}

/**
 * 格式化日期时间
 * @param {string} datetime 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(datetime) {
    if (!datetime) {
        return '';
    }
    
    try {
        var date = new Date(datetime);
        
        if (isNaN(date.getTime())) {
            // 尝试解析其他格式
            if (typeof datetime === 'string') {
                // 尝试处理MySQL格式的日期时间
                var parts = datetime.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/);
                if (parts) {
                    date = new Date(parts[1], parts[2] - 1, parts[3], parts[4], parts[5], parts[6]);
                }
            }
            
            if (isNaN(date.getTime())) {
                return datetime; // 无法解析，返回原始值
            }
        }
        
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        var seconds = ('0' + date.getSeconds()).slice(-2);
        
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    } catch (e) {
        console.error('格式化日期时间错误:', e);
        return datetime; // 发生错误，返回原始值
    }
} 

// 新增：类型下拉框每次都从后端接口获取
function fetchProductTypesAndFill() {
    var currentType = $('#info_type_filter').val() || '';
    $.get('/apis/product_detail_types/', function(res) {
        if(res.status === 'ok' && Array.isArray(res.data)) {
            var allTypes = res.data;
            var $typeFilter = $('#info_type_filter');
            $typeFilter.empty().append('<option value="">所有类型</option>');
            allTypes.forEach(function(type) {
                $typeFilter.append('<option value="'+type+'">'+type+'</option>');
            });
            // 恢复选中项
            $typeFilter.val(currentType);
        }
    });
}

// 显示生成页面模态框
function show_generate_page_modal() {
    console.log('显示生成页面模态框');
    
    // 重置表单
    $('#gen_product_type').val('');
    $('#gen_product_type_en').val('');
    $('#gen_result').empty();
    
    // 显示模态框
    $('#generatePageModal').modal('show');
}

// 生成产品页面
function generate_product_page() {
    var productType = $('#gen_product_type').val();
    var productTypeEn = $('#gen_product_type_en').val();
    
    if (!productType || !productTypeEn) {
        $.gritter.add({
            title: '警告',
            text: '请填写中文和英文产品类型名称',
            sticky: false,
            time: 3000
        });
        return;
    }
    
    // 显示加载状态
    $('#gen_btn').prop('disabled', true).text('生成中...');
    $('#gen_result').html('<div class="alert alert-info">正在生成页面...</div>');
    
    // 调用API生成页面
    $.ajax({
        type: "post",
        url: "/apis/generate_product_pages/",
        contentType: "application/json",
        data: JSON.stringify({
            productType: productType,
            productTypeEn: productTypeEn
        }),
        success: function(data) {
            // 恢复按钮状态
            $('#gen_btn').prop('disabled', false).text('生成页面');
            
            if (data.status === 'ok') {
                // 显示成功消息
                $('#gen_result').html(
                    '<div class="alert alert-success">' +
                    '<strong>生成成功!</strong><br>' +
                    '中文页面: <a href="' + data.files.chinese + '" target="_blank">' + data.files.chinese + '</a><br>' +
                    '英文页面: <a href="' + data.files.english + '" target="_blank">' + data.files.english + '</a>' +
                    '</div>'
                );
            } else {
                // 显示错误消息
                $('#gen_result').html(
                    '<div class="alert alert-error">' +
                    '<strong>生成失败!</strong> ' + (data.msg || '未知错误') +
                    '</div>'
                );
            }
        },
        error: function(xhr, status, error) {
            // 恢复按钮状态
            $('#gen_btn').prop('disabled', false).text('生成页面');
            
            // 显示错误消息
            $('#gen_result').html(
                '<div class="alert alert-error">' +
                '<strong>生成失败!</strong> 网络错误，请稍后重试' +
                '</div>'
            );
        }
    });
} 

// 图片上传预览和上传逻辑（与download_detail.js一致）
$(document).on('change', '#add_image_file', function() {
    var file = this.files[0];
    if (!file) return;
    var formData = new FormData();
    formData.append('file', file);
    $.ajax({
        url: '/apis/upload_pic/',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if(res.status === 'ok' && res.path) {
                $('#add_image_path').val(res.path);
                $('#add_image_preview').attr('src', res.path).show();
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '图片上传失败', sticky: false, time: 3000 });
            }
        },
        error: function() {
            $.gritter.add({ title: '错误', text: '图片上传失败', sticky: false, time: 3000 });
        }
    });
});
$(document).on('change', '#edit_image_file', function() {
    var file = this.files[0];
    if (!file) return;
    var formData = new FormData();
    formData.append('file', file);
    $.ajax({
        url: '/apis/upload_pic/',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if(res.status === 'ok' && res.path) {
                $('#edit_image_path').val(res.path);
                $('#edit_image_preview').attr('src', res.path).show();
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '图片上传失败', sticky: false, time: 3000 });
            }
        },
        error: function() {
            $.gritter.add({ title: '错误', text: '图片上传失败', sticky: false, time: 3000 });
        }
    });
});
// 清除图片按钮事件
$('#add_clear_image').on('click', function() {
    $('#add_image_preview').attr('src', '').hide();
    $('#add_image_file').val('');
    $('#add_image_path').val('');
    $('#add_image_removed').val('true');
});
$('#edit_clear_image').on('click', function() {
    $('#edit_image_preview').attr('src', '').hide();
    $('#edit_image_file').val('');
    $('#edit_image_path').val('');
    $('#edit_image_removed').val('true');
});

// 页面加载完成后执行
$(document).ready(function() {
    // 加载产品列表
    product_detail(1, 10);
    
    // 添加生成页面的模态框HTML到页面
    $('body').append(`
    <!-- 生成产品页面模态框 -->
    <div id="generatePageModal" class="modal hide">
        <div class="modal-header">
            <button data-dismiss="modal" class="close" type="button">×</button>
            <span style="font-size: 16px;font-weight:bold">生成产品详情页面</span>
        </div>
        <div class="modal-body">
            <div class="row-fluid">
                <div class="span12">
                    <form class="form-horizontal">
                        <div class="control-group">
                            <label class="control-label">中文产品类型</label>
                            <div class="controls">
                                <input type="text" class="span8" id="gen_product_type" />
                                <span class="help-block">例如：RK3568数据采集网关</span>
                            </div>
                        </div>
                        <div class="control-group">
                            <label class="control-label">英文产品类型</label>
                            <div class="controls">
                                <input type="text" class="span8" id="gen_product_type_en" />
                                <span class="help-block">例如：RK3568 DATA ACQUISITION GATEWAY</span>
                            </div>
                        </div>
                    </form>
                    <div id="gen_result" style="margin-top:10px;"></div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <a class="btn btn-primary" id="gen_btn" href="javascript:generate_product_page()">生成页面</a>
            <a data-dismiss="modal" class="btn" href="#">关闭</a>
        </div>
    </div>
    `);

    // 绑定清除图片按钮事件，确保无论用onclick还是jQuery都能生效
    $('#add_clear_image').on('click', window.clearAddImage);
    $('#edit_clear_image').on('click', window.clearEditImage);
});

