/**
 * 表格相关功能
 * 提供表格排序、分页、搜索等功能
 */

$(document).ready(function() {
    // 初始化DataTables
    if ($.fn.dataTable) {
        $('.data-table').dataTable({
            "bJQueryUI": true,
            "sPaginationType": "full_numbers",
            "sDom": '<""l>t<"F"fp>'
        });
    }
    
    // 表格行高亮
    $('tbody tr').hover(
        function() {
            $(this).addClass('highlight');
        },
        function() {
            $(this).removeClass('highlight');
        }
    );
    
    // 表格行选择
    $('.selectable-row').click(function() {
        $(this).toggleClass('selected');
    });
    
    // 表格排序
    $('.sortable').each(function() {
        var $table = $(this);
        
        $table.find('th').click(function() {
            var idx = $(this).index();
            
            if ($(this).hasClass('sorting-asc')) {
                $(this).removeClass('sorting-asc').addClass('sorting-desc');
                sortTable($table, idx, 'desc');
            } else {
                $table.find('th').removeClass('sorting-asc sorting-desc');
                $(this).addClass('sorting-asc');
                sortTable($table, idx, 'asc');
            }
        });
    });
});

/**
 * 简单的表格排序函数
 * @param {jQuery} $table - 表格jQuery对象
 * @param {number} idx - 要排序的列索引
 * @param {string} direction - 排序方向，'asc'或'desc'
 */
function sortTable($table, idx, direction) {
    var $rows = $table.find('tbody tr').get();
    
    $rows.sort(function(a, b) {
        var A = $(a).children('td').eq(idx).text().toUpperCase();
        var B = $(b).children('td').eq(idx).text().toUpperCase();
        
        if ($.isNumeric(A) && $.isNumeric(B)) {
            A = parseFloat(A);
            B = parseFloat(B);
        }
        
        return direction === 'asc' ? 
            (A < B ? -1 : (A > B ? 1 : 0)) : 
            (A > B ? -1 : (A < B ? 1 : 0));
    });
    
    $.each($rows, function(index, row) {
        $table.children('tbody').append(row);
    });
} 