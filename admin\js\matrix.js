
$(document).ready(function(){

	
	
	// === Sidebar navigation === //
	
	$('.submenu > a').click(function(e)
	{
		e.preventDefault();
		var submenu = $(this).siblings('ul');
		var li = $(this).parents('li');
		var submenus = $('#sidebar li.submenu ul');
		var submenus_parents = $('#sidebar li.submenu');
		if(li.hasClass('open'))
		{
			if(($(window).width() > 768) || ($(window).width() < 479)) {
				submenu.slideUp();
			} else {
				submenu.fadeOut(250);
			}
			li.removeClass('open');
		} else 
		{
			if(($(window).width() > 768) || ($(window).width() < 479)) {
				submenus.slideUp();			
				submenu.slideDown();
			} else {
				submenus.fadeOut(250);			
				submenu.fadeIn(250);
			}
			submenus_parents.removeClass('open');		
			li.addClass('open');	
		}
	});
	
	var ul = $('#sidebar > ul');
	
	$('#sidebar > a').click(function(e)
	{
		e.preventDefault();
		var sidebar = $('#sidebar');
		if(sidebar.hasClass('open'))
		{
			sidebar.removeClass('open');
			ul.slideUp(250);
		} else 
		{
			sidebar.addClass('open');
			ul.slideDown(250);
		}
	});
	
	// === Resize window related === //
	$(window).resize(function()
	{
		if($(window).width() > 479)
		{
			ul.css({'display':'block'});	
			$('#content-header .btn-group').css({width:'auto'});		
		}
		if($(window).width() < 479)
		{
			ul.css({'display':'none'});
			fix_position();
		}
		if($(window).width() > 768)
		{
			$('#user-nav > ul').css({width:'auto',margin:'0'});
            $('#content-header .btn-group').css({width:'auto'});
		}
	});
	
	if($(window).width() < 468)
	{
		ul.css({'display':'none'});
		fix_position();
	}
	
	if($(window).width() > 479)
	{
	   $('#content-header .btn-group').css({width:'auto'});
		ul.css({'display':'block'});
	}
	
	// === Tooltips === //
	$('.tip').tooltip();	
	$('.tip-left').tooltip({ placement: 'left' });	
	$('.tip-right').tooltip({ placement: 'right' });	
	$('.tip-top').tooltip({ placement: 'top' });	
	$('.tip-bottom').tooltip({ placement: 'bottom' });	
	
	// === Search input typeahead === //
	$('#search input[type=text]').typeahead({
		source: ['Dashboard','Form elements','Common Elements','Validation','Wizard','Buttons','Icons','Interface elements','Support','Calendar','Gallery','Reports','Charts','Graphs','Widgets'],
		items: 4
	});
	
	// === Fixes the position of buttons group in content header and top user navigation === //
	function fix_position()
	{
		var uwidth = $('#user-nav > ul').width();
		$('#user-nav > ul').css({width:uwidth,'margin-left':'-' + uwidth / 2 + 'px'});
        
        var cwidth = $('#content-header .btn-group').width();
        $('#content-header .btn-group').css({width:cwidth,'margin-left':'-' + uwidth / 2 + 'px'});
	}
	
	// === Style switcher === //
	$('#style-switcher i').click(function()
	{
		if($(this).hasClass('open'))
		{
			$(this).parent().animate({marginRight:'-=190'});
			$(this).removeClass('open');
		} else 
		{
			$(this).parent().animate({marginRight:'+=190'});
			$(this).addClass('open');
		}
		$(this).toggleClass('icon-arrow-left');
		$(this).toggleClass('icon-arrow-right');
	});
	
	$('#style-switcher a').click(function()
	{
		var style = $(this).attr('href').replace('#','');
		$('.skin-color').attr('href','css/maruti.'+style+'.css');
		$(this).siblings('a').css({'border-color':'transparent'});
		$(this).css({'border-color':'#aaaaaa'});
	});
	
	$('.lightbox_trigger').click(function(e) {
		
		e.preventDefault();
		
		var image_href = $(this).attr("href");
		
		if ($('#lightbox').length > 0) {
			
			$('#imgbox').html('<img src="' + image_href + '" /><p><i class="icon-remove icon-white"></i></p>');
		   	
			$('#lightbox').slideDown(500);
		}
		
		else { 
			var lightbox = 
			'<div id="lightbox" style="display:none;">' +
				'<div id="imgbox"><img src="' + image_href +'" />' + 
					'<p><i class="icon-remove icon-white"></i></p>' +
				'</div>' +	
			'</div>';
				
			$('body').append(lightbox);
			$('#lightbox').slideDown(500);
		}
		
	});
	

	$('#lightbox').live('click', function() { 
		$('#lightbox').hide(200);
	});


	// 侧边栏选中效果
	var now_html_name = (window.location.pathname).split('/').pop();

	$('#sidebar li a[href="' + now_html_name + '"]').parent().addClass('active').siblings().removeClass('active');
	$('#sidebar li a[href="' + now_html_name + '"]').parent().parent().parent().addClass('open').siblings().removeClass('open');

	if(now_html_name === 'product.html' || now_html_name === 'product_detail.html'){ // 这两个公用同一个侧边菜单栏，因此只能二存一
		// if (now_html_name === 'product.html') {
		// 	$('#sidebar li a[href="' + now_html_name + '"]').parent().show();
		// 	$('#sidebar li a[href="product_detail.html"]').parent().hide();
        // }else {
		// 	$('#sidebar li a[href="' + now_html_name + '"]').parent().show();
		// 	$('#sidebar li a[href="product.html"]').parent().hide();
		// }

		$('#sidebar li a[href="product.html"]').parent().addClass('active').siblings().removeClass('active');
		$('#sidebar li a[href="product.html"]').parent().parent().parent().addClass('open').siblings().removeClass('open');
		$('#sidebar li a[href="product.html"]').parent().parent().parent().addClass('active').siblings().removeClass('active');

		$('#sidebar li a[href="' + now_html_name + '"]').attr('href', 'product.html'); // 因为公用一个侧边栏菜单，所以要将当前的设置为dev.html，保证点击的时候能回到dev,
	}
	// $('#sidebar li a[href="' + now_html_name + '"]').parent().parent().parent().addClass('active').siblings().removeClass('active');
});


//401跳转回首页
jQuery(function ($) {
    // 备份jquery的ajax方法
    var _ajax = $.ajax;
    $.ajax = function (opt) {
        var _error = opt && opt.error || function (a, b, c) {
        };
        //修改传入对象的error函数
        var _opt = $.extend(opt, {
            error: function (xhr, textStatus, errorThrown) {
                //通过状态码判断401
                if (xhr.status === 401) {
                    console.log('您好，身份验证已过期，请重新登陆。');
                    //返回首页
                    top.location = 'login.html';
                    return;
                }
                if (xhr.status === 403) {
                    show_gitter('错误提示', '您没有操作权限', 2);
                    return;
                }
                if (xhr.status === 404) {
                    show_gitter('错误提示', '服务错误，请联系管理员', 2);
                    return;
                }
                if (xhr.status === 405) {
                    self.location = 'error405.html';
                    return;
                }
                if (xhr.status === 500) {
                    // self.location = 'error500.html';
                    // alert('服务错误，请联系管理员');
					show_gitter('错误提示', '服务错误，请联系管理员', 2);
                    return;
                }

                _error(xhr, textStatus, errorThrown);
            }
        });
        //注意这里需要返回_ajax(_opt)，否则在链式调用时会报错
        return _ajax(_opt);
    };
});


// 数据里面的数据普通字段进行处理，判断是否为空，空用‘--’代替
function Vorg_change(v_data) {

	var ret_data = v_data && v_data!=='' && toString(v_data)!=='null' ? v_data:'--';
	return ret_data
}
// 处理日期类型的数据
function Vdate_change(v_data) {
	var ret_data = v_data && v_data!=='' && toString(v_data)!=='null' ? v_data.replace(/T/g, ' '):'--';
	return ret_data;
}