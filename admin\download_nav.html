<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>资料下载导航栏管理</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="app-type" content="admin-panel" />
    <meta name="client-type" content="admin" />
    <link rel="stylesheet" href="css/bootstrap.min.css"/>
    <link rel="stylesheet" href="css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="css/colorpicker.css"/>
    <link rel="stylesheet" href="css/uniform.css"/>
    <link rel="stylesheet" href="css/fullcalendar.css"/>
    <link rel="stylesheet" href="css/matrix-style.css"/>
    <link rel="stylesheet" href="css/matrix-media.css"/>
    <link rel="stylesheet" href="css/bootstrap-wysihtml5.css"/>
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="css/jquery.gritter.css"/>
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.cookie.js"></script>
    <script src="js/jquery.i18n.js"></script>
    <script src="js/language.js"></script>
    <style>
        #wrapper { display: flex; min-height: 100vh; }
        #content { flex: 1; margin-left: 0 !important; border-left: 1px solid #ddd; background: #eee; }
        
        /* 修复模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: #000;
            opacity: 0.5;
        }
        
        /* 修复模态框居中问题 */
        .modal {
            width: 560px;
            margin-left: -280px;
            top: 50%;
            margin-top: -250px;
            z-index: 1050;
            position: fixed;
            background-color: #fff;
            border: 1px solid #999;
            border-radius: 6px;
            box-shadow: 0 3px 7px rgba(0,0,0,0.3);
        }
        
        body.modal-open {
            overflow: auto !important;
        }
        
        /* 确保通知显示在最上层 */
        #gritter-notice-wrapper {
            z-index: 9999999;
        }
        
        .gritter-item-wrapper {
            z-index: 9999999;
        }
        
        /* 图片预览样式 */
        .nav-image {
            max-height: 80px;
            max-width: 100%;
            margin-bottom: 10px;
        }
        
        .image-preview-container {
            margin-bottom: 10px;
        }
        
        .image-actions {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
<script src="js/head.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        if (sessionStorage.getItem('adminLoggedIn') !== 'true') {
            window.location.href = '/login/login.html';
        }
    });
</script>
<div id="wrapper">
    <div id="content">
        <div id="content-header">
            <div id="breadcrumb">
                <a href="admin_home.html" title="返回首页" class="tip-bottom"><i class="icon-home"></i>首页</a>
                <a href="javascript:void(0);" class="current" onclick="return false;">资料下载导航栏管理</a>
            </div>
        </div>
        <div class="container-fluid">
            <div class="widget-box">
                <div class="widget-content">
                    <div style="margin-bottom: 10px;">
                        <select id="lang_filter" class="form-control" style="width: 160px; display: inline-block; margin-right: 8px;">
                            <option value="">中/英文</option>
                            <option value="0">中文</option>
                            <option value="1">英文</option>
                        </select>
                        <select id="type_filter" class="form-control" style="width: 160px; display: inline-block; margin-right: 8px;">
                            <option value="">所有类型</option>
                            <option value="nav">导航</option>
                            <option value="product">产品</option>
                            <option value="web_image">网页大图</option>
                        </select>
                        <a class="btn btn-primary" href="#addNavAlert" data-toggle="modal"><i class="icon icon-plus"></i> 添加</a>
                        <a class="btn btn-danger" href="javascript:logout()" title="退出登录"><i class="icon icon-off">退出登录 </i></a>
                    </div>
                    <div class="widget-box" style="margin-top: 0;">
                        <div class="widget-content nopadding">
                            <table class="table table-bordered table-striped">
                                <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>语言</th>
                                    <th>类型</th>
                                    <th>标题</th>
                                    <th>内容</th>
                                    <th>图片</th>
                                    <th>显示顺序</th>
                                    <th>是否显示</th>
                                    <th>更新时间</th>
                                    <th>链接URL</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody id="data_list"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="fg-toolbar ui-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix">
                        <div id="page" class="dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_full_numbers"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 编辑模态框 -->
        <div id="editNavAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold">编辑导航项</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <form class="form-horizontal">
                            <input type="hidden" id="edit_id">
                            <div class="control-group">
                                <label class="control-label">类型</label>
                                <div class="controls">
                                    <select class="span8" id="edit_type">
                                        <option value="nav">导航</option>
                                        <option value="product">产品</option>
                                        <option value="web_image">网页大图</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">标题</label>
                                <div class="controls"><input type="text" class="span8" id="edit_title" /></div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">内容</label>
                                <div class="controls"><textarea class="span8" id="edit_content" rows="3"></textarea></div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">图片</label>
                                <div class="controls">
                                    <div class="image-preview-container">
                                        <img class="thumbnail nav-image" id="edit_image_preview" src="" style="display:none;max-width:120px;max-height:80px;border:1px solid #ccc;" />
                                    </div>
                                    <div class="image-actions">
                                        <input type="file" id="edit_image_file" accept="image/*" style="margin-bottom:8px;" />
                                        <button type="button" class="btn btn-small btn-warning" id="edit_clear_image">清除图片</button>
                                    </div>
                                    <input type="hidden" id="edit_image_path" />
                                    <input type="hidden" id="edit_image_removed" value="false">
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">显示顺序</label>
                                <div class="controls"><input type="number" class="span8" id="edit_display_order" min="0" value="100" /></div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">语言</label>
                                <div class="controls">
                                    <select class="span8" id="edit_lang">
                                        <option value="0">中文</option>
                                        <option value="1">英文</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">是否显示</label>
                                <div class="controls">
                                    <select class="span8" id="edit_show">
                                        <option value="True">显示</option>
                                        <option value="False">不显示</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接URL</label>
                                <div class="controls"><input type="text" class="span8" id="edit_url" /></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-primary" href="javascript:edit_nav()">保存</a>
                <a data-dismiss="modal" class="btn" href="#">取消</a>
            </div>
        </div>
        <!-- 添加模态框 -->
        <div id="addNavAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold">新增导航项</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <form class="form-horizontal">
                            <div class="control-group">
                                <label class="control-label">类型</label>
                                <div class="controls">
                                    <select class="span8" id="add_type">
                                        <option value="nav">导航</option>
                                        <option value="product">产品</option>
                                        <option value="web_image">网页大图</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">标题</label>
                                <div class="controls"><input type="text" class="span8" id="add_title" /></div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">内容</label>
                                <div class="controls"><textarea class="span8" id="add_content" rows="3"></textarea></div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">图片</label>
                                <div class="controls">
                                    <div class="image-preview-container">
                                        <img class="thumbnail nav-image" id="add_image_preview" src="" style="display:none;max-width:120px;max-height:80px;border:1px solid #ccc;" />
                                    </div>
                                    <div class="image-actions">
                                        <input type="file" id="add_image_file" accept="image/*" style="margin-bottom:8px;" />
                                        <button type="button" class="btn btn-small btn-warning" id="add_clear_image">清除图片</button>
                                    </div>
                                    <input type="hidden" id="add_image_path" />
                                    <input type="hidden" id="add_image_removed" value="false">
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">显示顺序</label>
                                <div class="controls"><input type="number" class="span8" id="add_display_order" min="0" value="100" /></div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">语言</label>
                                <div class="controls">
                                    <select class="span8" id="add_lang">
                                        <option value="0">中文</option>
                                        <option value="1">英文</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">是否显示</label>
                                <div class="controls">
                                    <select class="span8" id="add_show">
                                        <option value="True">显示</option>
                                        <option value="False">不显示</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接URL</label>
                                <div class="controls"><input type="text" class="span8" id="add_url" /></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-primary" href="javascript:add_nav()">保存</a>
                <a data-dismiss="modal" class="btn" href="#">取消</a>
            </div>
        </div>
        <!-- 删除确认模态框 -->
        <div id="deleteNavAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 18px;font-weight:bold">导航项删除确认</span>
            </div>
            <div class="modal-body">
                <p style="font-size: 18px">
                    <i class="icon icon-warning-sign " style="font-size: 30px;color: red"></i>
                    <span style="margin-left: 20px">是否确认删除ID为</span>
                    <span id="del_id_span" style="color: #1d00ff;font-weight: bold;font-size: 18px;"></span>
                    <span >的导航项？</span>
                    <hr/>
                    <div>
                        <p><strong>标题：</strong><span id="del_title"></span></p>
                        <p><strong>内容：</strong><span id="del_content"></span></p>
                        <p id="del_image_container" style="display: none;"><strong>图片：</strong></p>
                        <img id='del_image' class="thumbnail nav-image" src="" style="display: none;">
                    </div>
                    <input id="del_id" style="display: none">
                </p>
            </div>
            <div class="modal-footer">
                <a class="btn btn-danger" href="javascript:del_nav()">删除</a>
                <a data-dismiss="modal" class="btn" href="#">取消</a>
            </div>
        </div>
    </div>
</div>
<script src="js/footer.js"></script>
<script src="js/excanvas.min.js"></script>
<script src="js/jquery.ui.custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.peity.min.js"></script>
<script src="js/fullcalendar.min.js"></script>
<script src="js/matrix.js"></script>
<script src="js/jquery.gritter.min.js"></script>
<script src="js/matrix.interface.js"></script>
<script src="js/matrix.chat.js"></script>
<script src="js/jquery.validate.js"></script>
<script src="js/matrix.form_validation.js"></script>
<script src="js/jquery.wizard.js"></script>
<script src="js/jquery.uniform.js"></script>
<script src="js/matrix.popover.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/page.js"></script>
<script src="js/download_nav.js"></script>

<!-- 添加额外的修复脚本 -->
<script>
    // 修复模态框问题
    $(document).ready(function() {
        // 初始化时清理可能存在的模态框背景
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        
        // 修复Bootstrap 2.x模态框问题
        if (typeof $.fn.modal !== 'undefined') {
            // 保存原始modal方法
            var originalModal = $.fn.modal;
            
            // 重写modal方法
            $.fn.modal = function(option) {
                console.log("调用模态框方法:", option);
                
                if (option === 'hide') {
                    // 隐藏模态框时清理背景
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // 调用原始方法
                    return originalModal.apply(this, arguments);
                }
                else if (option === 'show' || typeof option === 'object') {
                    // 显示模态框前清理背景
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // 调用原始方法
                    return originalModal.apply(this, arguments);
                }
                
                // 其他情况调用原始方法
                return originalModal.apply(this, arguments);
            };
        }
        
        // 监听模态框关闭事件，确保背景被移除
        $(document).on('hidden', '.modal', function() {
            console.log("模态框关闭，清理背景");
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        // 监听点击事件，修复灰色遮罩无法点击的问题
        $(document).on('click', '.modal-backdrop', function() {
            console.log("点击了模态框背景，关闭所有模态框");
            $('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        // 监听ESC键，确保模态框可以关闭
        $(document).keyup(function(e) {
            if (e.keyCode === 27) { // ESC键
                console.log("按下ESC键，关闭所有模态框");
                $('.modal').modal('hide');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        });
        
        // 初始化数据列表
        setTimeout(function() {
            console.log("页面加载完成，正在加载数据列表...");
            if (typeof nav_list === 'function') {
                nav_list(1, 20);
                console.log("数据列表加载成功");
            }
        }, 1000);
        
        // 监听文件上传控件变化，预览图片
        $('#add_image_file').on('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#add_image_preview').attr('src', e.target.result).show();
                }
                reader.readAsDataURL(e.target.files[0]);
            }
        });
        
        $('#edit_image_file').on('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#edit_image_preview').attr('src', e.target.result).show();
                }
                reader.readAsDataURL(e.target.files[0]);
            }
        });

        // 清除图片按钮事件
        $('#add_clear_image').on('click', function() {
            $('#add_image_preview').attr('src', '').hide();
            $('#add_image_file').val('');
            $('#add_image_removed').val('true');
        });

        $('#edit_clear_image').on('click', function() {
            $('#edit_image_preview').attr('src', '').hide();
            $('#edit_image_file').val('');
            $('#edit_image_removed').val('true');
        });
    });

    // 页面加载后自动高亮菜单
    $(function () {
        reset_menu();
    });
    
    // 监听筛选下拉框变化，筛选数据
    $(function() {
        $('#lang_filter, #type_filter').on('change', function() {
            var lang = $('#lang_filter').val();
            var type = $('#type_filter').val();
            if (typeof nav_list === 'function') {
                nav_list(1, 20, lang, type);
            }
        });
    });
    
    // 添加模态框显示函数
    function add_nav_modal_show() {
        $('#addNavAlert').modal('show');
    }
</script>
</body>
</html> 