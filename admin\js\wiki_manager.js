/**
 * wiki_manager.js - 维基教程页面管理脚本
 * 
 * 该脚本用于管理维基教程页面的内容，包括大图和详情内容
 * 与company_info.js配合使用，扩展了公司信息管理功能
 */

// 页面加载完成后执行
$(document).ready(function() {
    // 初始化wiki相关功能
    initWikiManager();
});

/**
 * 初始化Wiki管理器
 */
function initWikiManager() {
    console.log("初始化Wiki管理器...");
    
    // 监听筛选器变化，当选择wiki相关类型时提供额外的帮助信息
    $('#info_type_filter').on('change', function() {
        var selectedType = $(this).val();
        if (selectedType === 'wiki_banner' || selectedType === 'wiki_detail') {
            showWikiHelp(selectedType);
        } else {
            hideWikiHelp();
        }
    });
    
    // 为添加和编辑表单添加wiki特定的字段验证
    enhanceWikiFormValidation();
}

/**
 * 显示Wiki帮助信息
 * @param {string} type - wiki内容类型
 */
function showWikiHelp(type) {
    // 移除可能存在的旧提示
    $('.wiki-help-box').remove();
    
    var helpText = "";
    if (type === 'wiki_banner') {
        helpText = "<div class='alert alert-info wiki-help-box'>" +
            "<button class='close' data-dismiss='alert'>×</button>" +
            "<strong>维基教程-大图说明：</strong><br>" +
            "- 此类型用于设置维基教程页面顶部的大图<br>" +
            "- 建议图片尺寸：1920x300像素<br>" +
            "- 每个语言版本只需要一张大图<br>" +
            "</div>";
    } else if (type === 'wiki_detail') {
        helpText = "<div class='alert alert-info wiki-help-box'>" +
            "<button class='close' data-dismiss='alert'>×</button>" +
            "<strong>维基教程-详情说明：</strong><br>" +
            "- 此类型用于添加维基教程的具体内容项<br>" +
            "- 标题：教程的名称（可选）<br>" +
            "- 内容：教程的简短描述（可选）<br>" +
            "- 图片：教程的缩略图（可选，建议尺寸：300x200像素）<br>" +
            "- 链接：点击后跳转的详情页面地址（可选）<br>" +
            "- 显示顺序：决定教程项在页面上的排列顺序<br>" +
            "</div>";
    }
    
    // 添加帮助信息到筛选器下方
    if (helpText) {
        $('#info_type_filter').parent().append(helpText);
    }
}

/**
 * 隐藏Wiki帮助信息
 */
function hideWikiHelp() {
    $('.wiki-help-box').remove();
}

/**
 * 增强Wiki表单验证
 */
function enhanceWikiFormValidation() {
    // 添加表单提交前的验证
    var originalAddInfo = window.add_info;
    window.add_info = function() {
        if ($('#add_info_type').val() === 'wiki_detail') {
            // 对于wiki详情，所有字段都可以为空，不做验证
            // 移除所有必填验证
        } else if ($('#add_info_type').val() === 'wiki_banner') {
            // 对于wiki大图，确保有图片
            if (!$('#add_image').attr('src') && !$('#add_new_image').val()) {
                $.gritter.add({title: '错误', text: '维基教程大图不能为空', sticky: false, time: 3000});
                return;
            }
        }
        
        // 调用原始添加方法
        originalAddInfo();
    };
    
    // 同样处理编辑表单
    var originalEditInfo = window.edit_info;
    window.edit_info = function() {
        if ($('#edit_info_type').val() === 'wiki_detail') {
            // 对于wiki详情，所有字段都可以为空，不做验证
            // 移除所有必填验证
        } else if ($('#edit_info_type').val() === 'wiki_banner') {
            // 对于wiki大图，确保有图片
            if (!$('#edit_image').attr('src') && $('#edit_image_removed').val() === 'true' && !$('#edit_new_image').val()) {
                $.gritter.add({title: '错误', text: '维基教程大图不能为空', sticky: false, time: 3000});
                return;
            }
        }
        
        // 调用原始编辑方法
        originalEditInfo();
    };
}

/**
 * 格式化Wiki数据显示
 * @param {Object} data - Wiki数据对象
 * @return {string} 格式化后的HTML
 */
function formatWikiPreview(data) {
    var html = '';
    
    if (data.info_type === 'wiki_banner') {
        // 大图预览
        if (data.image_path) {
            html += '<div><strong>大图预览：</strong></div>';
            html += '<img src="' + data.image_path + '" style="max-width:100%; max-height:100px;">';
        }
    } else if (data.info_type === 'wiki_detail') {
        // 详情预览
        html += '<div class="preview-container">';
        
        // 图片列
        html += '<div class="preview-column image-column">';
        if (data.image_path) {
            html += '<div class="preview-image"><img src="' + data.image_path + '"></div>';
        } else {
            html += '<div class="preview-empty">无图片</div>';
        }
        html += '</div>';
        
        // 文本列
        html += '<div class="preview-column text-column">';
        if (data.content) {
            html += '<div class="preview-text">' + data.content + '</div>';
        } else {
            html += '<div class="preview-empty">无描述</div>';
        }
        html += '</div>';
        
        html += '</div>';
        
        // 链接信息
        if (data.url) {
            html += '<div style="margin-top:5px;"><small>链接: ' + data.url + '</small></div>';
        }
    }
    
    return html;
}

/**
 * 从后台获取Wiki数据并更新前端页面
 * 此函数可以在需要时被调用，例如在公司信息管理页面加载完成后
 */
function updateWikiContent() {
    console.log("正在更新Wiki内容...");
    
    // 获取wiki相关的公司信息数据
    $.ajax({
        url: '/api/company_info',
        type: 'GET',
        data: {
            info_type: ['wiki_banner', 'wiki_detail'],
            is_show: true
        },
        success: function(response) {
            if (response.code === 0 && response.data) {
                processWikiData(response.data);
            } else {
                console.error("获取Wiki数据失败:", response.message || "未知错误");
            }
        },
        error: function(xhr, status, error) {
            console.error("获取Wiki数据请求失败:", error);
        }
    });
}

/**
 * 处理Wiki数据并更新前端页面
 * @param {Array} data - Wiki数据数组
 */
function processWikiData(data) {
    console.log("处理Wiki数据:", data);
    
    // 分离大图和详情数据
    var bannerData = data.filter(item => item.info_type === 'wiki_banner');
    var detailData = data.filter(item => item.info_type === 'wiki_detail')
                         .sort((a, b) => a.display_order - b.display_order);
    
    // 根据语言版本筛选数据
    var zhBanner = bannerData.filter(item => item.lang === 0)[0];
    var enBanner = bannerData.filter(item => item.lang === 1)[0];
    var zhDetails = detailData.filter(item => item.lang === 0);
    var enDetails = detailData.filter(item => item.lang === 1);
    
    // 更新前端页面
    updateWikiFrontend(zhBanner, enBanner, zhDetails, enDetails);
}

/**
 * 更新Wiki前端页面
 * @param {Object} zhBanner - 中文版大图数据
 * @param {Object} enBanner - 英文版大图数据
 * @param {Array} zhDetails - 中文版详情数据数组
 * @param {Array} enDetails - 英文版详情数据数组
 */
function updateWikiFrontend(zhBanner, enBanner, zhDetails, enDetails) {
    console.log("更新Wiki前端页面");
    
    // 生成Wiki配置JSON
    var wikiConfig = {
        all: []
    };
    
    // 如果有中文详情数据，添加到配置中
    if (zhDetails && zhDetails.length > 0) {
        var zhWikis = zhDetails.map(item => ({
            name: item.title || '',
            desc: item.content || '',
            img: item.image_path || '',
            link: item.url || ''
        }));
        
        wikiConfig.all.push({
            class: "中文教程",
            wikis: zhWikis
        });
    }
    
    // 如果有英文详情数据，添加到配置中
    if (enDetails && enDetails.length > 0) {
        var enWikis = enDetails.map(item => ({
            name: item.title || '',
            desc: item.content || '',
            img: item.image_path || '',
            link: item.url || ''
        }));
        
        wikiConfig.all.push({
            class: "English Tutorials",
            wikis: enWikis
        });
    }
    
    // 将配置保存到本地文件
    saveWikiConfig(wikiConfig);
    
    // 更新大图
    updateWikiBanner(zhBanner, enBanner);
}

/**
 * 保存Wiki配置到本地文件
 * @param {Object} config - Wiki配置对象
 */
function saveWikiConfig(config) {
    console.log("保存Wiki配置:", config);
    
    // 将配置转换为JSON字符串
    var configJson = JSON.stringify(config, null, 2);
    
    // 发送到后端保存
    $.ajax({
        url: '/api/save_wiki_config',
        type: 'POST',
        contentType: 'application/json',
        data: configJson,
        success: function(response) {
            if (response.code === 0) {
                console.log("Wiki配置保存成功");
                $.gritter.add({
                    title: '成功',
                    text: 'Wiki配置已更新，前端页面将在刷新后显示新内容',
                    sticky: false,
                    time: 3000
                });
            } else {
                console.error("Wiki配置保存失败:", response.message || "未知错误");
                $.gritter.add({
                    title: '错误',
                    text: 'Wiki配置保存失败: ' + (response.message || "未知错误"),
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function(xhr, status, error) {
            console.error("Wiki配置保存请求失败:", error);
            $.gritter.add({
                title: '错误',
                text: 'Wiki配置保存请求失败: ' + error,
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 更新Wiki大图
 * @param {Object} zhBanner - 中文版大图数据
 * @param {Object} enBanner - 英文版大图数据
 */
function updateWikiBanner(zhBanner, enBanner) {
    console.log("更新Wiki大图");
    
    // 如果有中文大图，更新中文页面
    if (zhBanner && zhBanner.image_path) {
        // 在实际应用中，这里可能需要更新前端页面中的大图元素
        console.log("中文Wiki大图路径:", zhBanner.image_path);
    }
    
    // 如果有英文大图，更新英文页面
    if (enBanner && enBanner.image_path) {
        // 在实际应用中，这里可能需要更新前端页面中的大图元素
        console.log("英文Wiki大图路径:", enBanner.image_path);
    }
}

// 导出函数供外部调用
window.wikiManager = {
    init: initWikiManager,
    formatPreview: formatWikiPreview,
    updateContent: updateWikiContent
}; 