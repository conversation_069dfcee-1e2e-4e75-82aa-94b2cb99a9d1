
.fc {
    direction: ltr;
    text-align: left; border:1px solid #dadada;
}
.fc table {
    border-collapse: collapse;
    border-spacing: 0;
}
html .fc, .fc table {
    font-size: 1em;
}
.fc td, .fc th {
    padding: 0;
    vertical-align: top;
}
.fc-header {
    position: relative;
}
.fc-header td {
    position: relative;
    white-space: nowrap;
}
.fc-header-left {
    text-align: left;
}
.fc-header-center {
    left: 10%;
    position: absolute;
    text-align: center;
    top: 0;
    width: 80%;
}
.fc-button-inner {
    border-left: 1px solid #D5D5D5;
}
.panel-left .fc-header-right .fc-button:last-child .fc-button-inner {
	border-right: 1px solid #D5D5D5;
}
.fc-header-left .fc-button-inner {
    border: medium none;
}
.fc-header-right {
    position: absolute !important;
    right: 0;
    text-align: right;
    top: -37px;
}
.panel-left .fc-header-right {
	right: -1px;
}
.fc-header-title {
    display: inline-block;
    vertical-align: top;
}
.fc-header-title strong {
    display: block;
    margin-top: 0;
    padding: 8px 12px !important;
    white-space: nowrap;
}
.fc .fc-header-space {
    padding-left: 10px;
}
.fc-header .fc-corner-right {
    float: right;
    margin-right: 1px;
}
.fc-header .ui-corner-right {
    margin-right: 0;
}
.fc-header .fc-state-hover, .fc-header .ui-state-hover {
    z-index: 2;
}
.fc-header .fc-state-down {
    z-index: 3;
}
.fc-header .fc-state-active, .fc-header .ui-state-active {
    z-index: 4;
}
.fc-button-prev .fc-button-content {
    background: url("../img/larrow.png") no-repeat scroll 15px 13px transparent;
    width: 10px;
}
.fc-button-next .fc-button-content {
    background: url("../img/rarrow.png") no-repeat scroll 15px 13px transparent;
    width: 10px;
}
.fc-content {
}
.fc-view {
    overflow: hidden;
    width: 100%;
}
.fc-widget-header, .fc-widget-content {
    border: 1px solid #D5D5D5;
}
.fc-state-highlight {
    background: url("../images/backgrounds/calActiveBg.html") repeat-x scroll 0 0 #F5F5F5;
}
.fc-cell-overlay {
    background: none repeat scroll 0 0 #99CCFF;
    opacity: 0.2;
}
.fc-button {
    cursor: pointer;
    display: inline-block;
    position: relative;
}
.fc-state-default {
}
.fc-button-inner {
    float: left;
    overflow: hidden;
    position: relative;
}
.fc-state-default .fc-button-inner {
}
.fc-button-content {
    float: left;
    height: 36px;
    line-height: 37px;
    padding: 0 14px;
    position: relative;
    white-space: nowrap;
}
.fc-header-right .fc-button-content {
    height: 37px;
}
.fc-button-content .fc-icon-wrap {
    float: left;
    position: relative;
    top: 50%;
}
.fc-button-content .ui-icon {
    float: left;
    margin-top: -50%;
    position: relative;
}
.fc-state-default .fc-button-effect {
    left: 0;
    position: absolute;
    top: 50%;
}
.fc-state-default .fc-button-effect span {
}
.fc-state-default, .fc-state-default .fc-button-inner {
}
.fc-state-hover, .fc-state-hover .fc-button-inner {
}
.fc-state-down, .fc-state-down .fc-button-inner {
}
.fc-state-active, .fc-state-active .fc-button-inner {
    background: none repeat scroll 0 0 #F9F9F9;
    color: #797979;
}
.fc-first th {
    padding-top: 1px;
}
.fc-state-disabled, .fc-state-disabled .fc-button-inner {
    border-color: #DDDDDD;
    color: #999999;
}
.fc-state-disabled {
    cursor: default;
}
.fc-state-disabled .fc-button-effect {
    display: none;
}
.fc-event {
    border-style: solid;
    border-width: 0;
    cursor: default;
    font-size: 0.85em;
}
a.fc-event, .fc-event-draggable {
    cursor: pointer;
}
a.fc-event {
    text-decoration: none;
}
.fc-rtl .fc-event {
    text-align: right;
}
.fc-event-skin {
    background-color: #fb7a2c;
    border-color: #000000;
    border-radius: 2px 2px 2px 2px;
    color: #FFFFFF;
    display: block;
    font-size: 11px;
    margin-top: 1px;
    padding: 1px 0;
}
.fc-event-inner {
    border-style: solid;
    border-width: 0;
    height: 100%;
    overflow: hidden;
    position: relative;
    width: 100%;
}
.fc-event-time, .fc-event-title {
    display: block;
    float: left;
    line-height: 16px;
    padding: 0 2px 1px 5px;
}
.fc .ui-resizable-handle {
    display: block;
    font-size: 300%;
    line-height: 50%;
    overflow: hidden;
    position: absolute;
    z-index: 99999;
}
.fc-event-hori {
    margin-bottom: 1px;
}
.fc-event-hori .ui-resizable-e {
    cursor: e-resize;
    height: 100% !important;
    right: -3px !important;
    top: 0 !important;
    width: 7px !important;
}
.fc-event-hori .ui-resizable-w {
    cursor: w-resize;
    height: 100% !important;
    left: -3px !important;
    top: 0 !important;
    width: 7px !important;
}
.fc-event-hori .ui-resizable-handle {
}
.fc-corner-left {
    margin-left: 1px;
}
.fc-corner-left .fc-button-inner, .fc-corner-left .fc-event-inner {
}
.fc-corner-right {
    margin-right: 1px;
}
.fc-corner-right .fc-button-inner, .fc-corner-right .fc-event-inner {
    margin-right: -1px;
}
.fc-corner-top {
    margin-top: 1px;
}
.fc-corner-top .fc-event-inner {
    margin-top: -1px;
}
.fc-corner-bottom {
    margin-bottom: 1px;
}
.fc-corner-bottom .fc-event-inner {
    margin-bottom: -1px;
}
.fc-corner-left .fc-event-inner {
}
.fc-corner-right .fc-event-inner {
}
.fc-corner-top .fc-event-inner {
    border-top-width: 1px;
}
.fc-corner-bottom .fc-event-inner {
    border-bottom-width: 1px;
}
table.fc-border-separate {
    border-collapse: separate;
}
.fc-border-separate th, .fc-border-separate td {
    border-width: 1px 0 0 1px;
}
.fc-border-separate td:first-child, .fc-border-separate th:first-child {
    border-left: medium none;
}
.fc-border-separate th.fc-last, .fc-border-separate td.fc-last {
}
.fc-border-separate tr.fc-last th, .fc-border-separate tr.fc-last td {
    border-top-width: 1px;
}
.fc-border-separate tbody tr.fc-first td, .fc-border-separate tbody tr.fc-first th {
    border-top-width: 1px;
}
.fc-grid th {
    text-align: center;
}
.fc-grid .fc-day-number {
    float: right;
    padding: 3px 5px;
}
.fc-grid .fc-other-month .fc-day-number {
    opacity: 0.3;
}
.fc-grid .fc-day-content {
    clear: both;
    padding: 5px 2px 3px;
}
.fc-grid .fc-event-time {
    font-weight: bold;
}
.fc-rtl .fc-grid .fc-day-number {
    float: left;
}
.fc-rtl .fc-grid .fc-event-time {
    float: right;
}
.fc-agenda table {
    border-collapse: separate;
}
.fc-agenda-days th {
    text-align: center;
}
.fc-agenda .fc-agenda-axis {
    font-weight: normal;
    padding: 0 4px;
    text-align: right;
    vertical-align: middle;
    white-space: nowrap;
    width: 50px;
}
.fc-agenda .fc-day-content {
    padding: 2px 2px 1px;
}
.fc-agenda-days .fc-agenda-axis {
    border-right-width: 1px;
}
.fc-agenda-days .fc-col0 {
    border-left-width: 0;
}
.fc-agenda-allday th {
    border-width: 0 1px;
}
.fc-agenda-allday .fc-day-content {
    min-height: 34px;
}
.fc-agenda-divider-inner {
    height: 2px;
    overflow: hidden;
}
.fc-widget-header .fc-agenda-divider-inner {
    background: none repeat scroll 0 0 #EEEEEE;
}
.fc-agenda-slots th {
    border-width: 1px 1px 0;
}
.fc-agenda-slots td {
    background: none repeat scroll 0 0 transparent;
    border-width: 1px 0 0;
}
.fc-agenda-slots td div {
    height: 20px;
}
.fc-agenda-slots tr.fc-slot0 th, .fc-agenda-slots tr.fc-slot0 td {
    border-top-width: 0;
}
.fc-agenda-slots tr.fc-minor th, .fc-agenda-slots tr.fc-minor td {
    border-top-style: dotted;
}
.fc-agenda-slots tr.fc-minor th.ui-widget-header {
}
.fc-event-vert {
    border-width: 0 1px;
}
.fc-event-vert .fc-event-head, .fc-event-vert .fc-event-content {
    overflow: hidden;
    position: relative;
    width: 100%;
    z-index: 2;
}
.fc-event-vert .fc-event-time {
    font-size: 10px;
    white-space: nowrap;
}
.fc-event-vert .fc-event-bg {
    background: none repeat scroll 0 0 #FFFFFF;
    height: 100%;
    left: 0;
    opacity: 0.3;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1;
}
.fc .ui-draggable-dragging .fc-event-bg, .fc-select-helper .fc-event-bg {
}
.fc-event-vert .ui-resizable-s {
    bottom: 0 !important;
    cursor: s-resize;
    font-family: monospace;
    font-size: 11px !important;
    height: 8px !important;
    line-height: 8px !important;
    overflow: hidden !important;
    text-align: center;
    width: 100% !important;
}
.fc-agenda .ui-resizable-resizing {
}

#external-events .external-event {
	margin-bottom: 5px; cursor:all-scroll;
}
