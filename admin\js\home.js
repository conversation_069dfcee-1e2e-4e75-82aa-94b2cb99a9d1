var current_page = 1;
function home_pic_lst(page, page_size, lang) {
    if(!page) page = 1;
    if(!page_size) page_size = 15;
    var filters = {};
    if (typeof lang === 'undefined') {
        // 兼容老调用方式，尝试从下拉框获取
        lang = $('#lang_filter').val() || '';
    }
    if (lang !== '' && lang !== undefined && lang !== null) {
        filters['lang'] = Number(lang); // 强制转为数字
    }
    current_page = page;

    // 在请求前清理可能存在的模态框背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');

    console.log("正在请求数据接口: /apis/get_home_pic/，筛选条件:", {
        page: page,
        page_size: page_size,
        lang: lang,
        filters: filters
    });
    
    $.ajax({
        type: "post",
        url: "/apis/get_home_pic/",
        async: false,
        // 添加自定义头，表明这是管理后台请求，不要过滤show=0的记录
        headers: {
            'X-Client-Type': 'admin',
            'X-Admin-Request': 'true'
        },
        data: {page: page, page_size:page_size, order_by:JSON.stringify(['lang', '-show','-pic_idx','-id']),filters:JSON.stringify(filters)},
        success: function (data) {
            console.log("数据接口返回:", data);
            if (data.status === 'ok') {
                var data_list = data.data_list;
                var tb_html = '';
                // 构造列表

                // 确保i18n.map存在
                if (typeof $.i18n === 'undefined' || !$.i18n.map) {
                    $.i18n = $.i18n || {};
                    $.i18n.map = $.i18n.map || {
                        'common.show': '显示',
                        'common.unshow': '不显示'
                    };
                }

                var date_str = Date.parse(new Date());

                $.each(data_list, function (_, item_data) {
                    // 修复显示状态判断，兼容数字和布尔值
                    var isShow = item_data.show === true || item_data.show === 1 || item_data.show === "1";
                    var show_str = isShow ? 
                        '<span class="badge badge-success i18n toggle-show-status" data-id="' + item_data.id + '" data-show="1" style="cursor:pointer" title="点击切换显示状态" name="common.show">' + ($.i18n.map['common.show'] || '显示') + '</span>' : 
                        '<span class="badge badge-important i18n toggle-show-status" data-id="' + item_data.id + '" data-show="0" style="cursor:pointer" title="点击切换显示状态" name="common.unshow">' + ($.i18n.map['common.unshow'] || '不显示') + '</span>';
                    
                    // 在控制台输出每条记录的显示状态，便于调试
                    console.log('记录ID=' + item_data.id + ', show值=' + item_data.show + ', 类型=' + typeof item_data.show + ', 显示状态=' + isShow + ', 语言=' + item_data.lang);
                    
                    // 处理最后操作时间
                    var lastOperationTime = '未记录';
                    if (item_data.update_time) {
                        // 如果有更新时间，优先显示更新时间
                        lastOperationTime = formatDateTime(item_data.update_time);
                    } else if (item_data.create_time) {
                        // 否则显示创建时间
                        lastOperationTime = formatDateTime(item_data.create_time);
                    } else {
                        // 尝试从localStorage中获取操作记录
                        var record = getOperationRecord(item_data.id);
                        if (record && record.timeStr) {
                            lastOperationTime = record.timeStr;
                        }
                    }
                    
                    var stritem = JSON.stringify(item_data);
                    if(item_data.lang ===0){
                        var lan_str = '<span class="label label-success">中文</span>';
                        var tr_bg_color = '#d2d2f7';
                    }else{
                        var lan_str = '<span class="label label-info">英文</span>';
                        var tr_bg_color = 'none';
                    }
                    tb_html += '<tr>\n' +
                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ item_data.id +' </td>\n' +
                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ lan_str +' </td>\n' +

                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">\n' +
                                '       <img class="thumbnail" style="max-height: 60px;display: inline" src="'+ item_data.pic  +'">\n' +
                                '  </td>\n' +
                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ (item_data.title || '') +' </td>\n' +
                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ (item_data.description ? item_data.description.substring(0, 30) + (item_data.description.length > 30 ? '...' : '') : '') +' </td>\n' +
                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ item_data.url +' </td>\n' +
                                '  <td style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ show_str +'</td>\n' +
                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ item_data.pic_idx +' </td>\n' +
                                '  <td  style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">'+ lastOperationTime +' </td>\n' +

                        '           <td class="taskOptions span4" style="text-align: center; vertical-align: middle;background:'+ tr_bg_color +' ">\n' +
                        '               <a href=\'javascript:init_edit('+ stritem +')\' class="btn btn-primary btn-mini"><i class="icon icon-pencil ">  编辑</i></a>\n' +
                        '               <a href=\'javascript:init_del('+ stritem +')\' class="btn btn-danger btn-mini"><i class="icon icon-trash " > 删除</i></a>\n' +
                        '           </td>\n' +
                        '        </tr>';
                });

                if (data_list.length === 0) {
                    // 如果没有数据，显示提示信息
                    tb_html = '<tr><td colspan="8" class="text-center">暂无数据</td></tr>';
                }

                $('#data_list').html(tb_html);
                 // 分页
                $("#page").paging({
                    pageNo: data.page, // 当前页码
                    totalPage: data.total_pages, // 总页数
                    totalSize: data.total_data, // 数据总数
                    callback: function (num) {
                        // 保持语言筛选状态
                        home_pic_lst(num, page_size, lang);
                    }
                })


            } else {
                console.error("数据接口返回错误:", data);
                if(i18nLanguage==='en_US'){
                    var err_msg  = data.msg ? data.msg : 'Internal service error, please contact us';
                    show_gitter('Error', err_msg, 2);
                }else{
                    var err_msg = data.msg ? data.msg : '内部服务错误';
                    show_gitter('错误提示', err_msg, 2);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error("数据接口请求失败:", status, error);
            show_gitter('错误提示', '数据接口请求失败，请检查网络连接', 2);
        }
    });
}

function check_is_num(str) {
    if(!/^\+?[1-9][0-9]*$/.test(str)){
        return false;
    }
    return true;

}

function add_one() {
    console.log("执行添加操作");
    var show_idx = $('#add_show_idx').val().trim();
    var show = $('#add_show').val().trim();
    var pic = $('#add_pic').attr('src').trim();
    var hurl = $('#hurl').val().trim();
    var lang = $('#add_lang').val().trim();
    var title = $('#add_title').val().trim();
    var description = $('#add_description').val().trim();

    if(!check_is_num(show_idx)){
        show_gitter('错误提示', '排序为整数', 2);
        return
    }
    
    // 确保show_idx是数字类型
    show_idx = parseInt(show_idx, 10);
    
    var insert_data = {
        pic_idx: show_idx,
        show: show,
        pic: pic,
        url: hurl,
        lang: lang,
        title: title,
        description: description,
        create_time: new Date().getTime() // 添加创建时间
    }
    
    console.log("添加数据:", insert_data);

    $.ajax({
        type: "post",
        url: "/apis/create_home_pic/",
        async: false,
        data: insert_data,
        success: function (data) {
            console.log("添加操作返回:", data);
            if (data.status === 'ok') {
                // 先移除焦点
                $('#addAlert a, #addAlert button, #addAlert input, #addAlert select').blur();
                
                // 再隐藏模态框
                $('#addAlert').modal('hide');
                
                // 清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                // 刷新列表
                home_pic_lst();
                
                if(i18nLanguage==='en_US') {
                    show_gitter('Success', 'Image added successfully!', 1);
                } else {
                    show_gitter('成功', '新增首页图片成功', 1);
                }
                
                // 刷新前端页面缓存
                refresh_frontend_cache();
            } else {
                console.error("添加操作失败:", data);
                if(i18nLanguage==='en_US'){
                    var err_msg = data.msg ? data.msg : 'Internal service error, please contact us';
                    show_gitter('Error', err_msg, 2);
                }else{
                    var err_msg  = data.msg ? data.msg : '内部服务错误';
                    show_gitter('错误提示', err_msg, 2);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error("添加操作请求失败:", status, error);
            show_gitter('错误提示', '添加操作请求失败，请检查网络连接', 2);
            
            // 清理背景
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }
    });
}



// 打开编辑框，修改编辑框内容
function init_edit(e) {
    console.log("打开编辑框，数据:", e);
    
    // 先清理可能存在的模态框背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    try {
        var date_str = Date.parse(new Date());
        $('#edit_pic').attr('src', e.pic );
        
        // 确保pic_idx是整数
        var picIdx = parseInt(e.pic_idx, 10);
        if (isNaN(picIdx)) {
            picIdx = 0;
        }
        $('#edit_px').val(picIdx);
        
        $('#edit_url').val(e.url);
        $('#edit_pid').val(e.id);
        $('#edit_title').val(e.title || '');
        $('#edit_description').val(e.description || '');

        $('#edit_new_pic').parent().find('.filename').html('未选择照片');
        $('#edit_new_pic').val('');

        // 重置所有选项的selected属性
        $("#edit_show option").removeAttr("selected");
        $("#edit_lang option").removeAttr("selected");
        
        // 设置显示状态选项（兼容多种类型的show值）
        var isShow = (e.show === true || e.show === 1 || e.show === "1");
        console.log("显示状态:", e.show, "类型:", typeof e.show, "转换后:", isShow);
        
        if (isShow) {
            $("#edit_show option[value='True']").prop("selected", true);
        } else {
            $("#edit_show option[value='False']").prop("selected", true);
        }
        
        // 设置语言选项
        $("#edit_lang option[value='" + e.lang + "']").prop("selected", true);

        // 显示模态框
        $('#editAlert').modal('show');
        console.log("编辑框已显示");
    } catch (error) {
        console.error("打开编辑框出错:", error);
        show_gitter('错误提示', '打开编辑框出错: ' + error.message, 2);
    }
}


// 编辑
function edit_item(){
    console.log("执行编辑操作");
    var img_idx = $('#edit_px').val().trim();
    var url = $('#edit_url').val().trim();
    var img = $('#edit_pic').attr('src').trim().split('?')[0];
    var show = $('#edit_show').val().trim();
    var lang = $('#edit_lang').val().trim();
    var id = $('#edit_pid').val();
    var title = $('#edit_title').val().trim();
    var description = $('#edit_description').val().trim();
    
    // 验证显示顺序是否为整数
    if(!check_is_num(img_idx)){
        show_gitter('错误提示', '排序必须为整数', 2);
        return;
    }
    
    // 确保img_idx是数字类型
    img_idx = parseInt(img_idx, 10);
    console.log("处理后的排序值:", img_idx);

    var filters = JSON.stringify({'id': id});
    
    // 记录编辑前的数据
    console.log("编辑数据:", {
        pic_idx: img_idx, 
        pic: img, 
        url: url, 
        filters: filters, 
        show: show, 
        lang: lang,
        title: title,
        description: description,
        update_time: new Date().getTime() // 添加更新时间
    });

    $.ajax({
        type: "post",
        url: "/apis/update_home_pic/",
        async: false,
        data: {
            pic_idx: img_idx, 
            pic: img, 
            url: url, 
            filters: filters, 
            show: show, 
            lang: lang,
            title: title,
            description: description,
            update_time: new Date().getTime() // 添加更新时间
        },
        success: function (data) {
            console.log("编辑操作返回:", data);
            if (data.status === 'ok') {
                // 先移除焦点
                $('#editAlert a, #editAlert button, #editAlert input, #editAlert select').blur();
                
                // 再隐藏模态框
                $('#editAlert').modal('hide');
                
                // 清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                // 刷新列表
                home_pic_lst();
                
                if(i18nLanguage==='en_US') {
                    show_gitter('Success', 'Image updated successfully!', 1);
                } else {
                    show_gitter('成功', '图片更新成功', 1);
                }
                
                // 刷新前端页面缓存
                refresh_frontend_cache();
            } else {
                console.error("编辑操作失败:", data);
                if(i18nLanguage==='en_US'){
                    var err_msg = data.msg ? data.msg : 'Internal service error, please contact us';
                    show_gitter('Error', err_msg, 2);
                }else{
                    var err_msg  = data.msg ? data.msg : '内部服务错误';
                    show_gitter('错误提示', err_msg, 2);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error("编辑操作请求失败:", status, error);
            show_gitter('错误提示', '编辑操作请求失败，请检查网络连接', 2);
            
            // 清理背景
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }
    });
}

// 刷新前端页面缓存
function refresh_frontend_cache() {
    // 发送请求到前端，通知其刷新轮播图数据
    $.ajax({
        type: "get",
        url: "/refresh-carousel-cache",
        async: true,
        success: function(data) {
            console.log("前端缓存刷新成功");
        },
        error: function() {
            console.log("前端缓存刷新失败，但不影响后台操作");
        }
    });
}



// 打开删除确认框
function init_del(e) {
    console.log("打开删除确认框，数据:", e);
    
    // 先清理可能存在的模态框背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    try {
        $('#del_id_span').html(e.id);
        $('#del_img').attr('src',e.pic);
        $('#del_id').val(e.id);
        
        // 显示模态框
        $('#myDelete').modal('show');
        console.log("删除确认框已显示");
    } catch (error) {
        console.error("打开删除确认框出错:", error);
        show_gitter('错误提示', '打开删除确认框出错: ' + error.message, 2);
    }
}

// 删除
function del_item(){
    console.log("执行删除操作");
    var id = $('#del_id').val();
    var filters = JSON.stringify({'id':id});
    
    console.log("删除数据ID:", id);

    $.ajax({
        type: "post",
        url: "/apis/delete_home_pic/",
        async: false,
        data: {filters: filters},
        success: function (data) {
            console.log("删除操作返回:", data);
            if (data.status === 'ok') {
                // 先移除焦点
                $('#myDelete a, #myDelete button, #myDelete input').blur();
                
                // 再隐藏模态框
                $('#myDelete').modal('hide');
                
                // 清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                // 刷新列表
                home_pic_lst(current_page);
                
                if(i18nLanguage==='en_US') {
                    show_gitter('Success', 'Image deleted successfully!', 1);
                } else {
                    show_gitter('成功', '成功删除图片', 1);
                }
                
                // 刷新前端页面缓存
                refresh_frontend_cache();
            } else {
                console.error("删除操作失败:", data);
                if(i18nLanguage==='en_US'){
                    var err_msg = data.msg ? data.msg : 'Internal service error, please contact us';
                    show_gitter('Error', err_msg, 2);
                }else{
                    var err_msg  = data.msg ? data.msg : '内部服务错误';
                    show_gitter('错误提示', err_msg, 2);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error("删除操作请求失败:", status, error);
            show_gitter('错误提示', '删除操作请求失败，请检查网络连接', 2);
            
            // 清理背景
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }
    });
}


$('#addAlert').on("show.bs.modal", function () {

    $('#add_new_pic').parent().find('.filename').html('未选择照片');
    $('#add_show_idx').val('1');
    $('#add_pic').attr('src','');
    $('#add_new_pic').val('');
    $('#hurl').val('');
});

$('#add_new_pic').change(
    function () {
        var location = $(this).val();
        var point = location.lastIndexOf(".");
        var type = location.substr(point);
        if (type == '.jpg' || type == '.gif' || type == '.jpeg' || type == '.png' || type == '.bmp') {
            var formData = new FormData();
            formData.append('file', $(this)[0].files[0]);

            $.ajax({
                url: '/apis/upload_pic/',
                type: 'POST',
                cache: false,
                data: formData,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.status === 'ok') {
                        $('#add_pic').attr('src', res.path);
                    } else {
                        show_gitter('错误提示', res.msg || '上传失败', 2);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('上传图片失败:', error);
                    show_gitter('错误提示', '服务器连接错误，请稍后再试', 2);
                }
            });
        } else {
            $('#add_new_pic').val('');
            show_gitter('错误提示', '仅支持jpg、jpeg、png、gif、bmp格式图片', 2);
        }
    }
);

$('#edit_new_pic').change(
    function () {
        var location = $(this).val();
        var point = location.lastIndexOf(".");
        var type = location.substr(point);
        if (type == '.jpg' || type == '.gif' || type == '.jpeg' || type == '.png' || type == '.bmp') {
            var formData = new FormData();
            formData.append('file', $(this)[0].files[0]);

            $.ajax({
                url: '/apis/upload_pic/',
                type: 'POST',
                cache: false,
                data: formData,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.status === 'ok') {
                        $('#edit_pic').attr('src', res.path);
                    } else {
                        show_gitter('错误提示', res.msg || '上传失败', 2);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('上传图片失败:', error);
                    show_gitter('错误提示', '服务器连接错误，请稍后再试', 2);
                }
            });
        } else {
            $('#edit_new_pic').val('');
            show_gitter('错误提示', '仅支持jpg、jpeg、png、gif、bmp格式图片', 2);
        }
    }
);

function make_index_html(){
    console.log("执行中文首页重新生成");
    
    $.ajax({
        type: "post",
        url: "/apis/make_index_html/",
        async: false,
        success: function (data) {
            console.log("中文首页重新生成返回:", data);
            if (data.status === 'ok') {
                // 先移除焦点
                $('#makeClsJson a, #makeClsJson button').blur();
                
                // 再隐藏模态框
                $('#makeClsJson').modal('hide');
                
                // 清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                if(i18nLanguage==='en_US') {
                    show_gitter('Success', 'Chinese homepage has been regenerated successfully!', 1);
                } else {
                    show_gitter('成功', '中文公司首页已经重新生成，请前往官网查看', 1);
                }
            } else {
                console.error("中文首页重新生成失败:", data);
                var err_msg = data.msg ? data.msg : (i18nLanguage==='en_US' ? 'Internal service error' : '内部服务错误');
                show_gitter(i18nLanguage==='en_US' ? 'Error' : '错误提示', err_msg, 2);
            }
        },
        error: function(xhr, status, error) {
            console.error("中文首页重新生成请求失败:", status, error);
            show_gitter('错误提示', '中文首页重新生成请求失败，请检查网络连接', 2);
            
            // 清理背景
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }
    });
}

function make_index_html_en(){
    console.log("执行英文首页重新生成");
    
    $.ajax({
        type: "post",
        url: "/apis/make_index_html_en/",
        async: false,
        success: function (data) {
            console.log("英文首页重新生成返回:", data);
            if (data.status === 'ok') {
                // 先移除焦点
                $('#makeClsJson_en a, #makeClsJson_en button').blur();
                
                // 再隐藏模态框
                $('#makeClsJson_en').modal('hide');
                
                // 清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                if(i18nLanguage==='en_US') {
                    show_gitter('Success', 'English homepage has been regenerated successfully!', 1);
                } else {
                    show_gitter('成功', '英文公司首页已经重新生成，请前往官网查看', 1);
                }
            } else {
                console.error("英文首页重新生成失败:", data);
                var err_msg = data.msg ? data.msg : (i18nLanguage==='en_US' ? 'Internal service error' : '内部服务错误');
                show_gitter(i18nLanguage==='en_US' ? 'Error' : '错误提示', err_msg, 2);
            }
        },
        error: function(xhr, status, error) {
            console.error("英文首页重新生成请求失败:", status, error);
            show_gitter('错误提示', '英文首页重新生成请求失败，请检查网络连接', 2);
            
            // 清理背景
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }
    });
}

$('#sel').on('input propertychange', function () {
    home_pic_lst();
});

// 添加调试函数，用于测试轮播图排序功能
function debug_carousel_order() {
    console.log("开始调试轮播图排序功能");
    
    // 获取当前轮播图数据
    $.ajax({
        type: "post",
        url: "/apis/get_home_pic/",
        async: false,
        headers: {
            'X-Client-Type': 'admin',
            'X-Admin-Request': 'true'
        },
        data: {page: 1, page_size: 100},
        success: function (data) {
            if (data.status === 'ok') {
                var data_list = data.data_list;
                console.log("获取到轮播图数据:", data_list.length, "条记录");
                
                // 显示当前排序
                console.log("当前轮播图排序:");
                $.each(data_list, function (_, item) {
                    console.log('记录' + (index+1) + ': ID=' + item.id + ', 显示=' + item.show + ', 排序=' + item.pic_idx + ', 图片路径=' + item.pic);
                });
                
                // 过滤显示的轮播图
                var visibleItems = data_list.filter(function(item) {
                    return item.show === true || item.show === 1 || item.show === "1";
                });
                
                var hiddenItems = data_list.filter(function(item) {
                    return !(item.show === true || item.show === 1 || item.show === "1");
                });
                
                console.log("显示状态的轮播图:", visibleItems.length, "条记录");
                console.log("隐藏状态的轮播图:", hiddenItems.length, "条记录");
                
                // 按pic_idx排序
                var sortedItems = visibleItems.sort(function(a, b) {
                    var aIdx = parseInt(a.pic_idx, 10);
                    var bIdx = parseInt(b.pic_idx, 10);
                    return aIdx - bIdx; // 升序排序
                });
                
                console.log("排序后的轮播图顺序:");
                $.each(sortedItems, function (index, item) {
                    console.log(`[${index}] ID=${item.id}, pic_idx=${item.pic_idx}, show=${item.show}`);
                });
                
                // 显示排序结果
                var resultHtml = '<div class="alert alert-info">' +
                    '<h4>轮播图排序调试结果</h4>' +
                    '<p>总记录数: ' + data_list.length + '</p>' +
                    '<p>显示的记录数: ' + visibleItems.length + '</p>' +
                    '<p>隐藏的记录数: ' + hiddenItems.length + '</p>' +
                    '<h5>显示状态的轮播图:</h5>' +
                    '<table class="table table-bordered">' +
                    '<thead><tr><th>顺序</th><th>ID</th><th>排序值</th><th>显示状态</th></tr></thead>' +
                    '<tbody>';
                
                $.each(sortedItems, function (index, item) {
                    resultHtml += '<tr>' +
                        '<td>' + (index + 1) + '</td>' +
                        '<td>' + item.id + '</td>' +
                        '<td>' + item.pic_idx + '</td>' +
                        '<td>' + (item.show === true || item.show === 1 ? '显示' : '不显示') + '</td>' +
                        '</tr>';
                });
                
                resultHtml += '</tbody></table>';
                
                // 添加隐藏状态的轮播图表格
                if (hiddenItems.length > 0) {
                    resultHtml += '<h5>隐藏状态的轮播图:</h5>' +
                        '<table class="table table-bordered table-striped">' +
                        '<thead><tr><th>ID</th><th>排序值</th><th>显示状态</th></tr></thead>' +
                        '<tbody>';
                    
                    $.each(hiddenItems, function (index, item) {
                        resultHtml += '<tr>' +
                            '<td>' + item.id + '</td>' +
                            '<td>' + item.pic_idx + '</td>' +
                            '<td>' + (item.show === true || item.show === 1 ? '显示' : '不显示') + '</td>' +
                            '</tr>';
                    });
                    
                    resultHtml += '</tbody></table>';
                }
                
                resultHtml += '<p>前端轮播图将按照上表顺序显示</p>' +
                    '</div>';
                
                // 显示结果
                $('#debug_result').html(resultHtml).show();
                
                // 刷新前端缓存
                refresh_frontend_cache();
                
                show_gitter('成功', '轮播图排序调试完成', 1);
            } else {
                console.error("获取数据失败:", data);
                show_gitter('错误提示', '获取轮播图数据失败', 2);
            }
        },
        error: function(xhr, status, error) {
            console.error("请求失败:", status, error);
            show_gitter('错误提示', '请求失败，请检查网络连接', 2);
        }
    });
}

// 添加一个函数来修复所有轮播图的显示状态
function fix_carousel_show_status() {
    console.log("开始修复轮播图显示状态");
    
    // 获取所有轮播图数据
    $.ajax({
        type: "post",
        url: "/apis/get_home_pic/",
        async: false,
        headers: {
            'X-Client-Type': 'admin'
        },
        data: {page: 1, page_size: 100},
        success: function (data) {
            if (data.status === 'ok') {
                var data_list = data.data_list;
                console.log('获取到' + data_list.length + '条轮播图数据');
                
                // 记录需要修复的记录数量
                var fixCount = 0;
                
                // 检查每条记录
                $.each(data_list, function(index, item) {
                    // 检查show字段是否为布尔值
                    if (typeof item.show !== 'number') {
                        console.log('记录ID=' + item.id + '的show字段类型为' + typeof item.show + '，值为' + item.show + '，需要修复');
                        
                        // 确定正确的show值
                        var showValue = item.show === true || item.show === "true" || item.show === "1" || item.show === 1;
                        
                        // 更新记录
                        updateRecordShowStatus(item.id, showValue ? 1 : 0);
                        fixCount++;
                    }
                });
                
                if (fixCount > 0) {
                    show_gitter('修复完成', '已修复' + fixCount + '条轮播图记录的显示状态', 1);
                    // 刷新列表
                    setTimeout(function() {
                        home_pic_lst();
                    }, 1000);
                } else {
                    show_gitter('检查完成', '所有轮播图记录的显示状态正常', 1);
                }
            } else {
                show_gitter('错误', '获取轮播图数据失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误', '请求失败，请检查网络连接', 2);
        }
    });
}

// 更新单条记录的显示状态
function updateRecordShowStatus(id, showValue) {
    console.log(`更新记录ID=${id}的显示状态为${showValue}`);
    
    // 构造请求参数
    var filters = JSON.stringify({'id': id});
    
    $.ajax({
        type: "post",
        url: "/apis/update_show_status/",
        async: false,
        data: {
            filters: filters,
            show: showValue
        },
        success: function(data) {
            if (data.status === 'ok') {
                console.log('成功更新ID=' + id + '的显示状态为' + newShow);
            } else {
                console.error('更新记录ID=' + id + '的显示状态失败:', data.msg);
            }
        },
        error: function(xhr, status, error) {
            console.error('更新记录ID=' + id + '的显示状态请求失败:', error);
        }
    });
}

// 在页面加载完成后添加修复按钮
$(document).ready(function() {
    // 添加调试按钮到页面
    var debugBtn = $('<button class="btn btn-info" style="margin-left: 10px;"><i class="icon-bug icon-white"></i> 调试排序</button>');
    debugBtn.click(function() {
        debug_carousel_order();
    });
    
    // 添加修复按钮
    var fixBtn = $('<button class="btn btn-warning" style="margin-left: 10px;"><i class="icon-wrench icon-white"></i> 修复显示状态</button>');
    fixBtn.click(function() {
        fix_carousel_show_status();
    });
    
    // 添加到页面上合适的位置
    $('.btn-group').first().append(debugBtn).append(fixBtn);
    
    // 添加调试结果容器
    $('<div id="debug_result" style="display:none; margin-top: 20px;"></div>').insertAfter('.row-fluid:first');
    
    // 添加显示状态切换事件
    $(document).on('click', '.toggle-show-status', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        var id = $(this).data('id');
        var currentShow = $(this).data('show');
        var newShow = currentShow === 1 ? 0 : 1;
        
        // 获取当前记录的语言类型
        var $tr = $(this).closest('tr');
        var langText = $tr.find('td:nth-child(2)').text().trim();
        var lang = langText.includes('英文') ? 1 : 0;
        
        console.log('切换ID=' + id + '的显示状态: ' + currentShow + ' => ' + newShow + ', 语言类型: ' + (lang === 1 ? '英文' : '中文'));
        
        // 显示加载指示器
        var $badge = $(this);
        var originalText = $badge.text();
        $badge.text('处理中...');
        
        // 更新显示状态
        $.ajax({
            type: "post",
            url: "/apis/update_show_status/",
            async: true,
            headers: {
                'X-Client-Type': 'admin',
                'X-Admin-Request': 'true'
            },
            data: {
                filters: JSON.stringify({id: id}),
                show: newShow,
                lang: lang, // 传递语言参数
                update_time: new Date().getTime() // 添加更新时间
            },
            success: function(data) {
                if (data.status === 'ok') {
                    console.log('成功更新ID=' + id + '的显示状态为' + newShow + ', 语言保持为: ' + lang);
                    
                    // 记录操作时间
                    var timeStr = recordOperationTime(id);
                    
                    // 更新UI
                    if (newShow === 1) {
                        $badge.removeClass('badge-important').addClass('badge-success');
                        $badge.text($.i18n.map['common.show'] || '显示');
                        $badge.data('show', 1);
                    } else {
                        $badge.removeClass('badge-success').addClass('badge-important');
                        $badge.text($.i18n.map['common.unshow'] || '不显示');
                        $badge.data('show', 0);
                    }
                    
                    // 更新操作时间列
                    var $timeCell = $tr.find('td:nth-child(7)');
                    $timeCell.text(timeStr);
                    
                    // 刷新前端缓存
                    refresh_frontend_cache();
                } else {
                    console.error('更新ID=' + id + '的显示状态失败:', data.msg);
                    $badge.text(originalText);
                    show_gitter('错误', '更新显示状态失败: ' + data.msg, 2);
                }
            },
            error: function(xhr, status, error) {
                console.error('更新ID=' + id + '的显示状态请求失败:', error);
                $badge.text(originalText);
                show_gitter('错误', '更新显示状态请求失败，请检查网络连接', 2);
            }
        });
    });
});

// 显示通知消息
function show_gitter(title, text, type) {
    // 类型：1=成功，2=错误，3=警告，4=信息
    var gritterClass = "";
    
    if (type === 1) {
        gritterClass = "gritter-success";
    } else if (type === 2) {
        gritterClass = "gritter-error";
    } else if (type === 3) {
        gritterClass = "gritter-warning";
    } else {
        gritterClass = "gritter-info";
    }
    
    try {
        // 首先检查jQuery和$.gritter是否已加载
        if (typeof $ === 'undefined' || typeof $.gritter === 'undefined') {
            console.error('jQuery或$.gritter未定义');
            alert(title + ': ' + text);
            return;
        }
        
        // 使用jQuery Gritter插件显示通知
        return $.gritter.add({
            title: title,
            text: text,
            class_name: gritterClass,
            sticky: false,
            time: 3000
        });
    } catch (e) {
        // 如果上述方法失败，使用原生alert作为最后手段
        console.error('通知显示错误:', e);
        alert(title + ': ' + text);
    }
}

// 添加日期时间格式化函数
function formatDateTime(timestamp) {
    if (!timestamp) return '未记录';
    
    try {
        // 尝试将时间戳转换为Date对象
        var date;
        if (typeof timestamp === 'number') {
            date = new Date(timestamp);
        } else if (typeof timestamp === 'string') {
            // 尝试解析字符串格式的时间戳
            if (timestamp.indexOf('T') > -1) {
                // ISO格式: 2023-01-01T12:00:00.000Z
                date = new Date(timestamp);
            } else if (!isNaN(Number(timestamp))) {
                // 数字字符串: "1609459200000"
                date = new Date(Number(timestamp));
            } else {
                // 其他格式的日期字符串
                date = new Date(timestamp);
            }
        } else {
            return '格式错误';
        }
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            return '日期无效';
        }
        
        // 格式化日期和时间
        var year = date.getFullYear();
        var month = padZero(date.getMonth() + 1);
        var day = padZero(date.getDate());
        var hours = padZero(date.getHours());
        var minutes = padZero(date.getMinutes());
        var seconds = padZero(date.getSeconds());
        
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    } catch (e) {
        console.error('日期格式化错误:', e, timestamp);
        return '格式错误';
    }
}

// 补零函数
function padZero(num) {
    return num < 10 ? '0' + num : num;
}

// 记录当前操作时间并更新到localStorage
function recordOperationTime(id) {
    var now = new Date();
    var timeStr = formatDateTime(now);
    
    // 从localStorage获取操作记录
    var operationRecords = localStorage.getItem('operationRecords');
    var records = operationRecords ? JSON.parse(operationRecords) : {};
    
    // 更新记录
    records[id] = {
        time: now.getTime(),
        timeStr: timeStr
    };
    
    // 保存回localStorage
    localStorage.setItem('operationRecords', JSON.stringify(records));
    
    return timeStr;
}

// 获取操作记录
function getOperationRecord(id) {
    var operationRecords = localStorage.getItem('operationRecords');
    if (!operationRecords) return null;
    
    var records = JSON.parse(operationRecords);
    return records[id] || null;
}

// 添加查看媒体备份的函数
function viewMediaBackups() {
    // 显示加载提示
    show_gitter('提示', '正在加载媒体备份数据...', 4);
    
    // 创建模态框（如果不存在）
    if ($('#mediaBackupsModal').length === 0) {
        var modalHtml = `
            <div id="mediaBackupsModal" class="modal hide" style="width: 80%; margin-left: -40%; max-height: 80%;">
                <div class="modal-header">
                    <button data-dismiss="modal" class="close" type="button">×</button>
                    <span style="font-size: 18px;font-weight:bold">图片备份管理</span>
                </div>
                <div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                    <div class="row-fluid">
                        <div class="span12">
                            <div class="control-group">
                                <label class="control-label">选择日期:</label>
                                <div class="controls">
                                    <select id="backup-date-select" class="span4">
                                        <option value="">所有日期</option>
                                    </select>
                                    <select id="backup-type-select" class="span3">
                                        <option value="">所有类型</option>
                                        <option value="normal">正常图片</option>
                                        <option value="deleted">已删除图片</option>
                                    </select>
                                    <button class="btn btn-primary" id="load-backups-btn">加载</button>
                                    <button class="btn btn-info" id="view-log-btn">查看日志</button>
                                </div>
                            </div>
                            <div id="backups-container">
                                <div class="alert alert-info">请选择日期并点击"加载"按钮查看备份</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a data-dismiss="modal" class="btn" href="#">关闭</a>
                </div>
            </div>
            
            <div id="mediaLogModal" class="modal hide" style="width: 90%; margin-left: -45%; max-height: 90%;">
                <div class="modal-header">
                    <button data-dismiss="modal" class="close" type="button">×</button>
                    <span style="font-size: 18px;font-weight:bold">图片操作日志</span>
                </div>
                <div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                    <div class="row-fluid">
                        <div class="span12">
                            <div id="log-container">
                                <div class="alert alert-info">正在加载日志...</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a data-dismiss="modal" class="btn" href="#">关闭</a>
                </div>
            </div>
        `;
        
        $('body').append(modalHtml);
        
        // 绑定事件
        $('#load-backups-btn').click(loadBackups);
        $('#view-log-btn').click(viewMediaLog);
    }
    
    // 加载日期列表
    $.ajax({
        type: "get",
        url: "/apis/media_backups/",
        async: true,
        success: function(data) {
            if (data.status === 'ok') {
                // 清空并重新填充日期选择器
                var $select = $('#backup-date-select');
                $select.find('option:not(:first)').remove();
                
                if (data.directories && data.directories.length > 0) {
                    data.directories.forEach(function(dir) {
                        $select.append('<option value="' + dir + '">' + dir + '</option>');
                    });
                }
                
                // 显示模态框
                $('#mediaBackupsModal').modal('show');
            } else {
                show_gitter('错误', data.msg || '加载备份数据失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误', '请求失败，请检查网络连接', 2);
        }
    });
}

// 加载备份文件
function loadBackups() {
    var date = $('#backup-date-select').val();
    var type = $('#backup-type-select').val();
    
    // 显示加载提示
    $('#backups-container').html('<div class="alert alert-info">正在加载备份数据...</div>');
    
    // 构建查询参数
    var url = '/apis/media_backups/';
    var params = [];
    if (date) params.push('date=' + encodeURIComponent(date));
    if (type) params.push('type=' + encodeURIComponent(type));
    if (params.length > 0) url += '?' + params.join('&');
    
    // 发送请求
    $.ajax({
        type: "get",
        url: url,
        async: true,
        success: function(data) {
            if (data.status === 'ok') {
                displayBackups(data, date);
            } else {
                $('#backups-container').html('<div class="alert alert-error">' + (data.msg || '加载备份数据失败') + '</div>');
            }
        },
        error: function(xhr, status, error) {
            $('#backups-container').html('<div class="alert alert-error">请求失败，请检查网络连接</div>');
        }
    });
}

// 显示备份文件
function displayBackups(data, date) {
    var html = '';
    
    if (!date) {
        // 显示日期目录列表
        if (data.directories && data.directories.length > 0) {
            html += '<h4>备份日期列表</h4>';
            html += '<ul class="nav nav-list">';
            data.directories.forEach(function(dir) {
                html += '<li><a href="javascript:void(0)" onclick="$(\'#backup-date-select\').val(\'' + dir + '\'); loadBackups();">';
                html += '<i class="icon-folder-open"></i> ' + dir + '</a></li>';
            });
            html += '</ul>';
        } else {
            html += '<div class="alert alert-info">没有找到备份记录</div>';
        }
        
        // 显示日志文件信息
        if (data.logFile) {
            html += '<h4>日志文件</h4>';
            html += '<ul class="nav nav-list">';
            html += '<li><a href="javascript:void(0)" onclick="viewMediaLog();">';
            html += '<i class="icon-file"></i> ' + data.logFile.name + ' (' + formatFileSize(data.logFile.size) + ')</a></li>';
            html += '</ul>';
        }
    } else {
        // 显示文件列表
        if (data.files && data.files.length > 0) {
            html += '<h4>' + date + ' 备份文件 (' + data.files.length + '个)</h4>';
            html += '<div class="row-fluid">';
            
            data.files.forEach(function(file, index) {
                // 每行显示3个图片
                if (index > 0 && index % 3 === 0) {
                    html += '</div><div class="row-fluid">';
                }
                
                html += '<div class="span4" style="margin-bottom: 20px;">';
                html += '<div class="thumbnail">';
                html += '<img src="/apis/media_file/' + date + '/' + file.name + '" alt="' + file.name + '" style="max-height: 150px;">';
                html += '<div class="caption">';
                html += '<p style="word-break: break-all;"><small>' + file.name + '</small></p>';
                html += '<p><small>大小: ' + formatFileSize(file.size) + '</small></p>';
                html += '<p><small>时间: ' + new Date(file.created).toLocaleString() + '</small></p>';
                if (file.isDeleted) {
                    html += '<span class="label label-important">已删除</span> ';
                }
                html += '<a class="btn btn-mini btn-info" href="/apis/media_file/' + date + '/' + file.name + '" target="_blank">';
                html += '<i class="icon-download-alt icon-white"></i> 下载</a>';
                html += '</div></div></div>';
            });
            
            html += '</div>';
        } else {
            html += '<div class="alert alert-info">该日期没有备份文件</div>';
        }
        
        // 添加返回按钮
        html += '<div><a href="javascript:void(0)" onclick="$(\'#backup-date-select\').val(\'\'); loadBackups();" class="btn">';
        html += '<i class="icon-arrow-left"></i> 返回日期列表</a></div>';
    }
    
    $('#backups-container').html(html);
}

// 查看媒体操作日志
function viewMediaLog() {
    // 显示模态框
    $('#mediaLogModal').modal('show');
    
    // 加载日志
    $('#log-container').html('<div class="alert alert-info">正在加载日志...</div>');
    
    $.ajax({
        type: "get",
        url: "/apis/media_log/",
        async: true,
        success: function(data) {
            if (data.status === 'ok' && data.entries && data.entries.length > 0) {
                // 构建表格
                var html = '<table class="table table-striped table-bordered">';
                html += '<thead><tr><th>时间</th><th>操作</th><th>文件名</th><th>大小</th></tr></thead><tbody>';
                
                data.entries.forEach(function(entry) {
                    var operationClass = '';
                    if (entry.operation === '上传') operationClass = 'label-success';
                    else if (entry.operation === '删除') operationClass = 'label-important';
                    else if (entry.operation === '备份') operationClass = 'label-info';
                    
                    html += '<tr>';
                    html += '<td>' + entry.time + '</td>';
                    html += '<td><span class="label ' + operationClass + '">' + entry.operation + '</span></td>';
                    html += '<td style="word-break: break-all;">' + entry.filename + '</td>';
                    html += '<td>' + formatFileSize(entry.size) + '</td>';
                    html += '</tr>';
                });
                
                html += '</tbody></table>';
                $('#log-container').html(html);
            } else {
                $('#log-container').html('<div class="alert alert-info">没有找到日志记录</div>');
            }
        },
        error: function(xhr, status, error) {
            $('#log-container').html('<div class="alert alert-error">加载日志失败，请检查网络连接</div>');
        }
    });
}

// 格式化文件大小
function formatFileSize(size) {
    if (size < 1024) {
        return size + ' B';
    } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
    } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
}

// 修正下拉框监听，传递 lang 参数
$(function() {
    $('#lang_filter').on('change', function() {
        var lang = $(this).val();
        if (lang === "" || lang === undefined || lang === null) {
            home_pic_lst(1, 15); // 不传 lang
        } else {
            home_pic_lst(1, 15, lang); // 传 lang
        }
    });
});

// 设置edit_item函数，确保在编辑时保持语言设置
window.original_show_edit_modal = window.show_edit_modal; // 保存原始函数
window.show_edit_modal = function(id) {
    // 调用原始函数
    window.original_show_edit_modal(id);
    
    // 获取当前选择的语言值
    var currentLang = $('#lang_filter').val();
    if (currentLang !== '') {
        // 如果已选择语言，则在获取数据后设置编辑表单的语言字段
        setTimeout(function() {
            $('#edit_lang').val(currentLang);
            
            // 根据语言设置表单标题
            if (currentLang === '0') {
                $('#edit_modal_title').text('编辑中文首页图片');
            } else if (currentLang === '1') {
                $('#edit_modal_title').text('编辑英文首页图片');
            }
            
            // 如果是特定语言，隐藏语言选择字段
            if (currentLang === '0' || currentLang === '1') {
                $('#edit_lang_group').hide();
            } else {
                $('#edit_lang_group').show();
            }
        }, 500); // 等待数据加载完成
    }
};

// 监听添加按钮点击事件
$('a[href="#addAlert"]').on('click', function() {
    // 获取当前选择的语言值
    var currentLang = $('#lang_filter').val();
    if (currentLang !== '') {
        // 如果已选择语言，则设置添加表单的语言字段
        $('#add_lang').val(currentLang);
        
        // 根据语言设置表单标题
        if (currentLang === '0') {
            $('#add_modal_title').text('新增中文首页图片');
        } else if (currentLang === '1') {
            $('#add_modal_title').text('新增英文首页图片');
        }
        
        // 如果是英文，隐藏语言选择字段
        if (currentLang === '1') {
            $('#add_lang_group').hide();
        } else {
            $('#add_lang_group').show();
        }
    }
});