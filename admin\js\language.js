/**
 * cookie操作
 */
function setCookie(c_name, value, expiredays) {
  var exdate = new Date();
  exdate.setDate(exdate.getDate() + expiredays);
  document.cookie = c_name + "=" + escape(value) + ((expiredays == null) ? "": ";expires=" + exdate.toGMTString());
}
function getCookie(c_name) {
  var that = this;
  if (document.cookie.length > 0) {
    //检查这个cookie是否存在，不存在就为 -1
    c_start = document.cookie.indexOf(c_name + "=")
    if (c_start != -1) {
      //获取cookie值的开始位置
      c_start = c_start + c_name.length + 1;
      //通过";"号是否存在来判断结束位置
      c_end = document.cookie.indexOf(";", c_start);

      if (c_end == -1){
        c_end = document.cookie.length;
      }
      //通过substring()得到了值
      return unescape(document.cookie.substring(c_start, c_end))
    }
  }
  return ""
}
/**
 * 获取浏览器语言类型
 * @return {string} 浏览器国家语言
 */
var getNavLanguage = function(){
  if(navigator.appName == "Netscape"){
    var navLanguage = navigator.language;
    // 替换-为_  适配资源文件名，可自定义
    return  navLanguage.replace(/[-]/g,"_");
  }
  return false;
}

// 定义语言变量
var i18nLanguage = '';
// 设置网站支持的语言种类
var webLanguage = ['zh_CN', 'en_US', 'ar_AE', 'de_DE'];

// 定义页面i18n方法
var execI18n = function(){
  // 获取cookie语言类型
  if(getCookie("languageType")) {
    i18nLanguage = getCookie("languageType")
    console.log('获取cooKie语言类型成功--->', getCookie("languageType"))
  } else if(getNavLanguage() && webLanguage.indexOf(getNavLanguage()) >= 0) {
    // 获取浏览器语言类型后
    // 判断在支持范围内，未支持默认显示中文
    i18nLanguage = getNavLanguage()
    console.log('获取浏览器语言类型成功--->', getNavLanguage())
  } else {
    // 获取失败时设置中文
    i18nLanguage = 'zh_CN'
  }
  /* 判断引入 i18n 文件*/
  if ($.i18n == undefined) {
    console.log("请引入i18n js 文件")
    return false;
  };

  // 确保i18n.map存在
  if (!$.i18n.map) {
    $.i18n.map = {};
  }
  
  // 添加默认的i18n键值
  var defaultKeys = {
    'common.show': '显示',
    'common.unshow': '不显示',
    'common.save': '保存',
    'common.cancel': '取消',
    'common.del': '删除',
    'common.start': '首页',
    'common.end': '尾页',
    'common.last_page': '上一页',
    'common.next_page': '下一页',
    'common.add': '添加',
    'head.welcome': '欢迎',
    'head.lgout': '退出',
    'head.overview': '概览',
    'login.title': '管理系统',
    'product_cls.show': '是否显示',
    'product_cls.show_idx': '显示顺序'
  };
  
  // 为所有缺失的键设置默认值
  for (var key in defaultKeys) {
    if (!$.i18n.map[key]) {
      $.i18n.map[key] = defaultKeys[key];
    }
  }

  // i18n翻译方法
  try {
    // 修改资源文件名称，使用特定语言的文件
    var langSpecificName = 'messages_' + i18nLanguage;
    
    jQuery.i18n.properties({
      name : langSpecificName,  // 使用带语言代码的资源文件名
      path : '/admin/i18n/',     // 资源文件路径，修改为绝对路径
      mode : 'map',       // 用Map的方式使用资源文件中的值
      language : '',      // 语言已包含在文件名中，这里留空
      cache: false,
      async: false,
      callback : function() {//加载成功后设置显示内容
        // 初始化一些基本的i18n键值，防止undefined错误
        if (!$.i18n.map) {
          $.i18n.map = {};
        }
        
        // 确保基本的i18n键有默认值
        for (var key in defaultKeys) {
          if (!$.i18n.map[key]) {
            $.i18n.map[key] = defaultKeys[key];
          }
        }
        
        var insertEle = $(".i18n");
        console.log(".i18n 写入中...");
        insertEle.each(function() {
          try {
            // 根据i18n元素的 name 获取内容写入
            if ($(this).attr('name')) {
              var value = $.i18n.prop($(this).attr('name')) || $(this).attr('name');
              $(this).html(value);
            }
          } catch (e) {
            console.error('i18n处理错误:', e, $(this).attr('name'));
          }
        });
        console.log("写入完毕");

        console.log(".i18n-input 写入中...");
        var insertInputEle = $(".i18n-input");
        insertInputEle.each(function() {
          try {
            var selectAttr = $(this).attr('selectattr');
            if (!selectAttr) {
              selectAttr = "value";
            }
            var value = $.i18n.prop($(this).attr('selectname')) || $(this).attr('selectname');
            $(this).attr(selectAttr, value);
          } catch (e) {
            console.error('i18n-input处理错误:', e, $(this).attr('selectname'));
          }
        });
        console.log("写入完毕");
      },
      error: function(xhr, status, error) {
        console.error('加载i18n资源文件失败:', status, error);
        // 使用默认的i18n映射
        for (var key in defaultKeys) {
          $.i18n.map[key] = defaultKeys[key];
        }
        
        // 尝试应用i18n
        try {
          var insertEle = $(".i18n");
          insertEle.each(function() {
            if ($(this).attr('name') && defaultKeys[$(this).attr('name')]) {
              $(this).html(defaultKeys[$(this).attr('name')]);
            }
          });
          
          var insertInputEle = $(".i18n-input");
          insertInputEle.each(function() {
            var selectAttr = $(this).attr('selectattr') || "value";
            if ($(this).attr('selectname') && defaultKeys[$(this).attr('selectname')]) {
              $(this).attr(selectAttr, defaultKeys[$(this).attr('selectname')]);
            }
          });
        } catch (e) {
          console.error('应用默认i18n值失败:', e);
        }
      }
    });
  } catch (e) {
    console.error('执行i18n初始化失败:', e);
    // 使用默认的i18n映射
    for (var key in defaultKeys) {
      $.i18n.map[key] = defaultKeys[key];
    }
    
    // 尝试应用i18n
    try {
      var insertEle = $(".i18n");
      insertEle.each(function() {
        if ($(this).attr('name') && defaultKeys[$(this).attr('name')]) {
          $(this).html(defaultKeys[$(this).attr('name')]);
        }
      });
      
      var insertInputEle = $(".i18n-input");
      insertInputEle.each(function() {
        var selectAttr = $(this).attr('selectattr') || "value";
        if ($(this).attr('selectname') && defaultKeys[$(this).attr('selectname')]) {
          $(this).attr(selectAttr, defaultKeys[$(this).attr('selectname')]);
        }
      });
    } catch (e) {
      console.error('应用默认i18n值失败:', e);
    }
  }
}

execI18n();
// 页面执行加载执行
$(function(){
  // 调用翻译方法
  // execI18n();
  // 将语言选择默认选中缓存中的值
  $("#language_login option[value="+i18nLanguage+"]").attr("selected",true);
  $("#language option[value="+i18nLanguage+"]").attr("selected",true);

  // 选择语言方法
  $("#language").on('change', function() {
    var language = $(this).children('option:selected').val()
    console.log('选择language-->', language);
    // 选择语言后设置cookie存储；变量名，值，过期天数
    setCookie("languageType", language, 365);
    execI18n();
  });// 选择语言方法
  $("#language_login").on('change', function() {
    var language = $(this).children('option:selected').val()
    console.log('选择language-->', language);
    // 选择语言后设置cookie存储；变量名，值，过期天数
    setCookie("languageType", language, 365);
    execI18n();
  });
});
