/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.1 (2020-07-08)
 */
!function(y){"use strict";var e,t,n,r,p=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},c=tinymce.util.Tools.resolve("tinymce.PluginManager"),o=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},f=function(n,r){n.addCommand("mceTogglePlainTextPaste",function(){var e,t;e=n,"text"===(t=r).pasteFormat.get()?(t.pasteFormat.set("html"),o(e,!1)):(t.pasteFormat.set("text"),o(e,!0)),e.focus()}),n.addCommand("mceInsertClipboardContent",function(e,t){t.content&&r.pasteHtml(t.content,t.internal),t.text&&r.pasteText(t.text)})},a=function(e){return function(){return e}},i=a(!1),s=a(!0),u=function(){return l},l=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:i,isSome:i,isNone:s,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:a(null),getOrUndefined:a(undefined),or:n,orThunk:t,map:u,each:function(){},bind:u,exists:i,forall:s,filter:u,equals:e,equals_:e,toArray:function(){return[]},toString:a("none()")}),d=function(n){var e=a(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:s,isNone:i,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return d(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(i,function(e){return t(n,e)})}};return o},g={some:d,none:u,from:function(e){return null===e||e===undefined?l:d(e)}},m=(r="function",function(e){return typeof e===r}),v=Array.prototype.slice,h=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var a=e[o];r[o]=t(a,o)}return r},b=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},x=m(Array.from)?Array.from:function(e){return v.call(e)},P=tinymce.util.Tools.resolve("tinymce.Env"),w=tinymce.util.Tools.resolve("tinymce.util.Delay"),_=tinymce.util.Tools.resolve("tinymce.util.Promise"),T=tinymce.util.Tools.resolve("tinymce.util.Tools"),C=tinymce.util.Tools.resolve("tinymce.util.VK"),D="x-tinymce/html",k="\x3c!-- "+D+" --\x3e",S=function(e){return-1!==e.indexOf(k)},O=tinymce.util.Tools.resolve("tinymce.html.Entities"),R=function(e,t,n){var r=e.split(/\n\n/),o=function(e,t){var n,r=[],o="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+O.encodeAllRaw(t[n])+'"');r.length&&(o+=" "+r.join(" "))}return o+">"}(t,n),a="</"+t+">",i=T.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===i.length?i[0]:T.map(i,function(e){return o+e+a}).join("")},A=tinymce.util.Tools.resolve("tinymce.html.DomParser"),I=tinymce.util.Tools.resolve("tinymce.html.Serializer"),E="\xa0",F=tinymce.util.Tools.resolve("tinymce.html.Node"),M=tinymce.util.Tools.resolve("tinymce.html.Schema"),N=function(e){return e.getParam("paste_data_images",!1)},B=function(e){return e.getParam("paste_retain_style_properties")},$=function(e){return e.getParam("validate")},H=function(e){return e.getParam("paste_data_images",!1,"boolean")};function j(t,e){return T.each(e,function(e){t=e.constructor===RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function L(e){var t=M(),n=A({},t),r="",o=t.getShortEndedElements(),a=T.makeMap("script noscript style textarea video audio iframe object"," "),i=t.getBlockElements();return e=j(e,[/<!\[[^\]]+\]>/g]),function s(e){var t=e.name,n=e;if("br"!==t){if("wbr"!==t)if(o[t]&&(r+=" "),a[t])r+=" ";else{if(3===e.type&&(r+=e.value),!e.shortEnded&&(e=e.firstChild))for(;s(e),e=e.next;);i[t]&&n.next&&(r+="\n","p"===t&&(r+="\n"))}}else r+="\n"}(n.parse(e)),r}function z(e){return e=j(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function r(e,t,n){return t||n?E:" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])}var U=function(){return-1!==y.navigator.userAgent.indexOf(" Edge/")};function q(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function V(t){var n;return t=t.replace(/^[\u00a0 ]+/,""),T.each([/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],function(e){if(e.test(t))return!(n=!0)}),n}function K(e){var a,i,s=1;function n(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=n(e),e=e.next;);return t}function u(e,t){if(3!==e.type||!t.test(e.value)){if(e=e.firstChild)do{if(!u(e,t))return}while(e=e.next);return 1}e.value=e.value.replace(t,"")}function t(e,t,n){var r=e._listLevel||s;r!==s&&(a=r<s?a&&a.parent.parent:(i=a,null)),a&&a.name===t?a.append(e):(i=i||a,a=new F(t,1),1<n&&a.attr("start",""+n),e.wrap(a)),e.name="li",s<r&&i&&i.lastChild.append(a),s=r,function o(e){if(e._listIgnore)e.remove();else if(e=e.firstChild)for(;o(e),e=e.next;);}(e),u(e,/^\u00a0+/),u(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),u(e,/^\u00a0+/)}for(var r=[],o=e.firstChild;null!=o;)if(r.push(o),null!==(o=o.walk()))for(;void 0!==o&&o.parent!==e;)o=o.walk();for(var l=0;l<r.length;l++)if("p"===(e=r[l]).name&&e.firstChild){var c=n(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(c)){t(e,"ul");continue}if(V(c)){var f=/([0-9]+)\./.exec(c),d=1;f&&(d=parseInt(f[1],10)),t(e,"ol",d);continue}if(e._listLevel){t(e,"ul",1);continue}a=null}else i=a,a=null}function X(n,r,o,a){var i,s={},e=n.dom.parseStyle(a);return T.each(e,function(e,t){switch(t){case"mso-list":(i=/\w+ \w+([0-9]+)/i.exec(a))&&(o._listLevel=parseInt(i[1],10)),/Ignore/i.test(e)&&o.firstChild&&(o._listIgnore=!0,o.firstChild._listIgnore=!0);break;case"horiz-align":t="text-align";break;case"vert-align":t="vertical-align";break;case"font-color":case"mso-foreground":t="color";break;case"mso-background":case"mso-highlight":t="background";break;case"font-weight":case"font-style":return void("normal"!==e&&(s[t]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void o.remove()}0!==t.indexOf("mso-comment")?0!==t.indexOf("mso-")&&("all"===B(n)||r&&r[t])&&(s[t]=e):o.remove()}),/(bold)/i.test(s["font-weight"])&&(delete s["font-weight"],o.wrap(new F("b",1))),/(italic)/i.test(s["font-style"])&&(delete s["font-style"],o.wrap(new F("i",1))),(s=n.dom.serializeStyle(s,o.name))||null}var W=function(e,t){return e.getParam("paste_enable_default_filters",!0)?function(r,e){var o,t=B(r);t&&(o=T.makeMap(t.split(/[, ]/))),e=j(e,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,E],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return 0<t.length?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join(E):""}]]);var n=r.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody"),a=M({valid_elements:n,valid_children:"-li[p]"});T.each(a.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var i=A({},a);i.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)(t=e[n]).attr("style",X(r,o,t,t.attr("style"))),"span"===t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),i.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),i.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),i.addNodeFilter("a",function(e){for(var t,n,r,o=e.length;o--;)if(n=(t=e[o]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=(n=n.split("#")[1])&&"#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=i.parse(e);return r.getParam("paste_convert_word_fake_lists",!0)&&K(s),e=I({validate:$(r)},a).serialize(s)}(e,t):t},Y=function(e,t){return{content:e,cancelled:t}},Z=function(e,t,n,r){var o,a,i,s,u,l,c,f,d,m,p,g,v=(o=t,a=n,i=r,e.fire("PastePreProcess",{content:o,internal:a,wordContent:i})),h=function(e,t){var n=A({},e.schema);n.addNodeFilter("meta",function(e){T.each(e,function(e){return e.remove()})});var r=n.parse(t,{forced_root_block:!1,isRootContent:!0});return I({validate:$(e)},e.schema).serialize(r)}(e,v.content);return e.hasEventListeners("PastePostProcess")&&!v.isDefaultPrevented()?(u=h,l=n,c=r,p=(s=e).dom.create("div",{style:"display:none"},u),f=p,d=l,m=c,g=s.fire("PastePostProcess",{node:f,internal:d,wordContent:m}),Y(g.node.innerHTML,g.isDefaultPrevented())):Y(h,v.isDefaultPrevented())},G=function(e,t){return e.insertContent(t,{merge:e.getParam("paste_merge_formats",!0),paste:!0}),!0},J=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},Q=function(e){return J(e)&&/.(gif|jpe?g|png)$/.test(e)},ee=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!J(t))&&(o=t,a=n,(r=e).undoManager.extra(function(){a(r,o)},function(){r.execCommand("mceInsertLink",!1,o)}),!0);var r,o,a},te=function(e,t,n){return!!Q(t)&&(o=t,a=n,(r=e).undoManager.extra(function(){a(r,o)},function(){r.insertContent('<img src="'+o+'">')}),!0);var r,o,a},ne=function(e,t,n){var r,o;n||!1===e.getParam("smart_paste",!0)?G(e,t):(r=e,o=t,T.each([ee,te,G],function(e){return!0!==e(r,o,G)}))},re=function(e){return"\n"===e||"\r"===e},oe=function(o){var t,n;return(n={pcIsSpace:!(t=function(e,t){return-1!==" \f\t\x0B".indexOf(t)||t===E?e.pcIsSpace||""===e.str||e.str.length===o.length-1||(n=o,(r=e.str.length+1)<n.length&&0<=r&&re(n[r]))?{pcIsSpace:!1,str:e.str+E}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:re(t),str:e.str+t};var n,r}),str:""},b(o,function(e){n=t(n,e)}),n).str},ae=function(e,t,n,r){var o,a,i,s,u,l=(o=e,i=n,s=q(a=t),u=s?W(o,a):a,Z(o,u,i,s));!1===l.cancelled&&ne(e,l.content,r)},ie=function(e,t,n){var r=n||S(t);ae(e,t.replace(k,""),r,!1)},se=function(e,t){var n,r,o,a=e.dom.encode(t).replace(/\r\n/g,"\n"),i=oe(a),s=(n=i,r=e.getParam("forced_root_block"),o=e.getParam("forced_root_block_attrs"),r?R(n,!0===r?"p":r,o):n.replace(/\r?\n/g,"<br>"));ae(e,s,!1,!0)},ue=function(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&0<n.length&&-1===n.indexOf("data:text/mce-internal,")&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var o=e.types[r];try{t[o]=e.getData(o)}catch(a){t[o]=""}}}return t},le=function(e,t){return t in e&&0<e[t].length},ce=function(e){return le(e,"text/html")||le(e,"text/plain")},fe=function He(e){var t=0;return function(){return e+t++}}("mceclip"),de=function(e,t){var n,r,o,a,i,s,u,l=(n=t.uri,(r=/data:([^;]+);base64,([a-z0-9\+\/=]+)/i.exec(n))?{type:r[1],data:decodeURIComponent(r[2])}:{type:null,data:null}),c=l.data,f=l.type,d=fe(),m=e.getParam("images_reuse_filename")&&t.blob.name?(o=e,a=t.blob.name,(i=a.match(/([\s\S]+?)\.(?:jpeg|jpg|png|gif)$/i))?o.dom.encode(i[1]):null):d,p=new y.Image;if(p.src=t.uri,s=p,!(u=e.getParam("images_dataimg_filter"))||u(s)){var g=e.editorUpload.blobCache,v=void 0,h=g.getByData(c,f);h?v=h:(v=g.create(d,t.blob,c,m),g.add(v)),ie(e,'<img src="'+v.blobUri()+'">',!1)}else ie(e,'<img src="'+t.uri+'">',!1)},me=function(t,e,n){var r,o,a,i,s="paste"===e.type?e.clipboardData:e.dataTransfer;if(H(t)&&s){var u=(a=(o=s).items?h(x(o.items),function(e){return e.getAsFile()}):[],i=o.files?x(o.files):[],function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var a=e[r];t(a,r)&&n.push(a)}return n}(0<a.length?a:i,function(e){return/^image\/(jpeg|png|gif|bmp)$/.test(e.type)}));if(0<u.length)return e.preventDefault(),r=u,_.all(h(r,function(r){return new _(function(e){var t=r.getAsFile?r.getAsFile():r,n=new window.FileReader;n.onload=function(){e({blob:t,uri:n.result})},n.readAsDataURL(t)})})).then(function(e){n&&t.selection.setRng(n),b(e,function(e){de(t,e)})}),!0}return!1},pe=function(e){return C.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},ge=function(u,l,c){var t,f,d=(t=p(g.none()),{clear:function(){t.set(g.none())},set:function(e){t.set(g.some(e))},isSet:function(){return t.get().isSome()},on:function(e){t.get().each(e)}});function m(e,t,n,r){var o;le(e,"text/html")?o=e["text/html"]:(o=l.getHtml(),r=r||S(o),l.isDefaultContent(o)&&(n=!0)),o=z(o),l.remove();var a=!1===r&&!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(o),i=Q(o);o.length&&(!a||i)||(n=!0),(n||i)&&(o=le(e,"text/plain")&&a?e["text/plain"]:L(o)),l.isDefaultContent(o)?t||u.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):n?se(u,o):ie(u,o,r)}u.on("keydown",function(e){function t(e){pe(e)&&!e.isDefaultPrevented()&&l.remove()}if(pe(e)&&!e.isDefaultPrevented()){if((f=e.shiftKey&&86===e.keyCode)&&P.webkit&&-1!==y.navigator.userAgent.indexOf("Version/"))return;if(e.stopImmediatePropagation(),d.set(e),window.setTimeout(function(){d.clear()},100),P.ie&&f)return e.preventDefault(),n=!0,void u.fire("paste",{ieFake:n});l.remove(),l.create(),u.once("keyup",t),u.once("paste",function(){u.off("keyup",t)})}var n});u.on("paste",function(e){var t,n,r,o=d.isSet(),a=(t=u,n=ue(e.clipboardData||t.getDoc().dataTransfer),U()?T.extend(n,{"text/html":""}):n),i="text"===c.get()||f,s=le(a,D);(f=!1,e.isDefaultPrevented()||(r=e.clipboardData,-1!==y.navigator.userAgent.indexOf("Android")&&r&&r.items&&0===r.items.length))?l.remove():ce(a)||!me(u,e,l.getLastRng()||u.selection.getRng())?(o||e.preventDefault(),!P.ie||o&&!e.ieFake||le(a,"text/html")||(l.create(),u.dom.bind(l.getEl(),"paste",function(e){e.stopPropagation()}),u.getDoc().execCommand("Paste",!1,null),a["text/html"]=l.getHtml()),le(a,"text/html")?(e.preventDefault(),s=s||S(a["text/html"]),m(a,o,i,s)):w.setEditorTimeout(u,function(){m(a,o,i,s)},0)):l.remove()})},ve=function(i,e,t){var s;ge(i,e,t),i.parser.addNodeFilter("img",function(e,t,n){var r,o=function(e){e.attr("data-mce-object")||s===P.transparentSrc||e.remove()};if(!H(i)&&((r=n).data&&!0===r.data.paste))for(var a=e.length;a--;)(s=e[a].attr("src"))&&(0===s.indexOf("webkit-fake-url")?o(e[a]):i.getParam("allow_html_data_urls",!1,"boolean")||0!==s.indexOf("data:")||o(e[a]))})},he=function(e){return P.ie&&e.inline?y.document.body:e.getBody()},ye=function(t,e,n){var r;he(r=t)!==r.getBody()&&t.dom.bind(e,"paste keyup",function(e){Pe(t,n)||t.fire("paste")})},be=function(e){return e.dom.get("mcepastebin")},xe=function(e,t){return t===e},Pe=function(e,t){var n,r=be(e);return(n=r)&&"mcepastebin"===n.id&&xe(t,r.innerHTML)},we=function(e){var t=p(null),n="%MCEPASTEBIN%";return{create:function(){return function(e,t,n){var r=e.dom,o=e.getBody();t.set(e.selection.getRng());var a=e.dom.add(he(e),"div",{id:"mcepastebin","class":"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n);(P.ie||P.gecko)&&r.setStyle(a,"left","rtl"===r.getStyle(o,"direction",!0)?65535:-65535),r.bind(a,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),ye(e,a,n),a.focus(),e.selection.select(a,!0)}(e,t,n)},remove:function(){return function(e,t){if(be(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(e,t)},getEl:function(){return be(e)},getHtml:function(){return function(n){var t=function(e,t){e.appendChild(t),n.dom.remove(t,!0)},e=T.grep(he(n).childNodes,function(e){return"mcepastebin"===e.id}),r=e.shift();T.each(e,function(e){t(r,e)});for(var o=n.dom.select("div[id=mcepastebin]",r),a=o.length-1;0<=a;a--){var i=n.dom.create("div");r.insertBefore(i,o[a]),t(i,o[a])}return r?r.innerHTML:""}(e)},getLastRng:function(){return t.get()},isDefault:function(){return Pe(e,n)},isDefaultContent:function(e){return e===n}}},_e=function(e,t,n){if(r=e,!1!==P.iOS||r===undefined||"function"!=typeof r.setData||!0===U())return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(D,t),!0}catch(o){return!1}var r},Te=function(e,t,n,r){_e(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},Ce=function(s){return function(e,t){var n=k+e,r=s.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),o=s.dom.create("div",{contenteditable:"true"},n);s.dom.setStyles(r,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),r.appendChild(o),s.dom.add(s.getBody(),r);var a=s.selection.getRng();o.focus();var i=s.dom.createRng();i.selectNodeContents(o),s.selection.setRng(i),w.setTimeout(function(){s.selection.setRng(a),r.parentNode.removeChild(r),t()},0)}},De=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},ke=function(e){return!e.selection.isCollapsed()||!!(t=e).dom.getParent(t.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",t.getBody());var t},Se=function(e){var t,n;e.on("cut",(t=e,function(e){ke(t)&&Te(e,De(t),Ce(t),function(){if(P.browser.isChrome()){var e=t.selection.getRng();w.setEditorTimeout(t,function(){t.selection.setRng(e),t.execCommand("Delete")},0)}else t.execCommand("Delete")})})),e.on("copy",(n=e,function(e){ke(n)&&Te(e,De(n),Ce(n),function(){})}))},Oe=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Re=function(e,t){return Oe.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},Ae=function(e,t){e.focus(),e.selection.setRng(t)},Ie=function(i,s,u){i.getParam("paste_block_drop",!1)&&i.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),N(i)||i.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&0<t.files.length&&e.preventDefault()}),i.on("drop",function(e){var t=Re(i,e);if(!e.isDefaultPrevented()&&!u.get()){var n,r=s.getDataTransferItems(e.dataTransfer),o=s.hasContentType(r,D);if(s.hasHtmlOrText(r)&&(!(n=r["text/plain"])||0!==n.indexOf("file://"))||!s.pasteImageData(e,t))if(t&&i.getParam("paste_filter_drop",!0)){var a=r["mce-internal"]||r["text/html"]||r["text/plain"];a&&(e.preventDefault(),w.setEditorTimeout(i,function(){i.undoManager.transact(function(){r["mce-internal"]&&i.execCommand("Delete"),Ae(i,t),a=z(a),r["text/html"]?s.pasteHtml(a,o):s.pasteText(a)})}))}}}),i.on("dragstart",function(e){u.set(!0)}),i.on("dragover dragend",function(e){N(i)&&!1===u.get()&&(e.preventDefault(),Ae(i,Re(i,e))),"dragend"===e.type&&u.set(!1)})};function Ee(t,n){t.on("PastePreProcess",function(e){e.content=n(t,e.content,e.internal,e.wordContent)})}function Fe(e,t){if(!q(t))return t;var n=[];return T.each(e.schema.getBlockElements(),function(e,t){n.push(t)}),t=j(t,[[new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g"),"$1"]]),t=j(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function Me(e,t,n,r){if(r||n)return t;var l,o=e.getParam("paste_webkit_styles");if(!1===e.getParam("paste_remove_styles_if_webkit",!0)||"all"===o)return t;if(o&&(l=o.split(/[, ]/)),l){var c=e.dom,f=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var o=c.parseStyle(c.decode(n)),a={};if("none"===l)return t+r;for(var i=0;i<l.length;i++){var s=o[l[i]],u=c.getStyle(f,l[i],!0);/color/.test(l[i])&&(s=c.toHex(s),u=c.toHex(u)),u!==s&&(a[l[i]]=s)}return(a=c.serializeStyle(a,"span"))?t+' style="'+a+'"'+r:t+r})}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r})}function Ne(n,e){n.$("a",e).find("font,u").each(function(e,t){n.dom.remove(t,!0)})}var Be=function(e){P.webkit&&Ee(e,Me),P.ie&&(Ee(e,Fe),function r(t,n){t.on("PastePostProcess",function(e){n(t,e.node)})}(e,Ne))},$e=function(n,r){return function(t){t.setActive("text"===r.pasteFormat.get());var e=function(e){return t.setActive(e.state)};return n.on("PastePlainTextToggle",e),function(){return n.off("PastePlainTextToggle",e)}}};!function je(){c.add("paste",function(e){if(!1==!(!/(^|[ ,])powerpaste([, ]|$)/.test(e.getParam("plugins"))||!c.get("powerpaste")||("undefined"!=typeof y.window.console&&y.window.console.log&&y.window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),0))){var t=p(!1),n=p(e.getParam("paste_as_text",!1)?"text":"html"),r=(u=n,l=we(s=e),s.on("PreInit",function(){return ve(s,l,u)}),{pasteFormat:u,pasteHtml:function(e,t){return ie(s,e,t)},pasteText:function(e){return se(s,e)},pasteImageData:function(e,t){return me(s,e,t)},getDataTransferItems:ue,hasHtmlOrText:ce,hasContentType:le}),o=Be(e);return i=r,(a=e).ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:$e(a,i)}),a.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:$e(a,i)}),f(e,r),function(e){var t=e.plugins.paste,n=e.getParam("paste_preprocess");n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=e.getParam("paste_postprocess");r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})}(e),Se(e),Ie(e,r,t),{clipboard:r,quirks:o}}var a,i,s,u,l})}()}(window);