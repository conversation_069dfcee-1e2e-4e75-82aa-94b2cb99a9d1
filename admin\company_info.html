<!DOCTYPE html>
<html lang="en">
<head>
    <title class="i18n" name="login.title"></title><meta charset="UTF-8" />
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <!-- 添加管理后台标识 -->
    <meta name="app-type" content="admin-panel" />
    <meta name="client-type" content="admin" />
    <link rel="stylesheet" href="css/bootstrap.min.css"/>
    <link rel="stylesheet" href="css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="css/colorpicker.css"/>
    <link rel="stylesheet" href="css/uniform.css"/>
    <link rel="stylesheet" href="css/fullcalendar.css"/>

    <link rel="stylesheet" href="css/matrix-style.css"/>
    <link rel="stylesheet" href="css/matrix-media.css"/>
    <link rel="stylesheet" href="css/bootstrap-wysihtml5.css"/>
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="css/jquery.gritter.css"/>
    <!-- 先加载jQuery -->
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.cookie.js"></script>
    <script src="js/jquery.i18n.js"></script>
    <script src="js/language.js"></script>
    <style>
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        #content {
            flex: 1;
            margin-left: 0 !important;
            border-left: 1px solid #ddd;
            background: #eee;
        }
        
        /* 修复模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: #000;
            opacity: 0.5;
        }
        
        /* 修复模态框居中问题 */
        .modal {
            width: 560px;
            margin-left: -280px;
            top: 50%;
            margin-top: -250px;
        }
        
        /* 确保通知显示在最上层 */
        #gritter-notice-wrapper {
            z-index: 9999999;
        }
        
        .gritter-item-wrapper {
            z-index: 9999999;
        }

        /* 公司Logo样式 */
        .company-logo {
            max-width: 150px;
            max-height: 100px;
            margin-bottom: 10px;
            min-height: 50px;
            min-width: 50px;
            background-color: #f9f9f9;
            border: 1px dashed #ddd;
            object-fit: contain;
            display: block;
        }
        
        /* 图片预览容器 */
        .image-preview-container {
            margin-bottom: 10px;
            width: 100%;
            text-align: center;
        }
        
        /* 无图片时的样式 */
        .company-logo:not([src]), 
        .company-logo[src=""], 
        .company-logo[src="undefined"] {
            position: relative;
            width: 150px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .company-logo:not([src])::after, 
        .company-logo[src=""]::after, 
        .company-logo[src="undefined"]::after {
            content: "无图片";
            color: #999;
            font-style: italic;
            position: absolute;
        }
        
        /* 图片操作按钮样式 */
        .image-actions {
            margin-top: 5px;
            display: flex;
            gap: 5px;
        }
        
        /* 文本区域样式 */
        textarea.span8 {
            min-height: 100px;
        }
        
        /* 确保模态框中的图片预览样式 */
        #add_image, #edit_image, #del_image {
            max-width: 200px;
            max-height: 150px;
            object-fit: contain;
            border: 1px solid #ddd;
            padding: 3px;
            margin-top: 10px;
        }
        
        /* 修复模态框中的图片上传预览 */
        .image-preview {
            margin-top: 10px;
            text-align: center;
        }
        
        /* 内容预览样式优化 */
        .preview-image {
            margin-bottom: 8px;
            display: block;
        }
        
        .preview-text {
            display: block;
            word-break: break-all;
            color: #333;
            font-size: 12px;
            line-height: 1.5;
            max-height: 60px;
            overflow: hidden;
        }
        
        .preview-empty {
            color: #999;
            font-style: italic;
        }
        
        /* 表格单元格内容垂直居中 */
        .table td {
            vertical-align: middle;
        }
        
        /* 确保图片和文字分开显示 */
        td .preview-image + .preview-text {
            margin-top: 8px;
            border-top: 1px dashed #eee;
            padding-top: 5px;
        }
        
        /* 两栏布局样式 */
        .preview-container {
            display: flex;
            width: 100%;
            border: 1px solid #eee;
            border-radius: 4px;
            overflow: hidden;
            background-color: #f9f9f9;
        }
        
        .preview-column {
            flex: 1;
            padding: 8px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .image-column {
            border-right: 1px solid #ddd;
            background-color: #f5f5f5;
            width: 50%;
            text-align: center;
        }
        
        .text-column {
            width: 50%;
            background-color: #fff;
            padding: 8px;
            overflow: hidden;
        }
        
        .preview-empty {
            color: #999;
            font-style: italic;
            padding: 10px 0;
            text-align: center;
        }
        
        .preview-image img {
            max-width: 90px;
            max-height: 50px;
            display: block;
            margin: 0 auto;
        }
        
        .preview-text {
            width: 100%;
            font-size: 12px;
            line-height: 1.4;
            max-height: 50px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>

<!-- 先加载head.js，确保菜单正确显示 -->
<script src="js/head.js"></script>

<!-- 添加登录验证脚本 -->
<script>
    // 登录验证
    document.addEventListener('DOMContentLoaded', () => {
        // 检查是否已登录
        if (sessionStorage.getItem('adminLoggedIn') !== 'true') {
            // 未登录，跳转到登录页面
            window.location.href = '/login/login.html';
        }
    });
</script>

<!-- 包装容器 -->
<div id="wrapper">
    <!--main-container-part-->
    <div id="content">
        <!--breadcrumbs-->
        <div id="content-header">
            <div id="breadcrumb">
                <a href="admin_home.html" title="返回首页" class="tip-bottom"><i class="icon-home"></i>首页</a>
                <a href="javascript:void(0);" class="current" onclick="return false;">公司详情管理</a></div>
        </div>
        <!--End-breadcrumbs-->

        <!--Action boxes-->
        <div class="container-fluid">
            <div class="widget-box">
                <div class="widget-content">
                    <div style="margin-bottom: 10px;">
                        <select id="lang_filter" class="form-control" style="width: 160px; display: inline-block; margin-right: 8px;">
                            <option value="">中/英文网站</option>
                            <option value="0">中文网站</option>
                            <option value="1">英文网站</option>
                        </select>
                        <select id="info_type_filter" class="form-control" style="width: 160px; display: inline-block; margin-right: 8px;">
                            <option value="">所有类型</option>
                            <option value="company_profile">公司简介</option>
                            <option value="company_logo">公司Logo</option>
                            <option value="address">办公地址</option>
                            <option value="contact">联系方式</option>
                            <option value="product_solution">产品中心</option>
                            <option value="hot_products">热门产品</option>
                            <option value="follow_us">关注我们</option>
                            <option value="service_support">服务与支持</option>
                            <option value="about_banner">关于我们-Banner</option>
                            <option value="technology_highlight">关于我们-公司简介</option>
                            <option value="our_technology">关于我们-我们的技术</option>
                            <option value="wiki_banner">维基教程-大图</option>
                            <option value="wiki_detail">维基教程-详情</option>
                            <option value="navbar">网页导航栏</option>
                            <option value="company_news">贝启科技动态-首页</option>
                            <option value="company_news_detail">贝启科技动态</option>
                            <option value="about_company_home">关于贝启科技-首页</option>
                            <option value="toolbar">工具栏</option>
                            <option value="solution_customize">方案定制</option>
                            <option value="solution_customize_detail">方案定制-详情</option>
                        </select>
                        <a class="btn btn-primary" href="#addInfoAlert" data-toggle="modal"><i class="icon icon-plus i18n" name="common.add"> </i></a>
                        <a class="btn btn-danger" href="javascript:logout()" title="退出登录"><i class="icon icon-off">退出登录 </i></a>
                    </div>

                    <!--  data list -->
                    <div class="widget-box" style="margin-top: 0;">
                        <div class="widget-content nopadding">
                            <table class="table table-bordered table-striped">
                                <thead>
                                <tr>
                                    <th>唯一ID</th>
                                    <th>网站类型</th>
                                    <th>信息类型</th>
                                    <th>标题</th>
                                    <th width="200">内容预览</th>
                                    <th>显示顺序</th>
                                    <th>是否显示</th>
                                    <th>最后操作时间</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody id="data_list">

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- page -->
                    <div class="fg-toolbar ui-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix">
                        <div id="page"
                             class="dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_full_numbers"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑公司详情模态框 -->
        <div id="editInfoAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold" >编辑公司详情</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <form action="#" method="get" class="form-horizontal">
                            <div class="control-group">
                                <label class="control-label">是否显示</label>
                                <div class="controls">
                                    <select class="span8" id="edit_show">
                                        <option value="True" class="i18n" name="common.show">显示</option>
                                        <option value="False" class="i18n" name="common.unshow">不显示</option>
                                    </select>
                                </div>
                            </div>
                            <input type="hidden" id="edit_id">
                            <div class="control-group">
                                <label class="control-label">网站类型</label>
                                <div class="controls">
                                    <select class="span8" id="edit_lang">
                                        <option value="0">中文网站</option>
                                        <option value="1">英文网站</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">信息类型</label>
                                <div class="controls">
                                    <select class="span8" id="edit_info_type">
                                        <option value="company_profile">公司简介</option>
                                        <option value="company_logo">公司Logo</option>
                                        <option value="address">办公地址</option>
                                        <option value="contact">联系方式</option>
                                        <option value="product_solution">产品中心</option>
                                        <option value="hot_products">热门产品</option>
                                        <option value="follow_us">关注我们</option>
                                        <option value="service_support">服务与支持</option>
                                        <option value="about_banner">关于我们-Banner</option>
                                        <option value="technology_highlight">关于我们-公司简介</option>
                                        <option value="our_technology">关于我们-我们的技术</option>
                                        <option value="wiki_banner">维基教程-大图</option>
                                        <option value="wiki_detail">维基教程-详情</option>
                                        <option value="navbar">网页导航栏</option>
                                        <option value="company_news">贝启科技动态-首页</option>
                                        <option value="company_news_detail">贝启科技动态</option>
                                        <option value="about_company_home">关于贝启科技-首页</option>
                                        <option value="toolbar">工具栏</option>
                                        <option value="solution_customize">方案定制</option>
                                        <option value="solution_customize_detail">方案定制-详情</option>
                                    </select>
                                    <span class="help-block">关于我们页面内容类型说明：<br>
                                        - 关于我们-Banner: 页面顶部大图<br>
                                        - 关于我们-公司简介: 公司简介标题和描述<br>
                                        - 关于我们-我们的技术: 技术图标、标题和描述<br>
                                        - 服务与支持: 服务与技术支持内容<br>
                                        - 热门产品: 首页热门产品展示<br>
                                        - 维基教程-大图: 维基教程页面顶部大图<br>
                                        - 维基教程-详情: 维基教程内容项目<br>
                                        - 贝启科技动态-首页: 首页展示的公司动态新闻<br>
                                        - 贝启科技动态: 公司动态新闻详情页<br>
                                        - 关于贝启科技-首页: 首页关于贝启科技部分
                                    </span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">显示顺序</label>
                                <div class="controls" >
                                    <input type="number" class="span8" id="edit_display_order" min="0" placeholder="数字越小越靠前，默认100" value="100" />
                                    <span class="help-block">数字越小越靠前，对于公司简介、关注我们和服务与支持：小的在左侧，大的在右侧；对于产品中心和其他类型：小的在上面；对于关于我们-技术实力和维基教程-可选内容：数字小的排在左侧，数字大的排在右侧</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">标题</label>
                                <div class="controls" >
                                    <input type="text" class="span8" id="edit_title" />
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接地址</label>
                                <div class="controls" >
                                    <input type="text" class="span8" id="edit_url" placeholder="http://..." />
                                    <span class="help-block">主要用于产品中心类型，如不需要可留空</span>
                                </div>
                            </div>
                            <div class="control-group" id="edit_content_group">
                                <label class="control-label">文本内容</label>
                                <div class="controls" >
                                    <textarea class="span8" id="edit_content" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="control-group" id="edit_image_group">
                                <label class="control-label">图片内容</label>
                                <div class="controls">
                                    <div class="image-preview-container">
                                        <img class="thumbnail company-logo" id="edit_image">
                                    </div>
                                    <div class="image-actions">
                                        <input type="file" id="edit_new_image" accept="image/png,image/jpeg,image/gif"/>
                                        <button type="button" class="btn btn-small btn-warning" id="edit_clear_image">清除图片</button>
                                    </div>
                                    <input type="hidden" id="edit_image_removed" value="false">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-primary i18n" name="common.save" href="javascript:edit_info()">保存</a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#">取消</a>
            </div>
        </div>

        <!-- 添加公司详情模态框 -->
        <div id="addInfoAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold" >新增公司详情</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <form action="#" method="get" class="form-horizontal">
                            <div class="control-group">
                                <label class="control-label">是否显示</label>
                                <div class="controls">
                                    <select class="span8" id="add_show">
                                        <option value="True" class="i18n" name="common.show">显示</option>
                                        <option value="False" class="i18n" name="common.unshow">不显示</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">网站类型</label>
                                <div class="controls">
                                    <select class="span8" id="add_lang">
                                        <option value="0">中文网站</option>
                                        <option value="1">英文网站</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">信息类型</label>
                                <div class="controls">
                                    <select class="span8" id="add_info_type" onchange="toggleAddImageContent()">
                                        <option value="company_profile">公司简介</option>
                                        <option value="company_logo">公司Logo</option>
                                        <option value="address">办公地址</option>
                                        <option value="contact">联系方式</option>
                                        <option value="product_solution">产品中心</option>
                                        <option value="hot_products">热门产品</option>
                                        <option value="follow_us">关注我们</option>
                                        <option value="service_support">服务与支持</option>
                                        <option value="about_banner">关于我们-Banner</option>
                                        <option value="technology_highlight">关于我们-公司简介</option>
                                        <option value="our_technology">关于我们-我们的技术</option>
                                        <option value="wiki_banner">维基教程-大图</option>
                                        <option value="wiki_detail">维基教程-详情</option>
                                        <option value="navbar">网页导航栏</option>
                                        <option value="company_news">贝启科技动态-首页</option>
                                        <option value="company_news_detail">贝启科技动态</option>
                                        <option value="about_company_home">关于贝启科技-首页</option>
                                        <option value="toolbar">工具栏</option>
                                        <option value="solution_customize">方案定制</option>
                                        <option value="solution_customize_detail">方案定制-详情</option>
                                    </select>
                                    <span class="help-block">关于我们页面内容类型说明：<br>
                                        - 关于我们-Banner: 页面顶部大图<br>
                                        - 关于我们-公司简介: 公司简介标题和描述<br>
                                        - 关于我们-我们的技术: 技术图标、标题和描述<br>
                                        - 服务与支持: 服务与技术支持内容<br>
                                        - 热门产品: 首页热门产品展示<br>
                                        - 维基教程-大图: 维基教程页面顶部大图<br>
                                        - 维基教程-详情: 维基教程内容项目<br>
                                        - 贝启科技动态-首页: 首页展示的公司动态新闻<br>
                                        - 贝启科技动态: 公司动态新闻详情页<br>
                                        - 关于贝启科技-首页: 首页关于贝启科技部分
                                    </span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">显示顺序</label>
                                <div class="controls" >
                                    <input type="number" class="span8" id="add_display_order" min="0" placeholder="数字越小越靠前，默认100" value="100" />
                                    <span class="help-block">数字越小越靠前，对于公司简介、关注我们和服务与支持：小的在左侧，大的在右侧；对于产品中心和其他类型：小的在上面；对于关于我们-技术实力和维基教程-可选内容：数字小的排在左侧，数字大的排在右侧</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">标题</label>
                                <div class="controls" >
                                    <input type="text" class="span8" id="add_title" />
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接地址</label>
                                <div class="controls" >
                                    <input type="text" class="span8" id="add_url" placeholder="http://..." />
                                    <span class="help-block">主要用于产品中心类型，如不需要可留空</span>
                                </div>
                            </div>
                            <div class="control-group" id="add_content_group">
                                <label class="control-label">文本内容</label>
                                <div class="controls" >
                                    <textarea class="span8" id="add_content" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="control-group" id="add_image_group">
                                <label class="control-label">图片内容</label>
                                <div class="controls">
                                    <div class="image-preview-container">
                                        <img class="thumbnail company-logo" id="add_image">
                                    </div>
                                    <div class="image-actions">
                                        <input type="file" id="add_new_image" accept="image/png,image/jpeg,image/gif"/>
                                        <button type="button" class="btn btn-small btn-warning" id="add_clear_image">清除图片</button>
                                    </div>
                                    <input type="hidden" id="add_image_removed" value="false">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-primary i18n" name="common.save" href="javascript:add_info()">保存</a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#">取消</a>
            </div>
        </div>

        <!-- 删除公司详情确认模态框 -->
        <div id="deleteInfoAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 18px;font-weight:bold">公司详情删除确认</span>
            </div>
            <div class="modal-body">
                <p style="font-size: 18px">
                    <i class="icon icon-warning-sign " style="font-size: 30px;color: red"></i>
                    <span style="margin-left: 20px">是否确认删除唯一ID为</span>
                    <span id="del_id_span" style="color: #1d00ff;font-weight: bold;font-size: 18px;"></span>
                    <span >的公司详情？</span>
                    <hr/>
                    <div>
                        <p><strong>标题：</strong><span id="del_title"></span></p>
                        <p><strong>内容：</strong><span id="del_content"></span></p>
                        <p id="del_image_container" style="display: none;"><strong>图片：</strong></p>
                        <img id='del_image' class="thumbnail company-logo" src="" style="display: none;">
                    </div>
                    <input id="del_id" style="display: none">
                </p>
            </div>
            <div class="modal-footer">
                <a class="btn btn-danger i18n" name="common.del" href="javascript:del_info()">删除</a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#">取消</a>
            </div>
        </div>
    </div>
    <!--end-main-container-part-->
</div>

<!--Footer-part-->
<script src="js/footer.js"></script>
<!--end-Footer-part-->

<!-- 其他脚本 -->
<script src="js/excanvas.min.js"></script>
<script src="js/jquery.ui.custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.peity.min.js"></script>
<script src="js/fullcalendar.min.js"></script>
<script src="js/matrix.js"></script>
<script src="js/jquery.gritter.min.js"></script>
<script src="js/matrix.interface.js"></script>
<script src="js/matrix.chat.js"></script>
<script src="js/jquery.validate.js"></script>
<script src="js/matrix.form_validation.js"></script>
<script src="js/jquery.wizard.js"></script>
<script src="js/jquery.uniform.js"></script>
<script src="js/matrix.popover.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/page.js"></script>
<script src="js/company_info.js"></script>
<script src="js/wiki_manager.js"></script>

<!-- 添加额外的修复脚本 -->
<script>
    // 修复模态框问题
    $(document).ready(function() {
        // 初始化时清理可能存在的模态框背景
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        
        // 修复Bootstrap 2.x模态框问题
        if (typeof $.fn.modal !== 'undefined') {
            // 保存原始modal方法
            var originalModal = $.fn.modal;
            
            // 重写modal方法
            $.fn.modal = function(option) {
                console.log("调用模态框方法:", option);
                
                if (option === 'hide') {
                    // 隐藏模态框时清理背景
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // 调用原始方法
                    return originalModal.apply(this, arguments);
                }
                else if (option === 'show' || typeof option === 'object') {
                    // 显示模态框前清理背景
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // 调用原始方法
                    return originalModal.apply(this, arguments);
                }
                
                // 其他情况调用原始方法
                return originalModal.apply(this, arguments);
            };
        }
        
        // 监听模态框关闭事件，确保背景被移除
        $(document).on('hidden', '.modal', function() {
            console.log("模态框关闭，清理背景");
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        // 监听点击事件，修复灰色遮罩无法点击的问题
        $(document).on('click', '.modal-backdrop', function() {
            console.log("点击了模态框背景，关闭所有模态框");
            $('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        // 监听ESC键，确保模态框可以关闭
        $(document).keyup(function(e) {
            if (e.keyCode === 27) { // ESC键
                console.log("按下ESC键，关闭所有模态框");
                $('.modal').modal('hide');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        });
    });
</script>

<script type="text/javascript">
    // This function is called from the pop-up menus to transfer to
    // a different page. Ignore if the value returned is a null string:
    function goPage(newURL) {
        // if url is empty, skip the menu dividers and reset the menu selection to default
        if (newURL != "") {
            // if url is "-", it is this page -- reset the menu:
            if (newURL == "-") {
                resetMenu();
            }
            // else, send page to designated URL
            else {
                document.location.href = newURL;
            }
        }
    }

    // resets the menu selection upon entry to this page:
    function resetMenu() {
        document.gomenu.selector.selectedIndex = 2;
    }

    $(function () {
        // 确保在页面加载完成后清理模态框背景
        setTimeout(function() {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            console.log("页面加载完成，清理模态框背景");
            
            // 初始化数据
            try {
                console.log("正在调用company_info_list函数...");
                if (typeof company_info_list === 'function') {
                    company_info_list(1, 10);
                    console.log("company_info_list函数调用成功");
                    
                    // 初始化Wiki管理器
                    if (window.wikiManager && typeof window.wikiManager.init === 'function') {
                        window.wikiManager.init();
                        console.log("Wiki管理器初始化成功");
                    }
                } else {
                    console.error("company_info_list函数未定义");
                    alert("加载数据失败：company_info_list函数未定义");
                }
            } catch (e) {
                console.error("初始化数据失败:", e);
                alert("加载数据失败：" + e.message);
            }
        }, 1000);  // 增加延迟时间，确保脚本加载完成
    })
</script>

<script>
    // 页面加载后自动高亮菜单
    $(function () {
        reset_menu();
    });
</script>

<script>
    // 监听筛选下拉框变化，筛选数据
    $(function() {
        $('#lang_filter, #info_type_filter').on('change', function() {
            var lang = $('#lang_filter').val();
            var infoType = $('#info_type_filter').val();
            if (typeof company_info_list === 'function') {
                company_info_list(1, 10, lang, infoType);
            }
        });
    });
    
    // 根据选择的信息类型切换显示图片或内容输入框
    function toggleAddImageContent() {
        var infoType = $('#add_info_type').val();
        // 不再隐藏任何内容，所有类型都可以同时添加文本和图片
        $('#add_content_group').show();
        $('#add_image_group').show();
    }
    
    // 编辑模态框中也需要同样的逻辑
    $('#edit_info_type').on('change', function() {
        // 不再隐藏任何内容，所有类型都可以同时添加文本和图片
        $('#edit_content_group').show();
        $('#edit_image_group').show();
    });
    
    // 监听文件上传控件变化，预览图片
    $('#add_new_image').on('change', function(e) {
        if (e.target.files && e.target.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#add_image').attr('src', e.target.result);
            }
            reader.readAsDataURL(e.target.files[0]);
        }
    });
    
    $('#edit_new_image').on('change', function(e) {
        if (e.target.files && e.target.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#edit_image').attr('src', e.target.result);
            }
            reader.readAsDataURL(e.target.files[0]);
        }
    });
</script>

<script>
function updateCompanyLogo(data) {
    try {
        // 更新顶部导航Logo
        const navbarLogo = document.getElementById('navbar_logo');
        
        // 获取图片路径
        let imgSrc = '';
        
        // 优先使用image_path字段，如果没有则使用content字段
        if (data.image_path) {
            imgSrc = data.image_path;
        } else if (data.content) {
            imgSrc = data.content;
        }
        
        if (imgSrc && navbarLogo) {
            // 处理绝对路径和相对路径
            if (imgSrc.includes('C:')) {
                const fileName = imgSrc.split('\\').pop().split('/').pop();
                imgSrc = '/uploads/' + fileName;
            } else if (!imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
                imgSrc = '/' + imgSrc;
            }
            
            console.log('更新Logo图片路径:', imgSrc);
            navbarLogo.src = imgSrc;
            navbarLogo.onerror = function() {
                this.onerror = null;
                this.src = '/images/home/<USER>'; // 兜底默认logo
                console.log('Logo加载失败，使用默认图片');
            };
        } else {
            console.log('没有找到有效的Logo图片路径');
        }
    } catch (error) {
        console.error('更新公司Logo失败:', error);
    }
}

/**
 * 更新Wiki内容
 * 调用wiki管理器的updateContent函数，更新wiki页面内容
 */
function updateWikiContent() {
    console.log("手动更新Wiki内容");
    
    if (window.wikiManager && typeof window.wikiManager.updateContent === 'function') {
        window.wikiManager.updateContent();
        $.gritter.add({
            title: '操作提示',
            text: '正在更新Wiki内容，请稍候...',
            sticky: false,
            time: 3000
        });
    } else {
        console.error("Wiki管理器未初始化或updateContent函数不存在");
        $.gritter.add({
            title: '错误',
            text: 'Wiki管理器未初始化，请刷新页面后重试',
            sticky: false,
            time: 3000
        });
    }
}
</script>
</body>
</html> 