// 资料下载导航栏管理JS
// 字段：类型、标题、内容、显示顺序、语言、是否显示、更新时间、url

var currentPage = 1;
var pageSize = 20; // 固定每页显示20条
var totalPage = 0;
var currentLang = '';
var currentType = '';

function nav_list(page, size, lang, type) {
    currentPage = page || 1;
    pageSize = 20; // 强制使用20条每页
    currentLang = lang || '';
    currentType = type || '';
    
    var params = { 
        page: currentPage, 
        size: pageSize
    };
    if (currentLang !== '') params.lang = currentLang;
    if (currentType !== '') params.type = currentType;
    
    $.ajax({
        type: "get",
        url: "/apis/download_nav_list/",
        data: params,
        headers: {
            'X-Client-Type': 'admin'
        },
        success: function (data) {
            if (data.status === 'ok') {
                render_nav_list(data.data);
                totalPage = Math.ceil(data.total / pageSize);
                page_ctrl(currentPage, totalPage);
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '加载失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，加载失败', sticky: false, time: 3000 });
        }
    });
}

function render_nav_list(data) {
    var html = '';
    if (!data || data.length === 0) {
        html = '<tr><td colspan="11" class="text-center">暂无数据</td></tr>';
    } else {
        $.each(data, function (index, item) {
            var langText = item.lang == 0 ? '<span class="label label-success">中文</span>' : '<span class="label label-info">英文</span>';
            var bg_style = item.lang == 0 ? ' style="background-color:#d2d2f7"' : '';
            var typeText = item.type === 'nav' ? '导航' : item.type === 'web_image' ? '网页大图' : '产品';
            var isShow = item.show === true || item.show === 1 || item.show === "1";
            var showText = isShow ? '<span class="badge badge-success toggle-show-status" data-id="' + item.id + '" data-show="1" style="cursor:pointer">显示</span>' : '<span class="badge badge-important toggle-show-status" data-id="' + item.id + '" data-show="0" style="cursor:pointer">不显示</span>';
            
            // 修复更新时间的处理逻辑
            var updateTime = '';
            if (item.update_time) {
                try {
                    // 尝试处理不同格式的时间
                    var timestamp;
                    if (typeof item.update_time === 'string') {
                        // 如果是字符串，尝试转换
                        if (item.update_time.includes('T')) {
                            // ISO格式 (2025/1/1T12:00:00)
                            timestamp = new Date(item.update_time);
                        } else if (item.update_time.includes('/')) {
                            // 斜杠格式 (2025/1/1 12:00:00)
                            timestamp = new Date(item.update_time.replace(/-/g, '/'));
                        } else if (!isNaN(item.update_time)) {
                            // 时间戳格式
                            timestamp = new Date(parseInt(item.update_time));
                        } else {
                            // 其他格式
                            timestamp = new Date(item.update_time);
                        }
                    } else if (typeof item.update_time === 'number') {
                        // 如果是数字（时间戳），直接转换
                        timestamp = new Date(item.update_time);
                    }

                    if (timestamp && !isNaN(timestamp.getTime())) {
                        // 格式化时间
                        updateTime = timestamp.getFullYear() + '/' + 
                                   padZero(timestamp.getMonth() + 1) + '/' + 
                                   padZero(timestamp.getDate()) + ' ' + 
                                   padZero(timestamp.getHours()) + ':' + 
                                   padZero(timestamp.getMinutes()) + ':' + 
                                   padZero(timestamp.getSeconds());
                    } else {
                        // 如果时间戳无效,显示原始值
                        updateTime = item.update_time || '';
                    }
                } catch (e) {
                    console.error('时间格式化错误:', e, item.update_time);
                    updateTime = item.update_time || ''; // 如果转换失败，显示原始值
                }
            } else {
                updateTime = ''; // 如果没有更新时间,显示空字符串
            }

            html += '<tr>' +
                '<td' + bg_style + '>' + item.id + '</td>' +
                '<td' + bg_style + '>' + langText + '</td>' +
                '<td' + bg_style + '>' + typeText + '</td>' +
                '<td' + bg_style + '>' + (item.title || '') + '</td>' +
                '<td' + bg_style + '>' + (item.content || '') + '</td>' +
                '<td' + bg_style + '>' + (item.image_path ? '<img src="'+item.image_path+'" style="max-width:120px;max-height:80px;border:1px solid #ccc;" />' : '') + '</td>' +
                '<td' + bg_style + '>' + (item.display_order || 100) + '</td>' +
                '<td' + bg_style + '>' + showText + '</td>' +
                '<td' + bg_style + '>' + updateTime + '</td>' +
                '<td' + bg_style + '>' + (item.url || '') + '</td>' +
                '<td' + bg_style + '>' +
                '<button class="btn btn-primary btn-mini" onclick="show_edit_modal(' + item.id + ')">编辑</button> ' +
                '<button class="btn btn-danger btn-mini" onclick="show_delete_modal(' + item.id + ')">删除</button>' +
                '</td>' +
                '</tr>';
        });
    }
    $("#data_list").html(html);
    // 绑定显示状态切换事件
    $('.toggle-show-status').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        var id = $(this).data('id');
        var currentShow = $(this).data('show');
        var newShow = currentShow == 1 ? false : true;
        toggle_show_status(id, newShow);
    });
}

function show_edit_modal(id) {
    // 先清理可能存在的模态框背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $.ajax({
        type: "get",
        url: "/apis/download_nav_detail/",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                $('#edit_id').val(item.id);
                $('#edit_type').val(item.type);
                $('#edit_title').val(item.title || '');
                $('#edit_content').val(item.content || '');
                $('#edit_display_order').val(item.display_order || 100);
                $('#edit_lang').val(item.lang);
                $('#edit_show').val(item.show ? "True" : "False");
                $('#edit_url').val(item.url || '');
                $('#edit_image_path').val(item.image_path || '');
                if(item.image_path) {
                    $('#edit_image_preview').attr('src', item.image_path).show();
                } else {
                    $('#edit_image_preview').hide();
                }
                // 显示前再次清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                $('#editNavAlert').modal('show');
                setTimeout(function(){ $('#editNavAlert').focus(); }, 200);
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '获取详情失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，获取详情失败', sticky: false, time: 3000 });
        }
    });
}

function edit_nav() {
    // 创建当前时间字符串,格式为YYYY-MM-DD HH:mm:ss
    var now = new Date();
    var currentTime = now.getFullYear() + '-' + 
                    padZero(now.getMonth() + 1) + '-' + 
                    padZero(now.getDate()) + ' ' + 
                    padZero(now.getHours()) + ':' + 
                    padZero(now.getMinutes()) + ':' + 
                    padZero(now.getSeconds());
    
    var formData = {
        id: $('#edit_id').val(),
        type: $('#edit_type').val(),
        title: $('#edit_title').val(),
        content: $('#edit_content').val(),
        display_order: $('#edit_display_order').val(),
        lang: $('#edit_lang').val(),
        show: $('#edit_show').val() === "True" ? 1 : 0,
        url: $('#edit_url').val(),
        image_path: $('#edit_image_path').val(),
        update_time: currentTime // 添加当前时间
    };
    
    console.log('更新导航项,提供update_time:', currentTime);
    
    $.ajax({
        type: "post",
        url: "/apis/update_download_nav/",
        data: formData,
        success: function (data) {
            if (data.status === 'ok') {
                $('#editNavAlert').modal('hide');
                nav_list(currentPage, pageSize, currentLang, currentType);
                $.gritter.add({ title: '成功', text: '更新成功', sticky: false, time: 3000 });
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '更新失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，更新失败', sticky: false, time: 3000 });
        }
    });
}

function add_nav() {
    // 创建当前时间字符串,格式为YYYY-MM-DD HH:mm:ss
    var now = new Date();
    var currentTime = now.getFullYear() + '-' + 
                    padZero(now.getMonth() + 1) + '-' + 
                    padZero(now.getDate()) + ' ' + 
                    padZero(now.getHours()) + ':' + 
                    padZero(now.getMinutes()) + ':' + 
                    padZero(now.getSeconds());
    
    var formData = {
        type: $('#add_type').val(),
        title: $('#add_title').val(),
        content: $('#add_content').val(),
        display_order: $('#add_display_order').val(),
        lang: $('#add_lang').val(),
        show: $('#add_show').val() === "True" ? 1 : 0,
        url: $('#add_url').val(),
        image_path: $('#add_image_path').val(),
        update_time: currentTime // 添加当前时间
    };
    
    console.log('添加导航项,提供update_time:', currentTime);
    
    $.ajax({
        type: "post",
        url: "/apis/add_download_nav/",
        data: formData,
        success: function (data) {
            if (data.status === 'ok') {
                $('#addNavAlert').modal('hide');
                resetAddForm();
                nav_list(1, pageSize, currentLang, currentType);
                $.gritter.add({ title: '成功', text: '添加成功', sticky: false, time: 3000 });
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '添加失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，添加失败', sticky: false, time: 3000 });
        }
    });
}

function show_delete_modal(id) {
    // 先清理可能存在的模态框背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    $.ajax({
        type: "get",
        url: "/apis/download_nav_detail/",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                $('#del_id').val(item.id);
                $('#del_id_span').text(item.id);
                $('#del_title').text(item.title || '');
                $('#del_content').text(item.content || '');
                
                // 显示前再次清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                $('#deleteNavAlert').modal('show');
                setTimeout(function(){ $('#deleteNavAlert').focus(); }, 200);
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '获取详情失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，获取详情失败', sticky: false, time: 3000 });
        }
    });
}

function del_nav() {
    var id = $('#del_id').val();
    $.ajax({
        type: "post",
        url: "/apis/delete_download_nav/",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                $('#deleteNavAlert').modal('hide');
                nav_list(currentPage, pageSize, currentLang, currentType);
                $.gritter.add({ title: '成功', text: '删除成功', sticky: false, time: 3000 });
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '删除失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，删除失败', sticky: false, time: 3000 });
        }
    });
}

function toggle_show_status(id, newShow) {
    $.ajax({
        type: "post",
        url: "/apis/toggle_download_nav_show/",
        data: { id: id, show: newShow },
        success: function (data) {
            if (data.status === 'ok') {
                nav_list(currentPage, pageSize, currentLang, currentType);
                $.gritter.add({ title: '成功', text: '显示状态更新成功', sticky: false, time: 3000 });
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '显示状态更新失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，显示状态更新失败', sticky: false, time: 3000 });
        }
    });
}

function page_ctrl(currentPage, totalPage) {
    var html = "";
    
    // 分页按钮
    html += "<a tabindex='0' class='first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='nav_list(1, " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>首页</a>";
    
    html += "<a tabindex='0' class='previous fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='nav_list(" + (currentPage - 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>上一页</a>";
    
    var startPage = Math.max(1, currentPage - 2);
    var endPage = Math.min(totalPage, startPage + 4);
    
    for (var i = startPage; i <= endPage; i++) {
        html += "<a tabindex='0' class='fg-button ui-button ui-state-default " + 
            (i === currentPage ? "ui-state-disabled" : "") + 
            "' onclick='nav_list(" + i + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>" + i + "</a>";
    }
    
    html += "<a tabindex='0' class='next fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='nav_list(" + (currentPage + 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>下一页</a>";
    
    html += "<a tabindex='0' class='last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='nav_list(" + totalPage + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>尾页</a>";
    
    $("#page").html(html);
}

function updatePageSizeSelector() {
    if ($('#pageSizeSelector').length > 0) {
        $('#pageSizeSelector').val(pageSize);
    }
}

function resetAddForm() {
    $('#add_title').val('');
    $('#add_content').val('');
    $('#add_display_order').val('100');
    $('#add_url').val('');
    $('#add_show').val('True');
    $('#add_lang').val('0');
    $('#add_type').val('nav');
}

function add_nav_modal_show() {
    console.log("添加按钮被点击，正在显示添加弹框...");
    
    // 重置表单
    resetAddForm();
    
    // 显示前先清理背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    // 显示模态框
    console.log("正在显示添加弹框...");
    $('#addNavAlert').modal('show');
    
    // 确保模态框获取焦点
    setTimeout(function() {
        $('#addNavAlert').focus();
        console.log("添加弹框显示完成并获得焦点");
    }, 200);
}

// 监听图片选择并上传，预览图片
$(document).on('change', '#add_image_file', function() {
    var file = this.files[0];
    if (!file) return;
    var formData = new FormData();
    formData.append('file', file);
    $.ajax({
        url: '/apis/upload_pic/',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if(res.status === 'ok' && res.path) {
                $('#add_image_path').val(res.path);
                $('#add_image_preview').attr('src', res.path).show();
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '图片上传失败', sticky: false, time: 3000 });
            }
        },
        error: function() {
            $.gritter.add({ title: '错误', text: '图片上传失败', sticky: false, time: 3000 });
        }
    });
});
$(document).on('change', '#edit_image_file', function() {
    var file = this.files[0];
    if (!file) return;
    var formData = new FormData();
    formData.append('file', file);
    $.ajax({
        url: '/apis/upload_pic/',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if(res.status === 'ok' && res.path) {
                $('#edit_image_path').val(res.path);
                $('#edit_image_preview').attr('src', res.path).show();
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '图片上传失败', sticky: false, time: 3000 });
            }
        },
        error: function() {
            $.gritter.add({ title: '错误', text: '图片上传失败', sticky: false, time: 3000 });
        }
    });
});

$(document).ready(function() {
    // 加载列表数据,固定20条每页
    nav_list(1, 20);
    
    // 绑定筛选事件
    $('#lang_filter').change(function() {
        currentLang = $(this).val();
        nav_list(1, 20, currentLang, currentType); // 重置到第一页,保持20条每页
    });
    
    $('#type_filter').change(function() {
        currentType = $(this).val();
        nav_list(1, 20, currentLang, currentType); // 重置到第一页,保持20条每页
    });
    
    // 修复 hidden 事件监听 - 使用正确的事件名称
    $(document).on('hidden', '.modal', function() {
        console.log("模态框关闭事件触发");
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('padding-right', '');
    });
    
    // 初始清理
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    // 确保编辑和删除按钮的事件处理
    $(document).on('click', '.btn-primary.btn-mini', function() {
        // 清理可能的残留模态框背景
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });
    
    $(document).on('click', '.btn-danger.btn-mini', function() {
        // 清理可能的残留模态框背景
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });

    // -------- 新增：初始化类型下拉框，添加"网页大图"类型 --------
    // 添加弹窗
    if ($('#add_type option[value="web_image"]').length === 0) {
        $('#add_type').append('<option value="web_image">网页大图</option>');
    }
    // 编辑弹窗
    if ($('#edit_type option[value="web_image"]').length === 0) {
        $('#edit_type').append('<option value="web_image">网页大图</option>');
    }
});

// 添加补零函数
function padZero(num) {
    return num < 10 ? '0' + num : num;
} 