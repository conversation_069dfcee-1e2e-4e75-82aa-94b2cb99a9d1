(function($, window, document, undefined) {
	//定义分页类
	function Paging(element, options) {
		this.element = element;
		//传入形参
		this.options = {
			pageNo: options.pageNo||1,
			totalPage: options.totalPage,
			totalSize:options.totalSize,
			callback:options.callback
		};
		//根据形参初始化分页html和css代码
		this.init();
		
		// 安全调用execI18n
		try {
			if (typeof execI18n === 'function') {
				execI18n();
			}
		} catch (e) {
			console.error('执行i18n初始化失败:', e);
		}
	}
	//对Paging的实例对象添加公共的属性和方法
	Paging.prototype = {
		constructor: Paging,
		init: function() {
			this.creatHtml();
			this.bindEvent();
		},
		creatHtml: function() {
			var me = this;
			var content = "";
			var current = parseInt(me.options.pageNo);
			var total = parseInt(me.options.totalPage);
			var totalNum = parseInt(me.options.totalSize);
			var content_prv = "";
			
			// 确保i18n.map存在
			if (typeof $.i18n === 'undefined' || !$.i18n.map) {
				$.i18n = $.i18n || {};
				$.i18n.map = $.i18n.map || {
					'common.start': '首页',
					'common.last_page': '上一页',
					'common.next_page': '下一页',
					'common.end': '尾页'
				};
			}
			
			// 获取国际化文本，如果不存在则使用默认值
			var startText = ($.i18n && $.i18n.map && $.i18n.map['common.start']) || '首页';
			var prevText = ($.i18n && $.i18n.map && $.i18n.map['common.last_page']) || '上一页';
			var nextText = ($.i18n && $.i18n.map && $.i18n.map['common.next_page']) || '下一页';
			var endText = ($.i18n && $.i18n.map && $.i18n.map['common.end']) || '尾页';
			
			if (current===1)
				content_prv = "<a id=\"firstPage\" class='first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default ui-state-disabled'>" + startText + "</a><a id='prePage' class='previous fg-button ui-button ui-state-default ui-state-disabled'>" + prevText + "</a>";
			else
				content_prv = "<a id=\"firstPage\" class='first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default'>" + startText + "</a><a id='prePage' class='previous fg-button ui-button ui-state-default'>" + prevText + "</a>";
			content = "";
			//总页数大于6时候
			if(total > 6) {
				//当前页数小于5时显示省略号
				if(current < 5) {
					for(var i = 1; i < 6; i++) {
						if(current == i) {
							content += "<a class='fg-button ui-button ui-state-default  ui-state-disabled'>" + i + "</a>";
						} else {
							content += "<a class='fg-button ui-button ui-state-default'>" + i + "</a>";
						}
					}
					content += " . . . ";
					content += "<a class='fg-button ui-button ui-state-default'>"+total+"</a>";
				} else {
					 //判断页码在末尾的时候
					if(current < total - 3) {
						for(var i = current - 2; i < current + 3; i++) {
							if(current == i) {
								content += "<a class='fg-button ui-button ui-state-default  ui-state-disabled'>" + i + "</a>";
							} else {
								content += "<a class='fg-button ui-button ui-state-default'>" + i + "</a>";
							}
						}
						content += " . . . ";
						content += "<a class='fg-button ui-button ui-state-default'>"+total+"</a>";
					//页码在中间部分时候
					} else {
						content += "<a class='fg-button ui-button ui-state-default '>1</a>";
						content += " . . . ";
						for(var i = total - 4; i < total + 1; i++) {
							if(current === i) {
								content += "<a class='fg-button ui-button ui-state-default  ui-state-disabled'>" + i + "</a>";
							} else {
								content += "<a class='fg-button ui-button ui-state-default'>" + i + "</a>";
							}
						}
					}
				}
				//页面总数小于6的时候
			} else {
				for(var i = 1; i < total + 1; i++) {
					if(current === i) {
						content += "<a class='fg-button ui-button ui-state-default ui-state-disabled'>" + i + "</a>";
					} else {
						content += "<a class='fg-button ui-button ui-state-default'>" + i + "</a>";
					}
				}
			}
			var content_last = "";
			if (current!=total){
				content_last += "<a id='nextPage' class='next fg-button ui-button ui-state-default'>" + nextText + "</a>";
				content_last += "<a id=\"lastPage\" class='last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default'>" + endText + "</a>";
			}else{
				content_last += "<a id='nextPage' class='next fg-button ui-button ui-state-default ui-state-disabled'>" + nextText + "</a>";
				content_last += "<a id=\"lastPage\" class='last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default ui-state-disabled'>" + endText + "</a>";
			}

			// content += "<span class='totalPages'> 共<span>"+total+"</span>页 </span>";
			// content += "<span class='totalSize'> 共<span>"+totalNum+"</span>条记录 </span>";
			content = "<span>" + content + "</span>";
			me.element.html(content_prv + content + content_last);
			
			// 安全调用execI18n
			try {
				if (typeof execI18n === 'function') {
					execI18n();
				}
			} catch (e) {
				console.error('执行i18n初始化失败:', e);
			}
		},
		//添加页面操作事件
		bindEvent: function() {
			var me = this;
			me.element.off('click', 'a');
			me.element.on('click', 'a', function() {
				var num = $(this).html();
				var id=$(this).attr("id");
				if($(this).hasClass('ui-state-disabled')) return;
				if(id == "prePage") {
					if(me.options.pageNo == 1) {
						me.options.pageNo = 1;
					} else {
						me.options.pageNo = +me.options.pageNo - 1;
					}
				} else if(id == "nextPage") {
					if(me.options.pageNo == me.options.totalPage) {
						me.options.pageNo = me.options.totalPage
					} else {
						me.options.pageNo = +me.options.pageNo + 1;
					}

				} else if(id =="firstPage") {
					me.options.pageNo = 1;
				} else if(id =="lastPage") {
					me.options.pageNo = me.options.totalPage;
				}else{
					me.options.pageNo = +num;
				}
				me.creatHtml();
				if(me.options.callback) {
					me.options.callback(me.options.pageNo);
				}
			});
		}
	};
	//通过jQuery对象初始化分页对象
	$.fn.paging = function(options) {
		return new Paging($(this), options);
	}

})(jQuery, window, document);