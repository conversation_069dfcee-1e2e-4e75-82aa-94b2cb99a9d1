/*

Uniform Theme: Uniform Default
Version: 1.6
By: <PERSON>
License: MIT License
---
For use with the Uniform plugin:
http://pixelmatrixdesign.com/uniform/
---
Generated by Uniform Theme Generator:
http://pixelmatrixdesign.com/uniform/themer.html

*/

/* Global Declaration */

div.selector, 
div.selector span, 
div.checker span,
div.radio span, 
div.uploader, 
div.uploader span.action,
div.button,
div.button span {
  background-image: url(../img/sprite.png);
  background-repeat: no-repeat;
  -webkit-font-smoothing: antialiased;
}

.selector, 
.radio, 
.checker, 
.uploader,
.button, 
.selector *, 
.radio *, 
.checker *, 
.uploader *,
.button *{
  margin: 0;
  padding: 0;
}

/* INPUT & TEXTAREA */



input.text:focus,
input.email:focus,
input.password:focus,
textarea.uniform:focus {
}

/* SPRITES */

/* Select */

div.selector {
  background-position: -483px -130px;
  line-height: 26px;
  height: 26px;
}

div.selector span {
  background-position: right 0px;
  height: 26px;
  line-height: 26px;
}

div.selector select {
  /* change these to adjust positioning of select element */
  top: 0px;
  left: 0px;
}

div.selector:active, 
div.selector.active {
  background-position: -483px -156px;
}

div.selector:active span, 
div.selector.active span {
  background-position: right -26px;
}

div.selector.focus, div.selector.hover, div.selector:hover {
  background-position: -483px -182px;
}

div.selector.focus span, div.selector.hover span, div.selector:hover span {
  background-position: right -52px;
}

div.selector.focus:active,
div.selector.focus.active,
div.selector:hover:active,
div.selector.active:hover {
  background-position: -483px -208px;
}

div.selector.focus:active span,
div.selector:hover:active span,
div.selector.active:hover span,
div.selector.focus.active span {
  background-position: right -78px;
}

div.selector.disabled {
  background-position: -483px -234px;
}

div.selector.disabled span {
  background-position: right -104px;
}

/* Checkbox */

div.checker {
  width: 19px;
  height: 19px;
}

div.checker input {
  width: 19px;
  height: 19px;
}

div.checker span {
  background-position: 0px -260px;
  height: 19px;
  width: 19px;
}

div.checker:active span, 
div.checker.active span {
  background-position: -19px -260px;
}

div.checker.focus span,
div.checker:hover span {
  background-position: -38px -260px;
}

div.checker.focus:active span,
div.checker:active:hover span,
div.checker.active:hover span,
div.checker.focus.active span {
  background-position: -57px -260px;
}

div.checker span.checked {
  background-position: -76px -260px;
}

div.checker:active span.checked, 
div.checker.active span.checked {
  background-position: -95px -260px;
}

div.checker.focus span.checked,
div.checker:hover span.checked {
  background-position: -114px -260px;
}

div.checker.focus:active span.checked,
div.checker:hover:active span.checked,
div.checker.active:hover span.checked,
div.checker.active.focus span.checked {
  background-position: -133px -260px;
}

div.checker.disabled span,
div.checker.disabled:active span,
div.checker.disabled.active span {
  background-position: -152px -260px;
}

div.checker.disabled span.checked,
div.checker.disabled:active span.checked,
div.checker.disabled.active span.checked {
  background-position: -171px -260px;
}

/* Radio */

div.radio {
  width: 18px;
  height: 18px;
}

div.radio input {
  width: 18px;
  height: 18px;
}

div.radio span {
  height: 18px;
  width: 18px;
  background-position: 0px -279px;
}

div.radio:active span, 
div.radio.active span {
  background-position: -18px -279px;
}

div.radio.focus span, 
div.radio:hover span {
  background-position: -36px -279px;
}

div.radio.focus:active span,
div.radio:active:hover span,
div.radio.active:hover span,
div.radio.active.focus span {
  background-position: -54px -279px;
}

div.radio span.checked {
  background-position: -72px -279px;
}

div.radio:active span.checked,
div.radio.active span.checked {
  background-position: -90px -279px;
}

div.radio.focus span.checked, div.radio:hover span.checked {
  background-position: -108px -279px;
}

div.radio.focus:active span.checked, 
div.radio:hover:active span.checked,
div.radio.focus.active span.checked,
div.radio.active:hover span.checked {
  background-position: -126px -279px;
}

div.radio.disabled span,
div.radio.disabled:active span,
div.radio.disabled.active span {
  background-position: -144px -279px;
}

div.radio.disabled span.checked,
div.radio.disabled:active span.checked,
div.radio.disabled.active span.checked {
  background-position: -162px -279px;
}

/* Uploader */

div.uploader {
  background-position: 0px -297px;
  height: 28px;
}

div.uploader span.action {
  background-position: right -409px;
  height: 24px;
  line-height: 24px;
}

div.uploader span.filename {
  height: 24px;
  /* change this line to adjust positioning of filename area */
  margin: 2px 0px 2px 2px;
  line-height: 24px;
}

div.uploader.focus,
div.uploader.hover,
div.uploader:hover {
  background-position: 0px -353px;
}

div.uploader.focus span.action,
div.uploader.hover span.action,
div.uploader:hover span.action {
  background-position: right -437px;
}

div.uploader.active span.action,
div.uploader:active span.action {
  background-position: right -465px;
}

div.uploader.focus.active span.action,
div.uploader:focus.active span.action,
div.uploader.focus:active span.action,
div.uploader:focus:active span.action {
  background-position: right -493px;
}

div.uploader.disabled {
  background-position: 0px -325px;
}

div.uploader.disabled span.action {
  background-position: right -381px;
}

div.button {
  background-position: 0px -523px;
}

div.button span {
  background-position: right -643px;
}

div.button.focus,
div.button:focus,
div.button:hover,
div.button.hover {
  background-position: 0px -553px;
}

div.button.focus span,
div.button:focus span,
div.button:hover span,
div.button.hover span {
  background-position: right -673px; 
}

div.button.active,
div.button:active {
  background-position: 0px -583px;
}

div.button.active span,
div.button:active span {
  background-position: right -703px;
  color: #555;
}

div.button.disabled,
div.button:disabled {
  background-position: 0px -613px;
}

div.button.disabled span,
div.button:disabled span {
  background-position: right -733px;
  color: #bbb;
  cursor: default;
}

/* PRESENTATION */

/* Button */

div.button {
  height: 30px;
}

div.button span {
  margin-left: 13px;
  height: 22px;
  padding-top: 8px;
  font-weight: bold;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  letter-spacing: 1px;
  text-transform: uppercase;
  padding-left: 2px;
  padding-right: 15px;
}

/* Select */
div.selector {
  width: 190px;
  font-size: 12px;
}

div.selector select {
  min-width: 190px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  border: solid 1px #fff;
}

div.selector span {
  padding: 0px 25px 0px 2px;
  cursor: pointer;
}

div.selector span {
  color: #666;
  width: 158px;
  text-shadow: 0 1px 0 #fff;
}

div.selector.disabled span {
  color: #bbb;
}

/* Checker */
div.checker {
  margin-right: 5px;
}

/* Radio */
div.radio {
  margin-right: 3px;
}

/* Uploader */
div.uploader {
  width: 190px;
  cursor: pointer;
}

div.uploader span.action {
  width: 85px;
  text-align: center;
  text-shadow: #fff 0px 1px 0px;
  background-color: #fff;
  font-size: 11px;
  font-weight: bold;
}

div.uploader span.filename {
  color: #777;
  width: 82px;
  border-right: solid 1px #bbb;
  font-size: 11px;
}

div.uploader input {
  width: 190px;
}

div.uploader.disabled span.action {
  color: #aaa;
}

div.uploader.disabled span.filename {
  border-color: #ddd;
  color: #aaa;
}
/*

CORE FUNCTIONALITY 

Not advised to edit stuff below this line
-----------------------------------------------------
*/

.selector, 
.checker, 
.button, 
.radio, 
.uploader {
  display: -moz-inline-box;
  /*display: inline-block;*/  /*by xxj*/
  vertical-align: middle;
  zoom: 1;
  *display: inline;
}

.selector select:focus, .radio input:focus, .checker input:focus, .uploader input:focus {
  outline: 0;
}

/* Button */

div.button a,
div.button button,
div.button input {
  position: absolute;
}

div.button {
  cursor: pointer;
  position: relative;
}

div.button span {
  display: -moz-inline-box;
  display: inline-block;
  line-height: 1;
  text-align: center;
}

/* Select */

div.selector {
  position: relative;
  padding-left: 10px;
  overflow: hidden;
}

div.selector span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

div.selector select {
  position: absolute;
  opacity: 0;
  filter: alpha(opacity:0);
  height: 25px;
  border: none;
  background: none;
}

/* Checker */

div.checker {
  position: relative;
}

div.checker span {
  display: -moz-inline-box;
  display: inline-block;
  text-align: center;
}

div.checker input {
  opacity: 0;
  filter: alpha(opacity:0);
  display: inline-block;
  background: none;
}

/* Radio */

div.radio {
  position: relative;
}

div.radio span {
  display: -moz-inline-box;
  display: inline-block;
  text-align: center;
}

div.radio input {
  opacity: 0;
  filter: alpha(opacity:0);
  text-align: center;
  display: inline-block;
  background: none;
}

/* Uploader */

div.uploader {
  position: relative;
  overflow: hidden;
  cursor: default;
}

div.uploader span.action {
  float: left;
  display: inline;
  padding: 2px 0px;
  overflow: hidden;
  cursor: pointer;
}

div.uploader span.filename {
  padding: 0px 10px;
  float: left;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default;
  position:relative;
}

div.uploader input {
  opacity: 0;
  filter: alpha(opacity:0);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  float: right;
  height: 25px;
  border: none;
  cursor: default;
}
