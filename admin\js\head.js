var _PADCHAR = "=", _ALPHA = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", _VERSION = "1.0";

function _getbyte64(s, i) {
    var idx = _ALPHA.indexOf(s.charAt(i));
    if (idx === -1) {
        throw"Cannot decode base64"
    }
    return idx
}

function _decode(s) {
    var pads = 0, i, b10, imax = s.length, x = [];
    s = String(s);
    if (imax === 0) {
        return s
    }
    if (imax % 4 !== 0) {
        throw"Cannot decode base64"
    }
    if (s.charAt(imax - 1) === _PADCHAR) {
        pads = 1;
        if (s.charAt(imax - 2) === _PADCHAR) {
            pads = 2
        }
        imax -= 4
    }
    for (i = 0; i < imax; i += 4) {
        b10 = (_getbyte64(s, i) << 18) | (_getbyte64(s, i + 1) << 12) | (_getbyte64(s, i + 2) << 6) | _getbyte64(s, i + 3);
        x.push(String.fromCharCode(b10 >> 16, (b10 >> 8) & 255, b10 & 255))
    }
    switch (pads) {
        case 1:
            b10 = (_getbyte64(s, i) << 18) | (_getbyte64(s, i + 1) << 12) | (_getbyte64(s, i + 2) << 6);
            x.push(String.fromCharCode(b10 >> 16, (b10 >> 8) & 255));
            break;
        case 2:
            b10 = (_getbyte64(s, i) << 18) | (_getbyte64(s, i + 1) << 12);
            x.push(String.fromCharCode(b10 >> 16));
            break
    }
    return x.join("")
}

function _getbyte(s, i) {
    var x = s.charCodeAt(i);
    if (x > 255) {
        throw"INVALID_CHARACTER_ERR: DOM Exception 5"
    }
    return x
}

function _encode(s) {
    if (arguments.length !== 1) {
        throw"SyntaxError: exactly one argument required"
    }
    s = String(s);
    var i, b10, x = [], imax = s.length - s.length % 3;
    if (s.length === 0) {
        return s
    }
    for (i = 0; i < imax; i += 3) {
        b10 = (_getbyte(s, i) << 16) | (_getbyte(s, i + 1) << 8) | _getbyte(s, i + 2);
        x.push(_ALPHA.charAt(b10 >> 18));
        x.push(_ALPHA.charAt((b10 >> 12) & 63));
        x.push(_ALPHA.charAt((b10 >> 6) & 63));
        x.push(_ALPHA.charAt(b10 & 63))
    }
    switch (s.length - imax) {
        case 1:
            b10 = _getbyte(s, i) << 16;
            x.push(_ALPHA.charAt(b10 >> 18) + _ALPHA.charAt((b10 >> 12) & 63) + _PADCHAR + _PADCHAR);
            break;
        case 2:
            b10 = (_getbyte(s, i) << 16) | (_getbyte(s, i + 1) << 8);
            x.push(_ALPHA.charAt(b10 >> 18) + _ALPHA.charAt((b10 >> 12) & 63) + _ALPHA.charAt((b10 >> 6) & 63) + _PADCHAR);
            break
    }
    return x.join("")
}


function logout() {
    // 清除登录状态
    sessionStorage.removeItem('adminLoggedIn');
    
    // 清除用户名cookie
    try {
        if (typeof $.cookie === 'function') {
            $.cookie('username', null, { path: '/' });
        }
    } catch (e) {
        console.error('清除cookie失败:', e);
    }
    
    // 调用后端登出接口
    try {
        $.ajax({
            type: "post",
            url: "/apis/logout/",
            async: true,
            timeout: 3000, // 设置超时时间
            complete: function() {
                // 无论成功还是失败，都跳转到登录页面
                window.location.href = "/login/login.html";
            }
        });
    } catch (e) {
        console.error("登出过程中发生错误:", e);
        // 发生任何错误，仍然跳转到登录页面
        window.location.href = "/login/login.html";
    }
}


function menu_ctrl() {
    // console.log(pm_json);
    var usn = $.cookie('username');
    if (usn === undefined || sessionStorage.getItem('adminLoggedIn') !== 'true') {
        window.location.href = "/login/login.html";
        return;
    }

    // 如果没有username cookie但有登录状态，设置一个默认值
    if (!usn && sessionStorage.getItem('adminLoggedIn') === 'true') {
        usn = "管理员";
        $.cookie('username', usn, { path: '/' });
    }

    // 确保i18n.map存在
    if (typeof $.i18n === 'undefined' || !$.i18n.map) {
        $.i18n = $.i18n || {};
        $.i18n.map = $.i18n.map || {};
    }

    // 添加head.welcome和head.lgout的默认值，防止i18n错误
    if (!$.i18n.map["head.welcome"]) {
        $.i18n.map["head.welcome"] = "欢迎";
    }
    if (!$.i18n.map["head.lgout"]) {
        $.i18n.map["head.lgout"] = "退出";
    }
    if (!$.i18n.map["head.overview"]) {
        $.i18n.map["head.overview"] = "概览";
    }

    // 使用现代DOM操作替代document.write()
    const headerHTML = `
        <!--Header-part-->
        <div id='header'>
            <h1><a href='index.html'>Matrix Admin</a></h1>
        </div>
        <!--close-Header-part-->

        <!--top-Header-menu-->
        <div id='search' class='navbar navbar-inverse'>
            <ul class='nav'>
                <select class='pull-left custom_select' id='language' style='width:75px; margin-right: 3px; margin-top: 5px;background-color: transparent; color: #999999'>
                    <option value='zh_CN'>中文</option>
                    <option value='en_US'>English</option>
                </select>
                <li class='dropdown' id='profile-messages'>
                    <a title='' href='#' data-toggle='dropdown' data-target='#profile-messages' class='dropdown-toggle'>
                        <i class='icon icon-user'></i>
                        <span class='text'>${$.cookie('username')} <span class='i18n' name='head.welcome'></span></span>
                        <b class='caret'></b>
                    </a>
                    <ul class='dropdown-menu'>
                        <li><a href='javascript:logout()'><i class='icon-share-alt'></i><span class='i18n' name='head.lgout'></span></a></li>
                    </ul>
                </li>
            </ul>
        </div>
        <!--close-top-Header-menu-->
        <!--start-top-serch-->
        <div id='user-nav'>
        </div>
        <!--close-top-serch-->

        <!--sidebar-menu-->
        <div id='sidebar'>
            <a href='#' class='visible-phone'><i class='icon icon-home'></i> <span class='i18n' name='head.overview'></span></a>
            <ul>
                <li class='submenu'>
                    <a href='#'><i class='icon icon-th-list'></i> <span>管理系统</span></a>
                    <ul>
                        <li><a href='admin_home.html'>首页轮播图片</a></li>
                        <li><a href='product_recommend.html'>产品系列管理</a></li>
                        <li><a href='company_info.html'>公司详情管理</a></li>
                        <li><a href='partner.html'>合作伙伴管理</a></li>
                    </ul>
                </li>
                <li class='submenu'>
                    <a href='javascript:void(0);'><i class='icon icon-file'></i> <span>产品方案</span></a>
                    <ul>
                        <li><a href='product_cls.html'>产品分类</a></li>
                        <li><a href='product_list.html'>产品列表</a></li>
                        <li><a href='product_detail.html'>产品详情</a></li>
                    </ul>
                </li>
                <li class='submenu'>
                    <a href='#'><i class='icon icon-info-sign'></i> <span>技术支持</span></a>
                    <ul>
                        <li><a href='support_cls.html'>技术支持分类</a></li>
                        <li><a href='support_lst.html'>技术支持列表</a></li>
                        <li><a href='download_nav.html'>资料下载分类</a></li>
                        <li><a href='download_detail.html'>资料下载详情</a></li>
                    </ul>
                </li>
            </ul>
        </div>
        <!--sidebar-menu-->
    `;

    // 将HTML插入到body的开头
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = headerHTML;

    // 逐个插入元素到body开头
    while (tempDiv.firstChild) {
        document.body.insertBefore(tempDiv.firstChild, document.body.firstChild);
    }

    // 在菜单生成后初始化菜单功能
    $(document).ready(function(){
        // 为所有submenu添加点击事件
        $('.submenu > a').click(function(e){
            e.preventDefault();
            var $li = $(this).parent('li');
            $li.toggleClass('open');
            
            // 如果当前菜单被打开，关闭其他菜单
            if($li.hasClass('open')){
                $('.submenu').not($li).removeClass('open');
            }
            
            // 切换子菜单的显示/隐藏
            $li.find('ul').slideToggle('fast');
        });

        // 初始化当前页面的菜单状态
        reset_menu();
    });
}

menu_ctrl();

// 原有的set_favicon函数
function set_favicon() {
    //favicon.png
    var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
    link.type = 'image/x-icon';
    link.rel = 'shortcut icon';
    link.href = 'img/favicon.png';
    document.getElementsByTagName('head')[0].appendChild(link);

    // var title = document.querySelector("title") || document.createElement('title');
    // title.innerHTML = '贝启人脸识别系统';
    // if(i18nLanguage==='en_US') title.innerHTML = 'Bearkey Face Recognition System';
    // document.getElementsByTagName('head')[0].appendChild(title);
}

set_favicon();

function arithmetic_crop(){
    var select_html = "";
    $.ajax({
        type: "post",
        url: "/apis/acc/arithmetic_supplier/",
        success: function (data) {
            if (data.status === 'ok') {
                if(data.arithmetic.length > 0){
                    var data_ls = data.arithmetic;

                    $.each(data_ls, function (_, item_data) {
                        select_html += "<option value="+item_data.id+">"+item_data.company+"</option>\n";
                    });
                    $("#logo_select").html(select_html);

                }
            } else {
                var inter_err = '内部服务错误';
                if(i18nLanguage==='en_US') inter_err = 'Internal service error, please contact us';
                var err_msg = data.msg ? data.msg : inter_err;
                $("#reg_error span").html(err_msg);
                $("#reg_error").show();
            }
        }
    });
}

// ======= 菜单插入部分重写 =======
function insertAdminMenu() {
    // 先移除已存在的头部和侧边栏，防止重复
    if(document.getElementById('header')) document.getElementById('header').remove();
    if(document.getElementById('sidebar')) document.getElementById('sidebar').remove();

    // 插入侧边栏
    const sidebarHTML = `
        <div id="sidebar">
            <ul>
                <li class="submenu">
                    <a href="#"><i class="icon icon-th-list"></i> <span>管理系统</span></a>
                    <ul>
                        <li><a href="admin_home.html">首页轮播图片</a></li>
                        <li><a href="product_recommend.html">产品系列管理</a></li>
                        <li><a href="company_info.html">公司详情管理</a></li>
                        <li><a href="partner.html">合作伙伴管理</a></li>
                    </ul>
                </li>
                <li class="submenu">
                    <a href="#"><i class="icon icon-file"></i> <span>产品方案</span></a>
                    <ul>
                        <li><a href="solution_customization.html">方案定制管理</a></li>
                        <li><a href="product_list.html">产品列表</a></li>
                        <li><a href="product_detail.html">产品详情</a></li>
                    </ul>
                </li>
                <li class="submenu">
                    <a href="#"><i class="icon icon-info-sign"></i> <span>技术支持</span></a>
                    <ul>
                        <li><a href="support_cls.html">技术支持分类</a></li>
                        <li><a href="support_lst.html">技术支持列表</a></li>
                        <li><a href="download_nav.html">资料下载分类</a></li>
                        <li><a href="download_detail.html">资料下载详情</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    `;
    const wrapper = document.getElementById('wrapper');
    if (wrapper) {
        const sidebarContainer = document.createElement('div');
        sidebarContainer.innerHTML = sidebarHTML;
        wrapper.insertBefore(sidebarContainer.firstElementChild, wrapper.firstChild);
    }

    // 插入头部
    const headerHTML = `
        <div id="header">
            <h1><a href="admin_home.html"></a></h1>
        </div>
    `;
    const headerContainer = document.createElement('div');
    headerContainer.innerHTML = headerHTML;
    document.body.insertBefore(headerContainer.firstElementChild, document.body.firstChild);

    // 为菜单添加点击事件
    $('.submenu > a').on('click', function(e){
        e.preventDefault();
        var $li = $(this).parent('li');
        $li.toggleClass('open');
        
        // 如果当前菜单被打开，关闭其他菜单
        if($li.hasClass('open')){
            $('.submenu').not($li).removeClass('open');
        }
        
        // 切换子菜单的显示/隐藏
        $li.find('ul').slideToggle('fast');
    });

    // 菜单插入后自动高亮
    if (typeof reset_menu === 'function') {
        setTimeout(reset_menu, 50);
    }
}

// 页面加载时插入菜单
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        if(typeof $ === 'function') {
            insertAdminMenu();
        } else {
            console.error('jQuery not loaded before menu initialization');
        }
    });
} else {
    if(typeof $ === 'function') {
        insertAdminMenu();
    } else {
        console.error('jQuery not loaded before menu initialization');
    }
}

// 菜单高亮显示逻辑
function reset_menu(){
    // 获取当前页面的URL路径
    var currentPath = window.location.pathname;
    var pageName = currentPath.split('/').pop();
    
    // 先移除所有激活状态
    $('.submenu').removeClass('active open');
    $('.submenu li').removeClass('active');
    
    // 根据页面名称设置高亮
    if (pageName === 'admin_home.html') {
        $('.submenu:first-child').addClass('active open');
        $('.submenu:first-child li:first-child').addClass('active');
    } else if (pageName === 'product_recommend.html') {
        $('.submenu:first-child').addClass('active open');
        $('.submenu:first-child li:nth-child(2)').addClass('active');
    } else if (pageName === 'company_info.html') {
        $('.submenu:first-child').addClass('active open');
        $('.submenu:first-child li:nth-child(3)').addClass('active');
    } else if (pageName === 'partner.html') {
        $('.submenu:first-child').addClass('active open');
        $('.submenu:first-child li:nth-child(4)').addClass('active');
    } else if (pageName === 'solution_customization.html') {
        $('.submenu:nth-child(2)').addClass('active open');
        $('.submenu:nth-child(2) li:first-child').addClass('active');
        // 确保子菜单显示
        $('.submenu:nth-child(2) ul').show();
    } else if (pageName === 'product_list.html') {
        $('.submenu:nth-child(2)').addClass('active open');
        $('.submenu:nth-child(2) li:nth-child(2)').addClass('active');
        // 确保子菜单显示
        $('.submenu:nth-child(2) ul').show();
    } else if (pageName === 'product_detail.html') {
        $('.submenu:nth-child(2)').addClass('active open');
        $('.submenu:nth-child(2) li:nth-child(3)').addClass('active');
        // 确保子菜单显示
        $('.submenu:nth-child(2) ul').show();
    } else if (pageName === 'support_cls.html') {
        $('.submenu:nth-child(3)').addClass('active open');
        $('.submenu:nth-child(3) li:first-child').addClass('active');
    } else if (pageName === 'support_lst.html') {
        $('.submenu:nth-child(3)').addClass('active open');
        $('.submenu:nth-child(3) li:nth-child(2)').addClass('active');
    } else if (pageName === 'download_nav.html') {
        $('.submenu:nth-child(3)').addClass('active open');
        $('.submenu:nth-child(3) li:nth-child(3)').addClass('active');
    } else if (pageName === 'download_detail.html') {
        $('.submenu:nth-child(3)').addClass('active open');
        $('.submenu:nth-child(3) li:nth-child(4)').addClass('active');
    }
}

// 同步时间记录
function syncTimeRecords() {
    alert('时间记录已同步');
}

// 加载模态框修复脚本 - 使用现代方式动态加载
function loadModalFixScript() {
    const script = document.createElement('script');
    script.src = '/admin/js/modal-fix.js';
    script.type = 'text/javascript';
    script.async = true;

    // 添加错误处理
    script.onerror = function() {
        console.warn('modal-fix.js 加载失败，但不影响主要功能');
    };

    document.head.appendChild(script);
}

// 页面加载完成后加载脚本
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadModalFixScript);
} else {
    loadModalFixScript();
}
