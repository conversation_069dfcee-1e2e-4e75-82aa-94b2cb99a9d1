/**
 * 公司详情管理API示例
 * 
 * 这个文件展示了如何实现支持显示顺序的公司详情API
 * 实际部署时，请根据您的后端框架(如Express, Koa等)进行适配
 */

const mysql = require('mysql2/promise');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'password',
  database: 'company_website'
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({ storage: storage });

/**
 * 获取公司详情列表
 * 
 * 请求参数:
 * - page: 页码
 * - size: 每页条数
 * - lang: 语言筛选(可选)
 * - info_type: 信息类型筛选(可选)
 * 
 * 响应:
 * {
 *   status: 'ok',
 *   data: [...],
 *   total: 100
 * }
 */
async function getCompanyInfoList(req, res) {
  try {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 10;
    const offset = (page - 1) * size;
    
    let whereClause = '1=1';
    const params = [];
    
    // 添加筛选条件
    if (req.query.lang !== undefined && req.query.lang !== '') {
      whereClause += ' AND lang = ?';
      params.push(req.query.lang);
    }
    
    if (req.query.info_type) {
      whereClause += ' AND info_type = ?';
      params.push(req.query.info_type);
    }
    
    // 查询总数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM company_info WHERE ${whereClause}`,
      params
    );
    const total = countResult[0].total;
    
    // 查询数据，按显示顺序排序
    const [rows] = await pool.query(
      `SELECT * FROM company_info WHERE ${whereClause} ORDER BY display_order, id LIMIT ? OFFSET ?`,
      [...params, size, offset]
    );
    
    // 处理空值，确保前端不会收到null值
    const processedRows = rows.map(row => ({
      ...row,
      title: row.title || '',
      content: row.content || '',
      image_path: row.image_path || '',
      display_order: row.display_order || 100
    }));
    
    res.json({
      status: 'ok',
      data: processedRows,
      total: total
    });
  } catch (error) {
    console.error('获取公司详情列表失败:', error);
    res.status(500).json({
      status: 'error',
      msg: '获取公司详情列表失败'
    });
  }
}

/**
 * 获取公司详情
 * 
 * 请求参数:
 * - id: 公司详情ID
 * 
 * 响应:
 * {
 *   status: 'ok',
 *   data: {...}
 * }
 */
async function getCompanyInfoDetail(req, res) {
  try {
    const id = req.query.id;
    
    if (!id) {
      return res.status(400).json({
        status: 'error',
        msg: '缺少ID参数'
      });
    }
    
    const [rows] = await pool.query(
      'SELECT * FROM company_info WHERE id = ?',
      [id]
    );
    
    if (rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        msg: '公司详情不存在'
      });
    }
    
    // 处理空值
    const processedRow = {
      ...rows[0],
      title: rows[0].title || '',
      content: rows[0].content || '',
      image_path: rows[0].image_path || '',
      display_order: rows[0].display_order || 100
    };
    
    res.json({
      status: 'ok',
      data: processedRow
    });
  } catch (error) {
    console.error('获取公司详情失败:', error);
    res.status(500).json({
      status: 'error',
      msg: '获取公司详情失败'
    });
  }
}

/**
 * 添加公司详情
 * 
 * 请求参数:
 * - lang: 语言(0-中文,1-英文)
 * - info_type: 信息类型
 * - title: 标题(可选)
 * - content: 内容(可选)
 * - image: 图片文件(可选)
 * - display_order: 显示顺序(可选，默认100)
 * - show: 是否显示
 * 
 * 响应:
 * {
 *   status: 'ok',
 *   data: {id: 1}
 * }
 */
async function addCompanyInfo(req, res) {
  try {
    const { lang, info_type, title, content, display_order, show } = req.body;
    
    // 获取图片路径(如果有上传图片)
    let imagePath = null;
    if (req.file) {
      imagePath = path.join('uploads', req.file.filename).replace(/\\/g, '/');
    }
    
    // 插入数据 - 允许所有字段为空
    const [result] = await pool.query(
      `INSERT INTO company_info (lang, info_type, title, content, image_path, display_order, show) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        lang || 0, 
        info_type || 'company_profile', 
        title || null, 
        content || null, 
        imagePath, 
        display_order || 100, 
        show === 'true' || show === true
      ]
    );
    
    res.json({
      status: 'ok',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('添加公司详情失败:', error);
    res.status(500).json({
      status: 'error',
      msg: '添加公司详情失败: ' + error.message
    });
  }
}

/**
 * 更新公司详情
 * 
 * 请求参数:
 * - id: 公司详情ID
 * - lang: 语言(0-中文,1-英文)
 * - info_type: 信息类型
 * - title: 标题(可选)
 * - content: 内容(可选)
 * - url: 链接(可选)
 * - image_path: 图片路径(可选)
 * - display_order: 显示顺序(可选)
 * - show: 是否显示
 * 
 * 响应:
 * {
 *   status: 'ok'
 * }
 */
async function updateCompanyInfo(req, res) {
  try {
    const { id, lang, info_type, title, content, url, image_path, display_order, show } = req.body;
    
    if (!id) {
      return res.status(400).json({
        status: 'error',
        msg: '缺少ID参数'
      });
    }
    
    // 构建更新字段
    const updateFields = [];
    const params = [];
    
    // 只更新前端提供的字段，未提供的字段保持不变
    if (lang !== undefined) {
      updateFields.push('lang = ?');
      params.push(lang);
    }
    
    if (info_type !== undefined) {
      updateFields.push('info_type = ?');
      params.push(info_type);
    }
    
    // 只有当title被明确提供时才更新
    if (title !== undefined) {
      updateFields.push('title = ?');
      params.push(title || null);
    }
    
    // 只有当content被明确提供时才更新
    if (content !== undefined) {
      updateFields.push('content = ?');
      params.push(content || null);
    }
    
    // 只有当url被明确提供时才更新
    if (url !== undefined) {
      updateFields.push('url = ?');
      params.push(url || null);
    }
    
    // 显示顺序
    if (display_order !== undefined) {
      updateFields.push('display_order = ?');
      params.push(display_order);
    }
    
    // 显示状态
    if (show !== undefined) {
      updateFields.push('show = ?');
      params.push(show === 'true' || show === true);
    }
    
    // 如果前端明确提供了image_path参数
    if (image_path !== undefined) {
      updateFields.push('image_path = ?');
      params.push(image_path || null);
    }
    
    // 获取图片路径(如果有上传图片)
    if (req.file) {
      const imagePath = path.join('uploads', req.file.filename).replace(/\\/g, '/');
      updateFields.push('image_path = ?');
      params.push(imagePath);
    }
    
    // 如果没有任何字段需要更新，直接返回成功
    if (updateFields.length === 0) {
      return res.json({
        status: 'ok',
        msg: '没有字段需要更新'
      });
    }
    
    // 添加ID到参数列表
    params.push(id);
    
    // 执行更新
    await pool.query(
      `UPDATE company_info SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );
    
    // 如果更新了显示顺序，可以选择调用存储过程重新排序
    if (display_order !== undefined) {
      try {
        await pool.query(
          'CALL update_company_info_order(?, ?)',
          [id, display_order]
        );
      } catch (err) {
        console.warn('调用显示顺序存储过程失败:', err);
        // 不中断主流程，继续执行
      }
    }
    
    res.json({
      status: 'ok'
    });
  } catch (error) {
    console.error('更新公司详情失败:', error);
    res.status(500).json({
      status: 'error',
      msg: '更新公司详情失败: ' + error.message
    });
  }
}

/**
 * 删除公司详情
 * 
 * 请求参数:
 * - id: 公司详情ID
 * 
 * 响应:
 * {
 *   status: 'ok'
 * }
 */
async function deleteCompanyInfo(req, res) {
  try {
    const { id } = req.body;
    
    if (!id) {
      return res.status(400).json({
        status: 'error',
        msg: '缺少ID参数'
      });
    }
    
    // 获取图片路径(用于删除文件)
    const [rows] = await pool.query(
      'SELECT image_path FROM company_info WHERE id = ?',
      [id]
    );
    
    // 删除数据
    await pool.query(
      'DELETE FROM company_info WHERE id = ?',
      [id]
    );
    
    // 如果有图片，尝试删除文件
    if (rows.length > 0 && rows[0].image_path) {
      const imagePath = path.join(__dirname, '..', rows[0].image_path);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }
    
    res.json({
      status: 'ok'
    });
  } catch (error) {
    console.error('删除公司详情失败:', error);
    res.status(500).json({
      status: 'error',
      msg: '删除公司详情失败: ' + error.message
    });
  }
}

/**
 * 更新显示顺序
 * 
 * 请求参数:
 * - id: 公司详情ID
 * - display_order: 新的显示顺序
 * 
 * 响应:
 * {
 *   status: 'ok'
 * }
 */
async function updateDisplayOrder(req, res) {
  try {
    const { id, display_order } = req.body;
    
    if (!id || display_order === undefined) {
      return res.status(400).json({
        status: 'error',
        msg: '缺少必要参数'
      });
    }
    
    // 调用存储过程更新显示顺序
    await pool.query(
      'CALL update_company_info_order(?, ?)',
      [id, display_order]
    );
    
    res.json({
      status: 'ok'
    });
  } catch (error) {
    console.error('更新显示顺序失败:', error);
    res.status(500).json({
      status: 'error',
      msg: '更新显示顺序失败: ' + error.message
    });
  }
}

/**
 * 仅更新显示状态
 * 
 * 请求参数:
 * - id: 公司详情ID
 * - show: 是否显示
 * 
 * 响应:
 * {
 *   status: 'ok'
 * }
 */
async function updateShowStatusOnly(req, res) {
  try {
    const { id, show } = req.body;
    
    if (!id || show === undefined) {
      return res.status(400).json({
        status: 'error',
        msg: '缺少必要参数'
      });
    }
    
    // 只更新show字段，不修改其他任何字段
    await pool.query(
      'UPDATE company_info SET show = ? WHERE id = ?',
      [show === 'true' || show === true, id]
    );
    
    res.json({
      status: 'ok'
    });
  } catch (error) {
    console.error('更新显示状态失败:', error);
    res.status(500).json({
      status: 'error',
      msg: '更新显示状态失败: ' + error.message
    });
  }
}

// 导出API函数
module.exports = {
  getCompanyInfoList,
  getCompanyInfoDetail,
  addCompanyInfo: [upload.single('image'), addCompanyInfo],
  updateCompanyInfo: [upload.single('image'), updateCompanyInfo],
  deleteCompanyInfo,
  updateDisplayOrder,
  updateShowStatusOnly
};

/**
 * 路由配置示例(Express)
 * 
 * const express = require('express');
 * const router = express.Router();
 * const companyInfoAPI = require('./company_info_api');
 * 
 * // 获取公司详情列表
 * router.get('/company_info/list', companyInfoAPI.getCompanyInfoList);
 * 
 * // 获取公司详情
 * router.get('/company_info/detail', companyInfoAPI.getCompanyInfoDetail);
 * 
 * // 添加公司详情
 * router.post('/company_info/add', companyInfoAPI.addCompanyInfo);
 * 
 * // 更新公司详情
 * router.post('/company_info/update', companyInfoAPI.updateCompanyInfo);
 * 
 * // 删除公司详情
 * router.post('/company_info/delete', companyInfoAPI.deleteCompanyInfo);
 * 
 * // 更新显示顺序
 * router.post('/company_info/updateOrder', companyInfoAPI.updateDisplayOrder);
 * 
 * // 仅更新显示状态
 * router.post('/company_info/updateShowStatusOnly', companyInfoAPI.updateShowStatusOnly);
 * 
 * module.exports = router;
 */ 