<!DOCTYPE html>
<html lang="en">
<head>
    <title class="i18n" name="login.title"></title>
    <meta charset="UTF-8"/>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="css/bootstrap.min.css"/>
    <link rel="stylesheet" href="css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="css/colorpicker.css"/>
    <!--<link rel="stylesheet" href="css/datepicker.css"/>-->
    <link rel="stylesheet" href="css/uniform.css"/>
    <!--<link rel="stylesheet" href="css/select2.css"/>-->
    <link rel="stylesheet" href="css/fullcalendar.css"/>

    <link rel="stylesheet" href="css/matrix-style.css"/>
    <link rel="stylesheet" href="css/matrix-media.css"/>
    <link rel="stylesheet" href="css/bootstrap-wysihtml5.css"/>
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="css/jquery.gritter.css"/>
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.cookie.js"></script>
    <script src="tinymce/tinymce.min.js"></script>
    <!--<link rel="stylesheet" href="css/page.css"/>-->
</head>
<body>

<script src="js/jquery.min.js"></script>
<!--<script src="js/jquery.i18n.js"></script>-->
<!--<script src="js/language.js"></script>-->
<script src="js/jquery.cookie.js"></script>
<script src="js/head.js"></script>
<!--main-container-part-->
<div id="content">
    <!--breadcrumbs-->
    <div id="content-header">
        <div id="breadcrumb">
            <!--<a href="index.html" title="Go to Home" class="tip-bottom"><i class="icon-home"></i>Home</a>-->
            <a href="#" class="current" id="pname"></a></div>
    </div>
    <!--End-breadcrumbs-->

    <!--Action boxes-->
    <div class="container-fluid">

        <div class="widget-box">
            <div class="widget-content tab-content">
                <a class="btn btn-primary" href="product.html"> <
                    <返回
                </a>
                <a class="btn btn-primary" href="javascript:update_detail()"> 保存</a>
                <select  class="input-medium" style="margin-bottom: 0;width: auto"  id="sel_paib" >
                    <option value="1">排版方式：全屏显示（全图片推荐）</option>
                    <option value="0">排版方式：居中显示（旧版本，图文混编推荐）</option>
                </select>
                <a class="btn btn-success" href="#makeHtml" data-toggle="modal"><i class="icon icon-th"> 生成html </i></a>
                <span style="font-size: 14px;margin-left: 50px;color: red">注：上传图片后，将图片宽度改为"100%"，高清空</span>

                <input id="pid" style="display: none">
                <input id="old_content" style="display: none">
                <div id="tinymce_demo"></div>
                <!--
                <div class="pull-right">
                    <br>
                    <hr>
                    <button type="button" onclick="setcontent()">填入数据</button>
                    <button type="button" onclick="getcontent()">读取数据1</button>
                    <button type="button" onclick="getbody()">获取纯文本</button>
                </div>
                -->

            </div>
        </div>
    </div>
    <div id="makeHtml" class="modal hide">
        <div class="modal-header">
            <button data-dismiss="modal" class="close" type="button">×</button>
            <span style="font-size: 18px;font-weight:bold">产品详情网页重新生成确认</span>
        </div>
        <div class="modal-body">
            <p>
                <i class="icon icon-warning-sign " style="font-size: 30px;color: red"></i>
                <span style="margin-left: 18px;font-size: 18px;">是否确认重新生成详情网页？</span>
                <br/>
                <span style="margin-left: 18px;font-size: 18px; color: red">请确保已保存，再生成</span>

            </p>
        </div>
        <div class="modal-footer">
            <a class="btn btn-danger " href="javascript:make_product_html()">确认</a>
            <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#"></a>
        </div>
    </div>
</div>

<!--end-main-container-part-->
<script src="js/jquery.i18n.js"></script>
<script src="js/language.js"></script>
<!--Footer-part-->

<script src="js/footer.js"></script>
<!--end-Footer-part-->

<script src="js/excanvas.min.js"></script>
<!--<script src="js/jquery.min.js"></script>-->
<script src="js/jquery.ui.custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<!--<script src="js/jquery.flot.min.js"></script>-->
<!--<script src="js/jquery.flot.resize.min.js"></script>-->
<script src="js/jquery.peity.min.js"></script>
<script src="js/fullcalendar.min.js"></script>
<script src="js/matrix.js"></script>
<!--<script src="js/matrix.dashboard.js"></script>-->
<script src="js/jquery.gritter.min.js"></script>
<script src="js/matrix.interface.js"></script>
<script src="js/matrix.chat.js"></script>
<script src="js/jquery.validate.js"></script>
<script src="js/matrix.form_validation.js"></script>
<script src="js/jquery.wizard.js"></script>
<script src="js/jquery.uniform.js"></script>
<!--<script src="js/select2.min.js"></script>-->
<script src="js/matrix.popover.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<!--<script src="js/matrix.tables.js"></script>-->
<script src="js/jquery.cookie.js"></script>
<script src="js/page.js"></script>
<!--<script src="js/matrix.interface.js"></script>-->

<script>
    tinymce.init({
        selector: '#tinymce_demo', //容器，可使用css选择器
        language: 'zh_CN', //调用放在langs文件夹内的语言包
        toolbar: true, //工具栏
        menubar: true, //菜单栏
        branding: false, //右下角技术支持
        inline: false, //开启内联模式
        elementpath: false,
        min_height: 400, //最小高度
        height: 800,  //高度
        skin: 'oxide',
        toolbar_sticky: true,
        visualchars_default_state: true, //显示不可见字符
        image_caption: true,
        paste_data_images: true,
        relative_urls: false,
        // remove_script_host : false,
        removed_menuitems: 'newdocument',  //清除“文件”菜单
        plugins: "lists,hr, advlist,anchor,autolink,autoresize,charmap,code,codesample,emoticons,fullscreen,image,media,insertdatetime,link,pagebreak,paste,preview,print,searchreplace,table,textcolor,toc,visualchars,wordcount", //依赖lists插件
        toolbar: 'bullist numlist anchor charmap emoticons fullscreen hr image insertdatetime link media pagebreak paste preview print searchreplace textcolor wordcount',
        //选中时出现的快捷工具，与插件有依赖关系
        images_upload_url: '/apis/upload_pic/', /*后图片上传接口*/ /*返回值为json类型 {'location':'uploads/jpg'}*/
        init_instance_callback: 'initData',
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        }

    });

</script>
<script type="text/javascript">
    // This function is called from the pop-up menus to transfer to
    // a different page. Ignore if the value returned is a null string:
    function goPage(newURL) {

        // if url is empty, skip the menu dividers and reset the menu selection to default
        if (newURL != "") {

            // if url is "-", it is this page -- reset the menu:
            if (newURL == "-") {
                resetMenu();
            }
            // else, send page to designated URL
            else {
                document.location.href = newURL;
            }
        }
    }

    // resets the menu selection upon entry to this page:
    function resetMenu() {
        document.gomenu.selector.selectedIndex = 2;
    }

    // sildebar change active when onclick

</script>

<!-- 例子 -->
<script>
    /*初始化数据*/
    function initData(instance) {
        //
        var pid = $.cookie('pid');
        if (pid === undefined || pid === '') {
            show_gitter('提示信息', '信息过期，请返回重新进入', 2);
            self.location = 'product.html'
            return
        }

        var filters = {id:pid};
        var fileds = ['html_content'];
        var html_content = '';
        $.ajax({
            type: "post",
            url: "/apis/get_product/",
            async: false,
            data: {page: 1, page_size:10, order_by:JSON.stringify(['-id']),filters:JSON.stringify(filters),fileds:JSON.stringify(fileds)},
            success: function (data) {
                if (data.status === 'ok') {
                    var data_list = data.data_list;
                    if (data_list.length>0){
                        html_content = data_list[0].html_content;
                    }
                } else {
                    var err_msg  = data.msg ? data.msg : '内部服务错误';
                    show_gitter('错误提示', err_msg, 2);

                    }
                }
            });
        if (instance != null) {
            tinyMCE.activeEditor.setContent(html_content);
        }
    }

    /*填入初始数据*/
    //tinyMCE.activeEditor.setContent("<h1>测试</h1><hr><h2>这是测试的数据<h2>");
    /*
    1、如果当前页面只有一个编辑器：
        获取内容：tinyMCE.activeEditor.getContent()
        设置内容：tinyMCE.activeEditor.setContent(“需要设置的编辑器内容”)
    2、如果当前页面有多个编辑器（下面的“[0]”表示第一个编辑器，以此类推）：
        获取内容：tinyMCE.editors[0].getContent()
        设置内容：tinyMCE.editors[0].setContent(“需要设置的编辑器内容”)
    */
    function setcontent() {
        tinyMCE.activeEditor.setContent("<h1>设置内容1</h1>");
        //tinyMCE.editors[0].setContent("<h1>设置内容2</h1>");
    }

    function getcontent() {
        alert(tinyMCE.activeEditor.getContent());
    }

    /*3、获取不带HTML标记的纯文本内容：
     var activeEditor = tinymce.activeEditor;
     var editBody = activeEditor.getBody();
     activeEditor.selection.select(editBody);
     var text = activeEditor.selection.getContent( {'format' : 'text' } );*/
    function getbody() {
        var activeEditor = tinymce.activeEditor;
        var editBody = activeEditor.getBody();
        activeEditor.selection.select(editBody);
        var text = activeEditor.selection.getContent({'format': 'text'});
        alert(text);
    }
</script>
<script>
    function make_product_html() {

        var pid = $.cookie('pid');
        var sel_paib = $('#sel_paib').val();
        if (pid === undefined || pid === '') {
            show_gitter('提示信息', '信息过期，请返回重新进入', 2);
            self.location = 'product.html'
            return
        }
        $.ajax({
        type: "post",
        url: "/apis/make_product_html/",
        data:{pid:pid, paib:sel_paib},
        async: false,
        success: function (data) {
            if (data.status === 'ok') {
                // todo 刷新列表
                $('#makeHtml').modal('hide');
                show_gitter('提示信息', 'html生成成功，请前往官网查看', 1);
            } else {
                    var err_msg  = data.msg ? data.msg : '内部服务错误';
                    show_gitter('错误提示', err_msg, 2);
                }
            }
        });
    }

    function update_detail() {
        var pid = $.cookie('pid');
        var html_content = tinyMCE.activeEditor.getContent(); //$.cookie('html_content');
        var new_content = tinyMCE.activeEditor.getContent();
        if (pid === undefined || pid === '') {
            show_gitter('提示信息', '信息过期，请返回重新进入', 2);
            return
        }


        // todo check pls name unique

        var filters = JSON.stringify({'id': pid});

        $.ajax({
            type: "post",
            url: "/apis/update_product/",
            async: false,
            data: {filters: filters, html_content: new_content, old_html_content: html_content},
            success: function (data) {
                if (data.status === 'ok') {
                    show_gitter('提示信息', '产品详情更新成功', 1);
                } else {
                    if (i18nLanguage === 'en_US') {
                        var err_msg = data.msg ? data.msg : 'Internal service error, please contact us';
                        show_gitter('Error', err_msg, 2);
                    } else {
                        var err_msg = data.msg ? data.msg : '内部服务错误';
                        show_gitter('错误提示', err_msg, 2);
                    }
                }
            }
        });
    }
</script>
</body>
</html>
