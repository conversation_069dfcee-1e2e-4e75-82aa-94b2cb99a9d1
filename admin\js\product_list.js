/**
 * 产品列表管理JS
 * 实现产品列表的增删改查、排序、显示/隐藏等功能
 */

// 全局变量
var currentPage = 1;
var pageSize = 10;
var totalPage = 0;
var currentLang = '';
var currentInfoType = '';

/**
 * 加载产品列表
 * @param {number} page 页码
 * @param {number} size 每页条数
 * @param {string} lang 语言筛选（可选）
 * @param {string} infoType 产品类型筛选（可选）
 */
function product_list(page, size, lang, infoType) {
    currentPage = page || 1;
    pageSize = size || 10;
    currentLang = lang || '';
    currentInfoType = infoType || '';
    
    // 构建请求参数
    var params = {
        page: currentPage,
        size: pageSize
    };
    
    // 添加筛选条件
    if (currentLang !== '') {
        params.lang = currentLang;
    }
    if (currentInfoType !== '') {
        params.info_type = currentInfoType;
    }
    
    // 发送请求获取数据
    $.ajax({
        type: "get",
        url: "/apis/product_list/",
        data: params,
        success: function (data) {
            if (data.status === 'ok') {
                // 渲染数据列表
                render_product_list(data.data);
                
                // 更新分页
                totalPage = Math.ceil(data.total / pageSize);
                page_ctrl(currentPage, totalPage);

                // 获取所有产品以提取类型
                $.ajax({
                    type: "get",
                    url: "/apis/product_list/",
                    data: { page: 1, size: 1000 }, // 获取足够多的数据以提取所有类型
                    success: function (allData) {
                        if (allData.status === 'ok') {
                            const types = extractProductTypes(allData.data);
                            updateProductTypeSelects(types);
                        }
                    }
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '加载产品列表失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，加载产品列表失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 渲染产品列表
 * @param {Array} data 产品数据
 */
function render_product_list(data) {
    var html = '';
    
    if (!data || data.length === 0) {
        html = '<tr><td colspan="10" class="text-center">暂无数据</td></tr>';
    } else {
        $.each(data, function (index, item) {
            var langText = item.lang === 0 ? '<span class="label label-success">中文网站</span>' : '<span class="label label-info">英文网站</span>';
            var bg_style = item.lang === 0 ? ' style="background-color:#d2d2f7"' : '';
            var infoTypeText = getInfoTypeText(item.info_type);
            var titleDisplay = item.title || '(无标题)';
            var hasImage = item.image_path && item.image_path.trim() !== '';
            var hasText = item.content && item.content.trim() !== '';
            var imagePreview = hasImage ? '<div class="preview-image"><img src="' + item.image_path + '" alt="产品图片"></div>' : '<div class="preview-empty">无图片</div>';
            var textPreview = hasText ? '<div class="preview-text">' + item.content + '</div>' : '<div class="preview-empty">无描述</div>';
            var contentPreview = '<div class="preview-container">';
            contentPreview += '<div class="preview-column image-column">' + imagePreview + '</div>';
            contentPreview += '<div class="preview-column text-column">' + textPreview + '</div>';
            contentPreview += '</div>';
            var displayOrder = item.display_order || 100;
            var isShow = item.show === true || item.show === 1 || item.show === "1";
            var showText = isShow ? '<span class="badge badge-success toggle-show-status" data-id="' + item.id + '" data-show="1" style="cursor:pointer" title="点击切换显示状态">显示</span>' : '<span class="badge badge-important toggle-show-status" data-id="' + item.id + '" data-show="0" style="cursor:pointer" title="点击切换显示状态">不显示</span>';
            var updateTime = formatDateTime(item.update_time);
            html += '<tr>' +
                '<td' + bg_style + '>' + item.id + '</td>' +
                '<td' + bg_style + '>' + langText + '</td>' +
                '<td' + bg_style + '>' + infoTypeText + '</td>' +
                '<td' + bg_style + '>' + titleDisplay + '</td>' +
                '<td' + bg_style + '>' + contentPreview + '</td>' +
                '<td' + bg_style + '>' + displayOrder + '</td>' +
                '<td' + bg_style + '>' + showText + '</td>' +
                '<td' + bg_style + '>' + updateTime + '</td>' +
                '<td' + bg_style + '>' +
                '<button class="btn btn-primary btn-mini" onclick="show_edit_modal(' + item.id + ')">编辑</button> ' +
                '<button class="btn btn-danger btn-mini" onclick="show_delete_modal(' + item.id + ')">删除</button>' +
                '</td>' +
                '</tr>';
        });
    }
    
    $("#data_list").html(html);
    
    // 绑定显示状态切换事件
    $('.toggle-show-status').on('click', function() {
        var id = $(this).data('id');
        var currentShow = $(this).data('show');
        toggle_show_status(id, currentShow === 0);
    });
}

/**
 * 获取产品类型的显示文本
 * @param {string} infoType 产品类型
 * @returns {string} 显示文本
 */
function getInfoTypeText(infoType) {
    switch (infoType) {
        case 'navigation':
            return '导航';
        case 'development_boards':
            return '开发板';
        case 'wireless_display':
            return '无线投屏';
        case 'smart_cards':
            return '智能板卡';
        case 'ai':
            return '人工智能';
        default:
            return infoType;
    }
}

/**
 * 显示编辑模态框
 * @param {number} id 产品ID
 */
function show_edit_modal(id) {
    // 发送请求获取产品详情
    $.ajax({
        type: "get",
        url: "/apis/product_detail/",
        data: {
            id: id
        },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                
                // 填充表单数据
                $('#edit_id').val(item.id);
                $('#edit_show').val(item.show ? "True" : "False");
                $('#edit_lang').val(item.lang);
                $('#edit_info_type').val(item.info_type);
                $('#edit_display_order').val(item.display_order || 100);
                $('#edit_title').val(item.title || '');
                $('#edit_content').val(item.content || '');
                $('#edit_url').val(item.url || '');
                
                // 处理图片
                if (item.image_path) {
                    $('#edit_image').attr('src', item.image_path);
                } else {
                    $('#edit_image').attr('src', '');
                }
                
                // 重置图片相关状态
                $('#edit_new_image').val('');
                $('#edit_image_removed').val('false');
                
                // 显示模态框
                $('#editProductAlert').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '获取产品详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，获取产品详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 编辑产品
 */
function edit_product() {
    // 获取表单数据
    var formData = new FormData();
    formData.append('id', $('#edit_id').val());
    formData.append('show', $('#edit_show').val() === "True");
    formData.append('lang', $('#edit_lang').val());
    formData.append('info_type', $('#edit_info_type').val());
    formData.append('display_order', $('#edit_display_order').val());
    formData.append('title', $('#edit_title').val());
    formData.append('content', $('#edit_content').val());
    formData.append('url', $('#edit_url').val());
    
    // 处理图片
    var newImage = $('#edit_new_image')[0].files[0];
    if (newImage) {
        formData.append('image', newImage);
    }
    
    // 处理图片删除标记
    if ($('#edit_image_removed').val() === 'true') {
        formData.append('remove_image', true);
    }
    
    // 发送请求
    $.ajax({
        type: "post",
        url: "/apis/update_product/",
        data: formData,
        processData: false,
        contentType: false,
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#editProductAlert').modal('hide');
                
                // 刷新列表
                product_list(currentPage, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '产品更新成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '产品更新失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，产品更新失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 添加产品
 */
function add_product() {
    // 获取表单数据
    var formData = new FormData();
    formData.append('show', $('#add_show').val() === "True");
    formData.append('lang', $('#add_lang').val());
    formData.append('info_type', $('#add_info_type').val());
    formData.append('display_order', $('#add_display_order').val());
    formData.append('title', $('#add_title').val());
    formData.append('content', $('#add_content').val());
    formData.append('url', $('#add_url').val());
    
    // 处理图片
    var newImage = $('#add_new_image')[0].files[0];
    if (newImage) {
        formData.append('image', newImage);
    }
    
    // 发送请求
    $.ajax({
        type: "post",
        url: "/apis/add_product/",
        data: formData,
        processData: false,
        contentType: false,
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#addProductAlert').modal('hide');
                
                // 重置表单
                resetAddForm();
                
                // 刷新列表
                product_list(1, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '产品添加成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '产品添加失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，产品添加失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 显示删除确认模态框
 * @param {number} id 产品ID
 */
function show_delete_modal(id) {
    // 发送请求获取产品详情
    $.ajax({
        type: "get",
        url: "/apis/product_detail/",
        data: {
            id: id
        },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                
                // 填充确认信息
                $('#del_id').val(item.id);
                $('#del_id_span').text(item.id);
                $('#del_title').text(item.title || '(无标题)');
                $('#del_content').text(item.content || '(无内容)');
                
                // 处理图片预览
                if (item.image_path) {
                    $('#del_image').attr('src', item.image_path).show();
                    $('#del_image_container').show();
                } else {
                    $('#del_image').hide();
                    $('#del_image_container').hide();
                }
                
                // 显示模态框
                $('#deleteProductAlert').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '获取产品详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，获取产品详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 删除产品
 */
function del_product() {
    var id = $('#del_id').val();
    
    // 发送删除请求
    $.ajax({
        type: "post",
        url: "/apis/delete_product/",
        data: {
            id: id
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#deleteProductAlert').modal('hide');
                
                // 刷新列表
                product_list(currentPage, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '产品删除成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '产品删除失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，产品删除失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 切换显示状态
 * @param {number} id 产品ID
 * @param {boolean} newShow 新的显示状态
 */
function toggle_show_status(id, newShow) {
    $.ajax({
        type: "post",
        url: "/apis/products/toggle_show",
        data: {
            id: id,
            show: newShow
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 刷新列表
                product_list(currentPage, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '显示状态更新成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '显示状态更新失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，显示状态更新失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 分页控制
 * @param {number} currentPage 当前页码
 * @param {number} totalPage 总页数
 */
function page_ctrl(currentPage, totalPage) {
    var html = "";
    
    // 首页
    html += "<a tabindex='0' class='first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='product_list(1, " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>首页</a>";
    
    // 上一页
    html += "<a tabindex='0' class='previous fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='product_list(" + (currentPage - 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>上一页</a>";
    
    // 页码
    var startPage = Math.max(1, currentPage - 2);
    var endPage = Math.min(totalPage, startPage + 4);
    
    for (var i = startPage; i <= endPage; i++) {
        html += "<a tabindex='0' class='fg-button ui-button ui-state-default " + 
            (i === currentPage ? "ui-state-disabled" : "") + 
            "' onclick='product_list(" + i + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>" + i + "</a>";
    }
    
    // 下一页
    html += "<a tabindex='0' class='next fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='product_list(" + (currentPage + 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>下一页</a>";
    
    // 尾页
    html += "<a tabindex='0' class='last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='product_list(" + totalPage + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>尾页</a>";
    
    $("#page").html(html);
}

/**
 * 重置添加表单
 */
function resetAddForm() {
    // 重置表单字段
    $('#add_title').val('');
    $('#add_content').val('');
    $('#add_image').attr('src', '');
    $('#add_new_image').val('');
    $('#add_display_order').val('100');
    $('#add_image_removed').val('false');
    $('#add_url').val('');
    
    // 重置下拉框
    $('#add_show').val('True');
    $('#add_lang').val('0');
    $('#add_info_type').val('navigation');
}

/**
 * 格式化日期时间
 * @param {string} datetime 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(datetime) {
    if (!datetime) return '';
    
    var date = new Date(datetime);
    var year = date.getFullYear();
    var month = (date.getMonth() + 1).toString().padStart(2, '0');
    var day = date.getDate().toString().padStart(2, '0');
    var hours = date.getHours().toString().padStart(2, '0');
    var minutes = date.getMinutes().toString().padStart(2, '0');
    var seconds = date.getSeconds().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 从产品列表数据中提取唯一的产品类型
 * @param {Array} products 产品列表数据
 * @returns {Array} 产品类型列表
 */
function extractProductTypes(products) {
    if (!products || !Array.isArray(products)) return [];
    
    // 使用Set去重
    const typeSet = new Set();
    products.forEach(product => {
        if (product.info_type) {
            typeSet.add(product.info_type);
        }
    });
    
    // 转换为数组并排序
    return Array.from(typeSet).sort();
}

/**
 * 更新产品类型下拉选项
 * @param {Array} types 产品类型列表
 */
function updateProductTypeSelects(types) {
    // 记录当前选中值
    var currentFilter = $('#info_type_filter').val();

    // 构建筛选器的选项HTML
    var optionsHtml = '<option value="">所有类型</option>';
    types.forEach(function(type) {
        optionsHtml += '<option value="' + type + '">' + type + '</option>';
    });
    
    // 更新筛选器的选项并恢复选中项
    $('#info_type_filter').html(optionsHtml).val(currentFilter);

    // 创建datalist用于输入框自动完成
    var datalistHtml = '<datalist id="product_types">';
    types.forEach(function(type) {
        datalistHtml += '<option value="' + type + '">';
    });
    datalistHtml += '</datalist>';

    // 如果datalist不存在则添加，存在则更新
    if ($('#product_types').length === 0) {
        $('body').append(datalistHtml);
    } else {
        $('#product_types').html(types.map(type => '<option value="' + type + '">').join(''));
    }

    // 为输入框添加datalist支持
    $('#edit_info_type, #add_info_type').attr('list', 'product_types');
}

// 页面加载完成后执行
$(document).ready(function() {
    // 加载产品列表（会自动提取并更新产品类型）
    product_list(1, 10);

    // 每次打开模态框时获取最新的产品类型列表
    $('#addProductAlert, #editProductAlert').on('show', function() {
        $.ajax({
            type: "get",
            url: "/apis/product_list/",
            data: { page: 1, size: 1000 }, // 获取足够多的数据以提取所有类型
            success: function (data) {
                if (data.status === 'ok') {
                    const types = extractProductTypes(data.data);
                    updateProductTypeSelects(types);
                }
            }
        });
    });
});