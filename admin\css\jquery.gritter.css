/* the norm */
#gritter-notice-wrapper {
	position:fixed;
	top:50px;
	right:10px;
	width:301px;
	z-index:9999999 !important;
}
#gritter-notice-wrapper.top-left {
    left: 20px;
    right: auto;
}
#gritter-notice-wrapper.bottom-right {
    top: auto;
    left: auto;
    bottom: 20px;
    right: 20px;
}
#gritter-notice-wrapper.bottom-left {
    top: auto;
    right: auto;
    bottom: 20px;
    left: 20px;
}
.gritter-item-wrapper {
	position:relative;
	margin:0 0 10px 0;
	z-index:9999999 !important;
}

.gritter-top, .gritter-bottom {
	height: 0;
}

.gritter-item {
	display:block;
	background: #333333;
	color:#ffffff;
	box-shadow:3px 3px 20px #000;
	padding:7px 10px 10px;
	font-size: 11px;
	font-family:verdana;
	border-radius: 3px;
}
.hover .gritter-item {
}
.gritter-item p {
	padding:0;
	margin:0;
	word-wrap:break-word;
	font-size: 10px;
	line-height: 14px;
}
.gritter-close {
	display:none;
	position:absolute;
	top:-7px;
	right:-9px;
	background:url(../img/gritter/gritter.png) no-repeat left top;
	cursor:pointer;
	width:30px;
	height:30px;
}
.gritter-title {
	font-size:12px;
	font-weight:bold;
	padding:0 0 7px 0;
	display:block;
}
.gritter-image {
	width:32px;
	height:32px;
	float:left;
	margin: 5px;
}
.gritter-with-image,
.gritter-without-image {
	padding:0;
}
.gritter-with-image {
	width:220px;
	float:right;
}
/* for the light (white) version of the gritter notice */
.gritter-light .gritter-item,
.gritter-light .gritter-bottom,
.gritter-light .gritter-top,
.gritter-light .gritter-close {
    background-image: url(../img/gritter/gritter-light.png);
    color: #222;
}
.gritter-light .gritter-title {
    text-shadow: none;
}

/* 添加不同类型的通知样式 */
.gritter-success {
    background-color: #dff0d8 !important;
    color: #3c763d !important;
    border-color: #d6e9c6 !important;
}

.gritter-success .gritter-title {
    color: #3c763d !important;
    font-weight: bold;
}

.gritter-error {
    background-color: #f2dede !important;
    color: #a94442 !important;
    border-color: #ebccd1 !important;
}

.gritter-error .gritter-title {
    color: #a94442 !important;
    font-weight: bold;
}

.gritter-warning {
    background-color: #fcf8e3 !important;
    color: #8a6d3b !important;
    border-color: #faebcc !important;
}

.gritter-warning .gritter-title {
    color: #8a6d3b !important;
    font-weight: bold;
}

.gritter-info {
    background-color: #d9edf7 !important;
    color: #31708f !important;
    border-color: #bce8f1 !important;
}

.gritter-info .gritter-title {
    color: #31708f !important;
    font-weight: bold;
}

