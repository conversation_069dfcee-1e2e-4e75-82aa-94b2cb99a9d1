<!DOCTYPE html>
<html lang="en">

<head>
        <title class="i18n" name="login.title"></title><meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="stylesheet" href="css/bootstrap.min.css" />
		<link rel="stylesheet" href="css/bootstrap-responsive.min.css" />
        <link rel="stylesheet" href="css/matrix-login.css" />
        <link rel="shortcut icon"  type="image/x-icon"  href="img/favicon.png" />
        <link href="css/font-awesome.css" rel="stylesheet" />

    </head>
    <body>
        <div id="loginbox">
            <form id="loginform" class="form-vertical" action="/apis/login/" method="post">
                <div class="control-group normal_text"> <h3 style="color: #0066A8;font-family: 黑体"><img src="img/logo2.png" alt="Logo" /><span class="i18n" name="login.logo"></span></h3></div>
                <div class="control-group">
                    <div class="controls">
                        <div class="main_input_box">
                            <span class="add-on bg_lg"><i class="icon-user"></i></span><input type="text" class="i18n-input" selectname="login.name"  selectattr="placeholder" name="username" id="username"/>
                        </div>
                    </div>
                </div>
                <div class="control-group">
                    <div class="controls">
                        <div class="main_input_box">
                            <span class="add-on bg_ly"><i class="icon-lock"></i></span><input type="password" class="i18n-input" selectname="login.pwd"  selectattr="placeholder" name="password" id="password"/>
                        </div>
                    </div>
                </div>
                <div class="alert alert-error" style="display: none" id="login_error">
                    <button class="close" data-dismiss="alert">×</button>
                    <i class="icon icon-info-sign" style="font-size: 15px">
                        <span>用户名或密码错误</span>
                    </i>
                </div>
                <div class="form-actions">
                    <span class="pull-right">
                        <button class="btn btn-success i18n btn-large" name="login.login" type="submit">登录</button>
                    </span>
                </div>
            </form>
            <form id="recoverform" action="/apis/acc/reset_pwd/" method="post"  class="form-vertical" style="display: none"  enctype="multipart/form-data" onSubmit="return reset_check();">
				<p class="normal_text i18n" name="login.resettitle"></p>


                <div class="control-group">
                    <div class="controls">
                        <div class="main_input_box">
                            <span class="add-on bg_ls"><i class="icon-phone"></i></span><input type="text"  class='i18n-input' selectname="login.phone" selectattr="placeholder" name="tel" id="reset_phone" style="width: 40%;" onblur="check_reset_phone()" /><a class="flip-link  btn btn-info i18n"  name="login.sendsms" type="button" style="width: 30%"  id="reset_send"></a>
                        </div>
                    </div>
                </div>
                <div class="control-group">
                    <div class="controls">
                        <div class="main_input_box">
                            <span class="add-on bg_lv"><i class="icon-envelope"></i></span><input type="text" class='i18n-input' selectname="login.smscode" selectattr="placeholder" name="code" id="reset_code" />
                            <input type="text" value="1" name="code_type" style="display: none">
                        </div>
                    </div>
                </div>
                <div class="control-group">
                    <div class="controls">
                        <div class="main_input_box">
                            <span class="add-on bg_ly"><i class="icon-lock"></i></span><input type="password" class='i18n-input' selectname="login.newpwd" selectattr="placeholder"  name="password" id="reset_pwd" />
                        </div>
                    </div>
                </div>
                <div class="alert alert-error" style="display: none" id="reset_error">
                    <button class="close" data-dismiss="alert">×</button>
                    <i class="icon icon-info-sign" style="font-size: 15px">
                        <span></span>
                    </i>
                </div>

                <div class="form-actions">
                    <span class="pull-left"><a href="#" class="flip-link btn btn-success to-login i18n" name="login.return2lg"></a></span>
                    <span class="pull-right"><button class="btn btn-info i18n"  name="login.reset" type="submit"></button></span>
                </div>
            </form>
            <form id="regform" action="/apis/acc/admin_reg/" method="post"  class="form-vertical" style="display: none"  enctype="multipart/form-data" onSubmit="return reg_check();" >
                <!--<div class="control-group normal_text"> <h3><img src="img/logo.png" alt="Logo" /></h3></div>-->
				<p class="normal_text i18n" name="login.register_title"></p>

                    <div class="control-group">
                        <div class="controls">
                            <div class="main_input_box">
                                <span class="add-on bg_lg"><i class="icon-user"></i></span><input type="text" class='i18n-input' selectname="login.smscode" selectattr="placeholder" name="username" id="reg_username" />
                            </div>
                        </div>
                    </div>
                    <div class="control-group">
                        <div class="controls">
                            <div class="main_input_box">
                                <span class="add-on bg_ly"><i class="icon-lock"></i></span><input type="password" class='i18n-input' selectname="login.pwd" selectattr="placeholder"  name="password" id="reg_pwd"/>
                            </div>
                        </div>
                    </div>
                    <div class="control-group">
                        <div class="controls">
                            <div class="main_input_box">
                                <span class="add-on bg_ly"><i class="icon-lock"></i></span><input type="password" class='i18n-input' selectname="login.pwdagin" selectattr="placeholder" name="password2" id="reg_pwd2" onblur="check_pwd()"/>
                            </div>
                        </div>
                    </div>
                    <div class="control-group">
                            <div class="controls">
                                <div class="main_input_box">
                                    <span class="add-on bg_ls"><i class="icon-phone"></i></span><input type="text" class='i18n-input' selectname="login.phone" selectattr="placeholder"  name="tel" id="reg_phone" style="width: 40%;" onblur="check_reg_phone()" /><a class="flip-link  btn btn-info i18n" name="login.sendsms"  type="button" style="width: 30%"  id="reg_send"></a>
                                </div>
                            </div>
                    </div>
                    <div class="control-group">
                            <div class="controls">
                                <div class="main_input_box">
                                    <span class="add-on bg_lv"><i class="icon-envelope"></i></span><input type="text" class='i18n-input' selectname="login.smscode" selectattr="placeholder"  name="code" id="reg_code" />
                                </div>
                            </div>
                    </div>
                    <div class="control-group" >
                            <div class="controls">
                                <div class="main_input_box">
                                    <span class="add-on bg_lh"><i class="icon-key"></i></span><input type="text" class='i18n-input' selectname="login.lic" selectattr="placeholder" name="license" id="reg_license"/>
                                </div>
                            </div>
                    </div>
                    <div class="alert alert-error" style="display: none" id="reg_error">
                        <button class="close" data-dismiss="alert">×</button>
                        <i class="icon icon-info-sign" style="font-size: 15px">
                            <span></span>
                        </i>
                    </div>

                <div class="form-actions">
                    <span class="pull-left"><a href="#" class="flip-link btn btn-success to-login i18n" name="login.return2lg"></a></span>
                    <span class="pull-right">
                        <button class="btn btn-info i18n" name="login.register" type="submit"></button>
                    </span>
                </div>
            </form>

        </div>

        <script src="js/jquery.min.js"></script>
        <script src="js/jquery.cookie.js"></script>
        <script src="js/jquery.i18n.js"></script>
        <script src="js/language.js"></script>
        <script src="js/matrix.login.js"></script>
        <script src="js/jquery-form.js"></script>
    </body>

</html>
