/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.1 (2020-07-08)
 */
!function(nt){"use strict";var Z=function(){},d=function(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e(o.apply(null,t))}},at=function(t){return function(){return t}},ct=function(t){return t};function g(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=r.concat(t);return o.apply(null,e)}}var x=function(n){return function(t){return!n(t)}},u=function(t){return function(){throw new Error(t)}},c=at(!1),i=at(!0),t=tinymce.util.Tools.resolve("tinymce.ThemeManager"),et=function(){return(et=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t}).apply(this,arguments)};function y(t,n){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&n.indexOf(o)<0&&(e[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(t);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(e[o[r]]=t[o[r]])}return e}function b(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var o=Array(t),r=0;for(n=0;n<e;n++)for(var i=arguments[n],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}var n,e,o,r,a,s,l=function(){return f},f=(n=function(t){return t.isNone()},{fold:function(t,n){return t()},is:c,isSome:c,isNone:i,getOr:o=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:at(null),getOrUndefined:at(undefined),or:o,orThunk:e,map:l,each:Z,bind:l,exists:c,forall:i,filter:l,equals:n,equals_:n,toArray:function(){return[]},toString:at("none()")}),m=function(e){var t=at(e),n=function(){return r},o=function(t){return t(e)},r={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:i,isNone:c,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return m(t(e))},each:function(t){t(e)},bind:o,exists:o,forall:o,filter:function(t){return t(e)?r:f},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(c,function(t){return n(e,t)})}};return r},st={some:m,none:l,from:function(t){return null===t||t===undefined?f:m(t)}},p=function(o){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===o;var n,e}},h=function(n){return function(t){return typeof t===n}},w=p("string"),S=p("object"),v=p("array"),k=h("boolean"),C=(r=undefined,function(t){return r===t}),O=function(t){return!(null===(n=t)||n===undefined);var n},_=h("function"),ot=h("number"),T=function(t,n){if(v(t)){for(var e=0,o=t.length;e<o;++e)if(!n(t[e]))return!1;return!0}return!1},E=Array.prototype.slice,B=Array.prototype.indexOf,D=Array.prototype.push,A=function(t,n){return B.call(t,n)},M=function(t,n){return-1<A(t,n)},F=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return!0}return!1},I=function(t,n){for(var e=[],o=0;o<t;o++)e.push(n(o));return e},R=function(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=E.call(t,o,o+n);e.push(r)}return e},V=function(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r)}return o},rt=function(t,n){for(var e=0,o=t.length;e<o;e++){n(t[e],e)}},P=function(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(n(u,r)?e:o).push(u)}return{pass:e,fail:o}},H=function(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o)&&e.push(i)}return e},z=function(t,n,e){return function(t,n){for(var e=t.length-1;0<=e;e--){n(t[e],e)}}(t,function(t){e=n(e,t)}),e},N=function(t,n,e){return rt(t,function(t){e=n(e,t)}),e},L=function(t,n){return function(t,n,e){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(n(i,o))return st.some(i);if(e(i,o))break}return st.none()}(t,n,c)},j=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return st.some(e)}return st.none()},it=function(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!v(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);D.apply(n,t[e])}return n},U=function(t,n){return it(V(t,n))},W=function(t,n){for(var e=0,o=t.length;e<o;++e){if(!0!==n(t[e],e))return!1}return!0},G=function(t){var n=E.call(t,0);return n.reverse(),n},X=function(t,n){return H(t,function(t){return!M(n,t)})},Y=function(t,n){var e=E.call(t,0);return e.sort(n),e},q=function(t){return 0===t.length?st.none():st.some(t[0])},K=function(t){return 0===t.length?st.none():st.some(t[t.length-1])},J=_(Array.from)?Array.from:function(t){return E.call(t)},$=function(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return st.none()},Q=function(e){return{is:function(t){return e===t},isValue:i,isError:c,getOr:at(e),getOrThunk:at(e),getOrDie:at(e),or:function(t){return Q(e)},orThunk:function(t){return Q(e)},fold:function(t,n){return n(e)},map:function(t){return Q(t(e))},mapError:function(t){return Q(e)},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOption:function(){return st.some(e)}}},tt=function(e){return{is:c,isValue:c,isError:i,getOr:ct,getOrThunk:function(t){return t()},getOrDie:function(){return u(String(e))()},or:function(t){return t},orThunk:function(t){return t()},fold:function(t,n){return t(e)},map:function(t){return tt(e)},mapError:function(t){return tt(t(e))},each:Z,bind:function(t){return tt(e)},exists:c,forall:i,toOption:st.none}},ut={value:Q,error:tt,fromOption:function(t,n){return t.fold(function(){return tt(n)},Q)}};(s=a=a||{})[s.Error=0]="Error",s[s.Value=1]="Value";var lt,ft,dt=function(t,n,e){return t.stype===a.Error?n(t.serror):e(t.svalue)},mt=function(t){return{stype:a.Value,svalue:t}},gt=function(t){return{stype:a.Error,serror:t}},pt=function(t){return t.fold(gt,mt)},ht=function(t){return dt(t,ut.error,ut.value)},vt=mt,bt=function(t){var n=[],e=[];return rt(t,function(t){dt(t,function(t){return e.push(t)},function(t){return n.push(t)})}),{values:n,errors:e}},yt=gt,xt=function(t,n){return t.stype===a.Value?n(t.svalue):t},wt=function(t,n){return t.stype===a.Error?n(t.serror):t},St=function(t,n){return t.stype===a.Value?{stype:a.Value,svalue:n(t.svalue)}:t},kt=function(t,n){return t.stype===a.Error?{stype:a.Error,serror:n(t.serror)}:t},Ct=Object.keys,Ot=Object.hasOwnProperty,_t=function(t,n){for(var e=Ct(t),o=0,r=e.length;o<r;o++){var i=e[o];n(t[i],i)}},Tt=function(t,e){return Et(t,function(t,n){return{k:n,v:e(t,n)}})},Et=function(t,o){var r={};return _t(t,function(t,n){var e=o(t,n);r[e.k]=e.v}),r},Bt=function(t,n){var e,o,r,i,u={};return e=n,i=u,o=function(t,n){i[n]=t},r=Z,_t(t,function(t,n){(e(t,n)?o:r)(t,n)}),u},Dt=function(t,e){var o=[];return _t(t,function(t,n){o.push(e(t,n))}),o},At=function(t,n){for(var e=Ct(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return st.some(u)}return st.none()},Mt=function(t){return Dt(t,function(t){return t})},Ft=function(t,n){return It(t,n)?st.from(t[n]):st.none()},It=function(t,n){return Ot.call(t,n)},Rt=function(t,n){return It(t,n)&&t[n]!==undefined&&null!==t[n]},Vt=function(u){if(!v(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return rt(u,function(t,o){var n=Ct(t);if(1!==n.length)throw new Error("one and only one name per case");var r=n[0],i=t[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!v(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var t=arguments.length;if(t!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+t);for(var e=new Array(t),n=0;n<e.length;n++)e[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(t){var n=Ct(t);if(a.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+n.join(","));if(!W(a,function(t){return M(n,t)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,e)},log:function(t){nt.console.log(t,{constructors:a,constructor:r,params:e})}}}}),e},Pt=Object.prototype.hasOwnProperty,Ht=function(u){return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<t.length;o++){var r=t[o];for(var i in r)Pt.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}},zt=Ht(function(t,n){return S(t)&&S(n)?zt(t,n):n}),Nt=Ht(function(t,n){return n}),Lt=function(e){var o,r=!1;return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r||(r=!0,o=e.apply(null,t)),o}},jt=Vt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Ut=function(t){return jt.defaultedThunk(at(t))},Wt=jt.strict,Gt=jt.asOption,Xt=jt.defaultedThunk,Yt=(jt.asDefaultedOptionThunk,jt.mergeWithThunk),qt=function(t,n){var e;return(e={})[t]=n,e},Kt=(Vt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(t,n){return e=n,o={},_t(t,function(t,n){M(e,n)||(o[n]=t)}),o;var e,o}),Jt=function(t,n){return qt(t,n)},$t=function(t){return n={},rt(t,function(t){n[t.key]=t.value}),n;var n},Qt=function(t,n){var e,o,r,i,u,a=(e=[],o=[],rt(t,function(t){t.fold(function(t){e.push(t)},function(t){o.push(t)})}),{errors:e,values:o});return 0<a.errors.length?(u=a.errors,ut.error(it(u))):(i=n,0===(r=a.values).length?ut.value(i):ut.value(zt(i,Nt.apply(undefined,r))))},Zt=function(t){return d(yt,it)(t)},tn=function(t,n){var e,o,r=bt(t);return 0<r.errors.length?Zt(r.errors):(e=r.values,o=n,0<e.length?vt(zt(o,Nt.apply(undefined,e))):vt(o))},nn=function(t){var n=bt(t);return 0<n.errors.length?Zt(n.errors):vt(n.values)},en=function(t){return S(t)&&100<Ct(t).length?" removed due to size":JSON.stringify(t,null,2)},on=function(t,n){return yt([{path:t,getErrorInfo:n}])},rn=Vt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),un=function(e,o,r){return Ft(o,r).fold(function(){return t=r,n=o,on(e,function(){return'Could not find valid *strict* value for "'+t+'" in '+en(n)});var t,n},vt)},an=function(t,n,e){var o=Ft(t,n).fold(function(){return e(t)},ct);return vt(o)},cn=function(a,c,t,s){return t.fold(function(r,e,t,o){var i=function(t){var n=o.extract(a.concat([r]),s,t);return St(n,function(t){return qt(e,s(t))})},u=function(t){return t.fold(function(){var t=qt(e,s(st.none()));return vt(t)},function(t){var n=o.extract(a.concat([r]),s,t);return St(n,function(t){return qt(e,s(st.some(t)))})})};return t.fold(function(){return xt(un(a,c,r),i)},function(t){return xt(an(c,r,t),i)},function(){return xt(vt(Ft(c,r)),u)},function(t){return xt((e=t,o=Ft(n=c,r).map(function(t){return!0===t?e(n):t}),vt(o)),u);var n,e,o},function(t){var n=t(c),e=St(an(c,r,at({})),function(t){return zt(n,t)});return xt(e,i)})},function(t,n){var e=n(c);return vt(qt(t,s(e)))})},sn=function(o){return{extract:function(e,t,n){return wt(o(n,t),function(t){return n=t,on(e,function(){return n});var n})},toString:function(){return"val"}}},ln=function(t){var u=fn(t),a=z(t,function(n,t){return t.fold(function(t){return zt(n,Jt(t,!0))},at(n))},{});return{extract:function(t,n,e){var o,r=k(e)?[]:Ct(Bt(e,function(t){return t!==undefined&&null!==t})),i=H(r,function(t){return!Rt(a,t)});return 0===i.length?u.extract(t,n,e):(o=i,on(t,function(){return"There are unsupported fields: ["+o.join(", ")+"] specified"}))},toString:u.toString}},fn=function(a){return{extract:function(t,n,e){return o=t,r=e,i=n,u=V(a,function(t){return cn(o,r,t,i)}),tn(u,{});var o,r,i,u},toString:function(){return"obj{\n"+V(a,function(t){return t.fold(function(t,n,e,o){return t+" -> "+o.toString()},function(t,n){return"state("+t+")"})}).join("\n")+"}"}}},dn=function(r){return{extract:function(e,o,t){var n=V(t,function(t,n){return r.extract(e.concat(["["+n+"]"]),o,t)});return nn(n)},toString:function(){return"array("+r.toString()+")"}}},mn=function(a,c){return{extract:function(e,o,r){var t,n,i=Ct(r),u=(t=e,n=i,dn(sn(a)).extract(t,ct,n));return xt(u,function(t){var n=V(t,function(t){return rn.field(t,t,Wt(),c)});return fn(n).extract(e,o,r)})},toString:function(){return"setOf("+c.toString()+")"}}},gn=at(sn(vt)),pn=d(dn,fn),hn=rn.state,vn=rn.field,bn=function(e,n,o,r,i){return Ft(r,i).fold(function(){return t=r,n=i,on(e,function(){return'The chosen schema: "'+n+'" did not exist in branches: '+en(t)});var t,n},function(t){return t.extract(e.concat(["branch: "+i]),n,o)})},yn=function(r,i){return{extract:function(n,e,o){return Ft(o,r).fold(function(){return t=r,on(n,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return bn(n,e,o,i,t)})},toString:function(){return"chooseOn("+r+"). Possible values: "+Ct(i)}}},xn=sn(vt),wn=function(t){return pn(t)},Sn=function(o){return{extract:function(t,n,e){return o().extract(t,n,e)},toString:function(){return o().toString()}}},kn=function(n){return sn(function(t){return n(t).fold(yt,vt)})},Cn=function(n,t){return mn(function(t){return pt(n(t))},t)},On=function(t,n,e){return ht((o=t,r=ct,i=e,u=n.extract([o],r,i),kt(u,function(t){return{input:i,errors:t}})));var o,r,i,u},_n=function(t){return t.fold(function(t){throw new Error(En(t))},ct)},Tn=function(t,n,e){return _n(On(t,n,e))},En=function(t){return"Errors: \n"+(n=t.errors,e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n,V(e,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}).join("\n"))+"\n\nInput object: "+en(t.input);var n,e},Bn=function(t,n){return yn(t,n)},Dn=function(t,n){return yn(t,Tt(n,fn))},An=at(xn),Mn=function(e,o){return sn(function(t){var n=typeof t;return e(t)?vt(t):yt("Expected type: "+o+" but got: "+n)})},Fn=Mn(ot,"number"),In=Mn(w,"string"),Rn=Mn(k,"boolean"),Vn=Mn(_,"function"),Pn=function(n){var t=function(t,n){for(var e=t.next();!e.done;){if(!n(e.value))return!1;e=t.next()}return!0};if(Object(n)!==n)return!0;switch({}.toString.call(n).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(n).every(function(t){return Pn(n[t])});case"Map":return t(n.keys(),Pn)&&t(n.values(),Pn);case"Set":return t(n.keys(),Pn);default:return!1}},Hn=sn(function(t){return Pn(t)?vt(t):yt("Expected value to be acceptable for sending via postMessage")}),zn=function(n){return kn(function(t){return M(n,t)?ut.value(t):ut.error('Unsupported value: "'+t+'", choose one of "'+n.join(", ")+'".')})},Nn=function(t){return vn(t,t,Wt(),gn())},Ln=function(t,n){return vn(t,t,Wt(),n)},jn=function(t){return Ln(t,In)},Un=function(t,n){return vn(t,t,Wt(),zn(n))},Wn=function(t){return Ln(t,Vn)},Gn=function(t,n){return vn(t,t,Wt(),fn(n))},Xn=function(t,n){return vn(t,t,Wt(),pn(n))},Yn=function(t,n){return vn(t,t,Wt(),dn(n))},qn=function(t){return vn(t,t,Gt(),gn())},Kn=function(t,n){return vn(t,t,Gt(),n)},Jn=function(t){return Kn(t,Fn)},$n=function(t){return Kn(t,In)},Qn=function(t){return Kn(t,Vn)},Zn=function(t,n){return Kn(t,fn(n))},te=function(t,n){return vn(t,t,Ut(n),gn())},ne=function(t,n,e){return vn(t,t,Ut(n),e)},ee=function(t,n){return ne(t,n,Fn)},oe=function(t,n){return ne(t,n,In)},re=function(t,n,e){return ne(t,n,zn(e))},ie=function(t,n){return ne(t,n,Rn)},ue=function(t,n){return ne(t,n,Vn)},ae=function(t,n,e){return ne(t,n,fn(e))},ce=function(t,n){return hn(t,n)},se=function(t){var n=t;return{get:function(){return n},set:function(t){n=t}}},le=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:at(t)}},fe={fromHtml:function(t,n){var e=(n||nt.document).createElement("div");if(e.innerHTML=t,!e.hasChildNodes()||1<e.childNodes.length)throw nt.console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return le(e.childNodes[0])},fromTag:function(t,n){var e=(n||nt.document).createElement(t);return le(e)},fromText:function(t,n){var e=(n||nt.document).createTextNode(t);return le(e)},fromDom:le,fromPoint:function(t,n,e){var o=t.dom();return st.from(o.elementFromPoint(n,e)).map(le)}},de=function(t,n){var e=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}return undefined}(t,n);if(!e)return{major:0,minor:0};var o=function(t){return Number(n.replace(e,"$"+t))};return ge(o(1),o(2))},me=function(){return ge(0,0)},ge=function(t,n){return{major:t,minor:n}},pe={nu:ge,detect:function(t,n){var e=String(n).toLowerCase();return 0===t.length?me():de(t,e)},unknown:me},he="Firefox",ve=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isEdge:o("Edge"),isChrome:o("Chrome"),isIE:o("IE"),isOpera:o("Opera"),isFirefox:o(he),isSafari:o("Safari")}},be={unknown:function(){return ve({current:undefined,version:pe.unknown()})},nu:ve,edge:at("Edge"),chrome:at("Chrome"),ie:at("IE"),opera:at("Opera"),firefox:at(he),safari:at("Safari")},ye="Windows",xe="Android",we="Solaris",Se="FreeBSD",ke="ChromeOS",Ce=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isWindows:o(ye),isiOS:o("iOS"),isAndroid:o(xe),isOSX:o("OSX"),isLinux:o("Linux"),isSolaris:o(we),isFreeBSD:o(Se),isChromeOS:o(ke)}},Oe={unknown:function(){return Ce({current:undefined,version:pe.unknown()})},nu:Ce,windows:at(ye),ios:at("iOS"),android:at(xe),linux:at("Linux"),osx:at("OSX"),solaris:at(we),freebsd:at(Se),chromeos:at(ke)},_e=function(t,n){var e=String(n).toLowerCase();return L(t,function(t){return t.search(e)})},Te=function(t,e){return _e(t,e).map(function(t){var n=pe.detect(t.versionRegexes,e);return{current:t.name,version:n}})},Ee=function(t,e){return _e(t,e).map(function(t){var n=pe.detect(t.versionRegexes,e);return{current:t.name,version:n}})},Be=function(t,n){return-1!==t.indexOf(n)},De=(lt=/^\s+|\s+$/g,function(t){return t.replace(lt,"")}),Ae=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Me=function(n){return function(t){return Be(t,n)}},Fe=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return Be(t,"edge/")&&Be(t,"chrome")&&Be(t,"safari")&&Be(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Ae],search:function(t){return Be(t,"chrome")&&!Be(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return Be(t,"msie")||Be(t,"trident")}},{name:"Opera",versionRegexes:[Ae,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Me("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Me("firefox")},{name:"Safari",versionRegexes:[Ae,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(Be(t,"safari")||Be(t,"mobile/"))&&Be(t,"applewebkit")}}],Ie=[{name:"Windows",search:Me("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return Be(t,"iphone")||Be(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Me("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Me("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Me("linux"),versionRegexes:[]},{name:"Solaris",search:Me("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Me("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Me("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Re={browsers:at(Fe),oses:at(Ie)},Ve=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g=Re.browsers(),p=Re.oses(),h=Te(g,t).fold(be.unknown,be.nu),v=Ee(p,t).fold(Oe.unknown,Oe.nu);return{browser:h,os:v,deviceType:(o=h,r=t,i=n,u=(e=v).isiOS()&&!0===/ipad/i.test(r),a=e.isiOS()&&!u,c=e.isiOS()||e.isAndroid(),s=c||i("(pointer:coarse)"),l=u||!a&&c&&i("(min-device-width:768px)"),f=a||c&&!l,d=o.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),m=!f&&!l&&!d,{isiPad:at(u),isiPhone:at(a),isTablet:at(l),isPhone:at(f),isTouch:at(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:at(d),isDesktop:at(m)})}},Pe=function(t){return nt.window.matchMedia(t).matches},He=Lt(function(){return Ve(nt.navigator.userAgent,Pe)}),ze=function(){return He()},Ne=function(t,n){var e=t.dom();if(1!==e.nodeType)return!1;var o=e;if(o.matches!==undefined)return o.matches(n);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(n);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(n);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},Le=function(t){return 1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType||0===t.childElementCount},je=function(t,n){return t.dom()===n.dom()},Ue=function(t,n){return e=t.dom(),o=n.dom(),r=e,i=o,u=nt.Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(r.compareDocumentPosition(i)&u);var e,o,r,i,u},We=function(t,n){return ze().browser.isIE()?Ue(t,n):(e=n,o=t.dom(),r=e.dom(),o!==r&&o.contains(r));var e,o,r},Ge=function(t){return _(t)?t:at(!1)},Xe=function(t,n,e){for(var o=t.dom(),r=Ge(e);o.parentNode;){o=o.parentNode;var i=fe.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return st.none()},Ye=function(t,n,e){var o=n(t),r=Ge(e);return o.orThunk(function(){return r(t)?st.none():Xe(t,n,r)})},qe=function(t,n){return je(t.element(),n.event().target())},Ke=function(t){if(!Rt(t,"can")&&!Rt(t,"abort")&&!Rt(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return Tn("Extracting event.handler",ln([te("can",at(!0)),te("abort",at(!1)),te("run",Z)]),t)},Je=function(e){var n,o,r,i,t=(o=function(t){return t.can},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(n,function(t,n){return t&&o(n).apply(undefined,e)},!0)}),u=(r=n=e,i=function(t){return t.abort},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(r,function(t,n){return t||i(n).apply(undefined,e)},!1)});return Ke({can:t,abort:u,run:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];rt(e,function(t){t.run.apply(undefined,n)})}})},$e=at("touchstart"),Qe=at("touchmove"),Ze=at("touchend"),to=at("touchcancel"),no=at("mousedown"),eo=at("mousemove"),oo=at("mouseout"),ro=at("mouseup"),io=at("mouseover"),uo=at("focusin"),ao=at("focusout"),co=at("keydown"),so=at("keyup"),lo=at("input"),fo=at("change"),mo=at("click"),go=at("transitionend"),po=at("selectstart"),ho={tap:at("alloy.tap")},vo=at("alloy.focus"),bo=at("alloy.blur.post"),yo=at("alloy.paste.post"),xo=at("alloy.receive"),wo=at("alloy.execute"),So=at("alloy.focus.item"),ko=ho.tap,Co=at("alloy.longpress"),Oo=at("alloy.sandbox.close"),_o=at("alloy.typeahead.cancel"),To=at("alloy.system.init"),Eo=at("alloy.system.touchmove"),Bo=at("alloy.system.touchend"),Do=at("alloy.system.scroll"),Ao=at("alloy.system.resize"),Mo=at("alloy.system.attached"),Fo=at("alloy.system.detached"),Io=at("alloy.system.dismissRequested"),Ro=at("alloy.system.repositionRequested"),Vo=at("alloy.focusmanager.shifted"),Po=at("alloy.slotcontainer.visibility"),Ho=at("alloy.change.tab"),zo=at("alloy.dismiss.tab"),No=at("alloy.highlight"),Lo=at("alloy.dehighlight"),jo=function(t,n){Xo(t,t.element(),n,{})},Uo=function(t,n,e){Xo(t,t.element(),n,e)},Wo=function(t){jo(t,wo())},Go=function(t,n,e){Xo(t,n,e,{})},Xo=function(t,n,e,o){var r=et({target:n},o);t.getSystem().triggerEvent(e,n,Tt(r,at))},Yo=function(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event())},qo=function(t){return $t(t)},Ko=function(t,n){return{key:t,value:Ke({abort:n})}},Jo=function(t){return{key:t,value:Ke({run:function(t,n){n.event().prevent()}})}},$o=function(t,n){return{key:t,value:Ke({run:n})}},Qo=function(t,e,o){return{key:t,value:Ke({run:function(t,n){e.apply(undefined,[t,n].concat(o))}})}},Zo=function(t){return function(e){return{key:t,value:Ke({run:function(t,n){qe(t,n)&&e(t,n)}})}}},tr=function(t,n,e){var o,r,i=n.partUids[e];return r=i,$o(o=t,function(t,n){t.getSystem().getByUid(r).each(function(t){Yo(t,t.element(),o,n)})})},nr=function(t,r){return $o(t,function(n,t){var e=t.event(),o=n.getSystem().getByDom(e.target()).fold(function(){return Ye(e.target(),function(t){return n.getSystem().getByDom(t).toOption()},at(!1)).getOr(n)},function(t){return t});r(n,o,t)})},er=function(t){return $o(t,function(t,n){n.cut()})},or=function(t,n){return Zo(t)(n)},rr=Zo(Mo()),ir=Zo(Fo()),ur=Zo(To()),ar=(ft=wo(),function(t){return $o(ft,t)}),cr=("undefined"!=typeof nt.window?nt.window:Function("return this;")(),function(t){return t.dom().nodeName.toLowerCase()}),sr=function(n){return function(t){return t.dom().nodeType===n}},lr=sr(1),fr=sr(3),dr=sr(9),mr=sr(11),gr=function(t){return fe.fromDom(t.dom().ownerDocument)},pr=function(t){return dr(t)?t:gr(t)},hr=function(t){return fe.fromDom(t.dom().ownerDocument.documentElement)},vr=function(t){return fe.fromDom(t.dom().ownerDocument.defaultView)},br=function(t){return st.from(t.dom().parentNode).map(fe.fromDom)},yr=function(t){return st.from(t.dom().offsetParent).map(fe.fromDom)},xr=function(t){return V(t.dom().childNodes,fe.fromDom)},wr=function(t,n){var e=t.dom().childNodes;return st.from(e[n]).map(fe.fromDom)},Sr=function(n,e){br(n).each(function(t){t.dom().insertBefore(e.dom(),n.dom())})},kr=function(t,n){var e;(e=t,st.from(e.dom().nextSibling).map(fe.fromDom)).fold(function(){br(t).each(function(t){Or(t,n)})},function(t){Sr(t,n)})},Cr=function(n,e){wr(n,0).fold(function(){Or(n,e)},function(t){n.dom().insertBefore(e.dom(),t.dom())})},Or=function(t,n){t.dom().appendChild(n.dom())},_r=function(n,t){rt(t,function(t){Or(n,t)})},Tr=function(t){t.dom().textContent="",rt(xr(t),function(t){Er(t)})},Er=function(t){var n=t.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},Br=function(t){var n,e=xr(t);0<e.length&&(n=t,rt(e,function(t){Sr(n,t)})),Er(t)},Dr=function(t){return t.dom().innerHTML},Ar=function(t,n){var e,o,r=gr(t).dom(),i=fe.fromDom(r.createDocumentFragment()),u=(e=n,(o=(r||nt.document).createElement("div")).innerHTML=e,xr(fe.fromDom(o)));_r(i,u),Tr(t),Or(t,i)},Mr=function(t,n,e){if(!(w(e)||k(e)||ot(e)))throw nt.console.error("Invalid call to Attr.set. Key ",n,":: Value ",e,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(n,e+"")},Fr=function(t,n,e){Mr(t.dom(),n,e)},Ir=function(t,n){var e=t.dom().getAttribute(n);return null===e?undefined:e},Rr=function(t,n){return st.from(Ir(t,n))},Vr=function(t,n){var e=t.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(n)},Pr=function(t,n){t.dom().removeAttribute(n)},Hr=function(t){return n=t,e=!1,fe.fromDom(n.dom().cloneNode(e));var n,e},zr=function(t){var n,e,o,r=Hr(t);return n=r,e=fe.fromTag("div"),o=fe.fromDom(n.dom().cloneNode(!0)),Or(e,o),Dr(e)},Nr=function(t){return zr(t)},Lr=qo([{key:vo(),value:Ke({can:function(t,n){var e,o,r=n.event().originator(),i=n.event().target();return o=i,!(je(e=r,t.element())&&!je(e,o))||(nt.console.warn(vo()+" did not get interpreted by the desired target. \nOriginator: "+Nr(r)+"\nTarget: "+Nr(i)+"\nCheck the "+vo()+" event handlers"),!1)}})}]),jr=/* */Object.freeze({__proto__:null,events:Lr}),Ur=0,Wr=function(t){var n=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++Ur+String(n)},Gr=at("alloy-id-"),Xr=at("data-alloy-id"),Yr=Gr(),qr=Xr(),Kr=function(t,n){Object.defineProperty(t.dom(),qr,{value:n,writable:!0})},Jr=function(t){var n=lr(t)?t.dom()[qr]:null;return st.from(n)},$r=function(t){return Wr(t)},Qr=ct,Zr=function(n){var t=function(t){return function(){throw new Error("The component must be in a context to send: "+t+(n?"\n"+Nr(n().element())+" is not in context.":""))}};return{debugInfo:at("fake"),triggerEvent:t("triggerEvent"),triggerFocus:t("triggerFocus"),triggerEscape:t("triggerEscape"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),broadcast:t("broadcast"),broadcastOn:t("broadcastOn"),broadcastEvent:t("broadcastEvent"),isConnected:at(!1)}},ti=Zr(),ni=function(t){return V(t,function(t){return o=n="/*",r=(e=t).length-n.length,""===o||e.length>=o.length&&e.substr(r,r+o.length)===o?t.substring(0,t.length-"/*".length):t;var n,e,o,r})},ei=function(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:ni(i)}},t},oi=Wr("alloy-premade"),ri=function(t){return Jt(oi,t)},ii=function(o){return t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return o.apply(void 0,b([t.getApis(),t],n))},n=o.toString(),e=n.indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,e-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:ni(i.slice(1))}},t;var t,n,e,r,i},ui={init:function(){return ai({readState:function(){return"No State required"}})}},ai=function(t){return t},ci=function(t,r){var i={};return _t(t,function(t,o){_t(t,function(t,n){var e=Ft(i,n).getOr([]);i[n]=e.concat([r(o,t)])})}),i},si=function(t){return{classes:t.classes!==undefined?t.classes:[],attributes:t.attributes!==undefined?t.attributes:{},styles:t.styles!==undefined?t.styles:{}}},li=function(t,n){return e=g.apply(undefined,[t.handler].concat(n)),o=t.purpose(),{cHandler:e,purpose:at(o)};var e,o},fi=function(t){return t.cHandler},di=function(t,n){return{name:at(t),handler:at(n)}},mi=function(t,n,e){var o,r,i=et(et({},e),(o=t,r={},rt(n,function(t){r[t.name()]=t.handlers(o)}),r));return ci(i,di)},gi=function(t){var n,i=_(n=t)?{can:at(!0),abort:at(!1),run:n}:n;return function(t,n){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[t,n].concat(e);i.abort.apply(undefined,r)?n.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}},pi=function(t,n,e){var o,r,i=n[e];return i?function(u,a,t,c){try{var n=Y(t,function(t,n){var e=t[a](),o=n[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return ut.value(n)}catch(e){return ut.error([e])}}("Event: "+e,"name",t,i).map(function(t){var n=V(t,function(t){return t.handler()});return Je(n)}):(o=e,r=t,ut.error(["The event ("+o+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(V(r,function(t){return t.name()}),null,2)]))},hi=function(t,i){var n=Dt(t,function(o,r){return(1===o.length?ut.value(o[0].handler()):pi(o,i,r)).map(function(t){var n=gi(t),e=1<o.length?H(i[r],function(n){return F(o,function(t){return t.name()===n})}).join(" > "):o[0].name();return Jt(r,{handler:n,purpose:at(e)})})});return Qt(n,{})},vi=function(t){return On("custom.definition",fn([vn("dom","dom",Wt(),fn([Nn("tag"),te("styles",{}),te("classes",[]),te("attributes",{}),qn("value"),qn("innerHtml")])),Nn("components"),Nn("uid"),te("events",{}),te("apis",{}),vn("eventOrder","eventOrder",jt.mergeWithThunk(at({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"],"alloy.receive":["receiving","reflecting","tooltipping"]})),An()),qn("domModification")]),t)},bi=function(t,n){var e=Ir(t,n);return e===undefined||""===e?[]:e.split(" ")},yi=function(t){return t.dom().classList!==undefined},xi=function(t,n){return r=n,i=bi(e=t,o="class").concat([r]),Fr(e,o,i.join(" ")),!0;var e,o,r,i},wi=function(t,n){return r=n,0<(i=H(bi(e=t,o="class"),function(t){return t!==r})).length?Fr(e,o,i.join(" ")):Pr(e,o),!1;var e,o,r,i},Si=function(t,n){yi(t)?t.dom().classList.add(n):xi(t,n)},ki=function(t){0===(yi(t)?t.dom().classList:bi(t,"class")).length&&Pr(t,"class")},Ci=function(t,n){yi(t)?t.dom().classList.remove(n):wi(t,n);ki(t)},Oi=function(t,n){return yi(t)&&t.dom().classList.contains(n)},_i=function(n,t){rt(t,function(t){Si(n,t)})},Ti=function(n,t){rt(t,function(t){Ci(n,t)})},Ei=function(t){return t.style!==undefined&&_(t.style.getPropertyValue)},Bi=function(t){return mr(t)},Di=_(nt.Element.prototype.attachShadow)&&_(nt.Node.prototype.getRootNode),Ai=at(Di),Mi=Di?function(t){return fe.fromDom(t.dom().getRootNode())}:pr,Fi=function(t){return fe.fromDom(t.dom().host)},Ii=function(t){return O(t.dom().shadowRoot)},Ri=function(t){var n,e,o,r,i=fr(t)?t.dom().parentNode:t.dom();return i!==undefined&&null!==i&&null!==i.ownerDocument&&(o=fe.fromDom(i),r=Mi(o),(Bi(r)?st.some(r):st.none()).fold(function(){return i.ownerDocument.body.contains(i)},(n=Ri,e=Fi,function(t){return n(e(t))})))},Vi=function(){return Pi(fe.fromDom(nt.document))},Pi=function(t){var n=t.dom().body;if(null===n||n===undefined)throw new Error("Body is not available yet");return fe.fromDom(n)},Hi=function(t,n,e){if(!w(e))throw nt.console.error("Invalid call to CSS.set. Property ",n,":: Value ",e,":: Element ",t),new Error("CSS value must be a string: "+e);Ei(t)&&t.style.setProperty(n,e)},zi=function(t,n){Ei(t)&&t.style.removeProperty(n)},Ni=function(t,n,e){var o=t.dom();Hi(o,n,e)},Li=function(t,n){var e=t.dom();_t(n,function(t,n){Hi(e,n,t)})},ji=function(t,n){var e=t.dom();_t(n,function(t,n){t.fold(function(){zi(e,n)},function(t){Hi(e,n,t)})})},Ui=function(t,n){var e=t.dom(),o=nt.window.getComputedStyle(e).getPropertyValue(n);return""!==o||Ri(t)?o:Wi(e,n)},Wi=function(t,n){return Ei(t)?t.style.getPropertyValue(n):""},Gi=function(t,n){var e=t.dom(),o=Wi(e,n);return st.from(o).filter(function(t){return 0<t.length})},Xi=function(t,n,e){var o=fe.fromTag(t);return Ni(o,n,e),Gi(o,n).isSome()},Yi=function(t,n){var e=t.dom();zi(e,n),Rr(t,"style").map(De).is("")&&Pr(t,"style")},qi=function(t){return t.dom().offsetWidth},Ki=function(t){return t.dom().value},Ji=function(t,n){if(n===undefined)throw new Error("Value.set was undefined");t.dom().value=n},$i=function(t){var n,e,o,r=fe.fromTag(t.tag);n=r,e=t.attributes,o=n.dom(),_t(e,function(t,n){Mr(o,n,t)}),_i(r,t.classes),Li(r,t.styles),t.innerHtml.each(function(t){return Ar(r,t)});var i=t.domChildren;return _r(r,i),t.value.each(function(t){Ji(r,t)}),t.uid,Kr(r,t.uid),r},Qi=function(t,n){return e=t,r=V(o=n,function(t){return Zn(t.name(),[Nn("config"),te("state",ui)])}),i=On("component.behaviours",fn(r),e.behaviours).fold(function(t){throw new Error(En(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},function(t){return t}),{list:o,data:Tt(i,function(t){var n=t.map(function(t){return{config:t.config,state:t.state.init(t.config)}});return function(){return n}})};var e,o,r,i},Zi=function(t){var n,e,o=(n=Ft(t,"behaviours").getOr({}),e=H(Ct(n),function(t){return n[t]!==undefined}),V(e,function(t){return n[t].me}));return Qi(t,o)},tu=function(t,n,e){var o,r,i,u=et(et({},(o=t).dom),{uid:o.uid,domChildren:V(o.components,function(t){return t.element()})}),a=t.domModification.fold(function(){return si({})},si),c={"alloy.base.modification":a},s=0<n.length?function(n,t,e,o){var r=et({},t);rt(e,function(t){r[t.name()]=t.exhibit(n,o)});var i=ci(r,function(t,n){return{name:t,modification:n}}),u=function(t){return z(t,function(t,n){return et(et({},n.modification),t)},{})},a=z(i.classes,function(t,n){return n.modification.concat(t)},[]),c=u(i.attributes),s=u(i.styles);return si({classes:a,attributes:c,styles:s})}(e,c,n,u):a;return i=s,et(et({},r=u),{attributes:et(et({},r.attributes),i.attributes),styles:et(et({},r.styles),i.styles),classes:r.classes.concat(i.classes)})},nu=function(t,n,e){var o,r,i,u={"alloy.base.behaviour":t.events};return o=e,r=t.eventOrder,i=mi(o,n,u),hi(i,r).getOrDie()},eu=function(t){var n,e,o,r,i,u,a,c,s,l,f,d,m,g=Qr(t),p=g.events,h=y(g,["events"]),v=(n=Ft(h,"components").getOr([]),V(n,uu)),b=et(et({},h),{events:et(et({},jr),p),components:v});return ut.value((o=function(){return m},r=se(ti),i=_n(vi(e=b)),u=Zi(e),a=u.list,c=u.data,s=tu(i,a,c),l=$i(s),f=nu(i,a,c),d=se(i.components),m={getSystem:r.get,config:function(t){var n=c;return(_(n[t.name()])?n[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(t){return _(c[t.name()])},spec:at(e),readState:function(t){return c[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return i.apis},connect:function(t){r.set(t)},disconnect:function(){r.set(Zr(o))},element:at(l),syncComponents:function(){var t=xr(l),n=U(t,function(t){return r.get().getByDom(t).fold(function(){return[]},function(t){return[t]})});d.set(n)},components:d.get,events:at(f)}))},ou=function(t){var n=fe.fromText(t);return ru({element:n})},ru=function(t){var n=Tn("external.component",ln([Nn("element"),qn("uid")]),t),e=se(Zr());n.uid.each(function(t){Kr(n.element,t)});var o={getSystem:e.get,config:st.none,hasConfigured:at(!1),connect:function(t){e.set(t)},disconnect:function(){e.set(Zr(function(){return o}))},getApis:function(){return{}},element:at(n.element),spec:at(t),readState:at("No state"),syncComponents:Z,components:at([]),events:at({})};return ri(o)},iu=$r,uu=function(n){return Ft(n,oi).fold(function(){var t=n.hasOwnProperty("uid")?n:et({uid:iu("")},n);return eu(t).getOrDie()},function(t){return t})},au=ri;function cu(o,r){var t=function(t){var n=r(t);if(n<=0||null===n){var e=Ui(t,o);return parseFloat(e)||0}return n},i=function(r,t){return N(t,function(t,n){var e=Ui(r,n),o=e===undefined?0:parseInt(e,10);return isNaN(o)?t:t+o},0)};return{set:function(t,n){if(!ot(n)&&!n.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+n);var e=t.dom();Ei(e)&&(e.style[o]=n+"px")},get:t,getOuter:t,aggregate:i,max:function(t,n,e){var o=i(t,e);return o<n?n-o:0}}}var su=cu("height",function(t){var n=t.dom();return Ri(t)?n.getBoundingClientRect().height:n.offsetHeight}),lu=function(t){return su.get(t)},fu=function(t){return su.getOuter(t)},du=function(e,o){return{left:at(e),top:at(o),translate:function(t,n){return du(e+t,o+n)}}},mu=du,gu=function(t,n){return t!==undefined?t:n!==undefined?n:0},pu=function(t){var n=t.dom().ownerDocument,e=n.body,o=n.defaultView,r=n.documentElement;if(e===t.dom())return mu(e.offsetLeft,e.offsetTop);var i=gu(o.pageYOffset,r.scrollTop),u=gu(o.pageXOffset,r.scrollLeft),a=gu(r.clientTop,e.clientTop),c=gu(r.clientLeft,e.clientLeft);return hu(t).translate(u-c,i-a)},hu=function(t){var n,e=t.dom(),o=e.ownerDocument.body;return o===e?mu(o.offsetLeft,o.offsetTop):Ri(t)?(n=e.getBoundingClientRect(),mu(n.left,n.top)):mu(0,0)},vu=cu("width",function(t){return t.dom().offsetWidth}),bu=function(t){return vu.get(t)},yu=function(t){return vu.getOuter(t)},xu=function(t){var n,e,o,r,i,u,a,c=fe.fromDom(function(t){if(Ai()&&O(t.target)){var n=fe.fromDom(t.target);if(lr(n)&&Ii(fe.fromDom(t.target))){if(t.composed&&t.composedPath){var e=t.composedPath();if(e)return q(e)}}}return st.from(t.target)}(t).getOr(t.target)),s=function(){return t.stopPropagation()},l=function(){return t.preventDefault()},f=d(l,s);return n=c,e=t.clientX,o=t.clientY,r=s,i=l,u=f,a=t,{target:at(n),x:at(e),y:at(o),stop:r,prevent:i,kill:u,raw:at(a)}},wu=function(t,n,e,o,r){var i,u,a=(i=e,u=o,function(t){i(t)&&u(xu(t))});return t.dom().addEventListener(n,a,r),{unbind:g(Su,t,n,a,r)}},Su=function(t,n,e,o){t.dom().removeEventListener(n,e,o)},ku=function(t){var n=t!==undefined?t.dom():nt.document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return mu(e,o)},Cu=function(t,n,e){(e!==undefined?e.dom():nt.document).defaultView.scrollTo(t,n)},Ou=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},_u=function(t){var n,e,o=t===undefined?nt.window:t,r=o.document,i=ku(fe.fromDom(r));return e=(n=o)===undefined?nt.window:n,st.from(e.visualViewport).fold(function(){var t=o.document.documentElement,n=t.clientWidth,e=t.clientHeight;return Ou(i.left(),i.top(),n,e)},function(t){return Ou(Math.max(t.pageLeft,i.left()),Math.max(t.pageTop,i.top()),t.width,t.height)})},Tu=function(o,t){return o.view(t).fold(at([]),function(t){var n=o.owner(t),e=Tu(o,n);return[t].concat(e)})},Eu=/* */Object.freeze({__proto__:null,view:function(t){return(t.dom()===nt.document?st.none():st.from(t.dom().defaultView.frameElement)).map(fe.fromDom)},owner:function(t){return gr(t)}}),Bu=function(o){var t,n,e,r,i=fe.fromDom(nt.document),u=ku(i);return(t=o,e=(n=Eu).owner(t),r=Tu(n,e),st.some(r)).fold(g(pu,o),function(t){var n=hu(o),e=z(t,function(t,n){var e=hu(n);return{left:t.left+e.left(),top:t.top+e.top()}},{left:0,top:0});return mu(e.left+n.left()+u.left(),e.top+n.top()+u.top())})},Du=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Au=function(t){var n=pu(t),e=yu(t),o=fu(t);return Du(n.left(),n.top(),e,o)},Mu=function(t){var n=Bu(t),e=yu(t),o=fu(t);return Du(n.left(),n.top(),e,o)},Fu=function(){return _u(nt.window)};function Iu(t,n,e,o,r){return t(e,o)?st.some(e):_(r)&&r(e)?st.none():n(e,o,r)}var Ru,Vu,Pu=function(t,n,e){for(var o=t.dom(),r=_(e)?e:at(!1);o.parentNode;){o=o.parentNode;var i=fe.fromDom(o);if(n(i))return st.some(i);if(r(i))break}return st.none()},Hu=function(t,n,e){return Iu(function(t,n){return n(t)},Pu,t,n,e)},zu=function(t,n,e){return Hu(t,n,e).isSome()},Nu=function(t,n,e){return Pu(t,function(t){return Ne(t,n)},e)},Lu=function(t,n){return e=n,r=(o=t)===undefined?nt.document:o.dom(),Le(r)?st.none():st.from(r.querySelector(e)).map(fe.fromDom);var e,o,r},ju=function(t,n,e){return Iu(function(t,n){return Ne(t,n)},Nu,t,n,e)},Uu=function(){var n=Wr("aria-owns");return{id:n,link:function(t){Fr(t,"aria-owns",n)},unlink:function(t){Pr(t,"aria-owns")}}},Wu=function(n,t){return Hu(t,function(t){if(!lr(t))return!1;var n=Ir(t,"id");return n!==undefined&&-1<n.indexOf("aria-owns")}).bind(function(t){var n=Ir(t,"id"),e=gr(t);return Lu(e,'[aria-owns="'+n+'"]')}).exists(function(t){return Gu(n,t)})},Gu=function(n,t){return zu(t,function(t){return je(t,n.element())},at(!1))||Wu(n,t)},Xu="unknown";(Vu=Ru=Ru||{})[Vu.STOP=0]="STOP",Vu[Vu.NORMAL=1]="NORMAL",Vu[Vu.LOGGING=2]="LOGGING";var Yu,qu,Ku=se({}),Ju=function(n,t,e){var o,r,i,u;switch(Ft(Ku.get(),n).orThunk(function(){var t=Ct(Ku.get());return $(t,function(t){return-1<n.indexOf(t)?st.some(Ku.get()[t]):st.none()})}).getOr(Ru.NORMAL)){case Ru.NORMAL:return e(Zu());case Ru.LOGGING:var a=(o=n,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,n,e){i.push({outcome:"cut",target:n,purpose:e})},logEventStopped:function(t,n,e){i.push({outcome:"stopped",target:n,purpose:e})},logNoParent:function(t,n,e){i.push({outcome:"no-parent",target:n,purpose:e})},logEventNoHandlers:function(t,n){i.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,e){i.push({outcome:"response",purpose:e,target:n})},write:function(){var t=(new Date).getTime();M(["mousemove","mouseover","mouseout",To()],o)||nt.console.log(o,{event:o,time:t-u,target:r.dom(),sequence:V(i,function(t){return M(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+Nr(t.target)+")":t.outcome})})}}),c=e(a);return a.write(),c;case Ru.STOP:return!0}},$u=["alloy/data/Fields","alloy/debugging/Debugging"],Qu=function(t,n,e){return Ju(t,n,e)},Zu=at({logEventCut:Z,logEventStopped:Z,logNoParent:Z,logEventNoHandlers:Z,logEventResponse:Z,write:Z}),ta=at([Nn("menu"),Nn("selectedMenu")]),na=at([Nn("item"),Nn("selectedItem")]),ea=(at(fn(na().concat(ta()))),at(fn(na()))),oa=Gn("initSize",[Nn("numColumns"),Nn("numRows")]),ra=function(){return Gn("markers",[Nn("backgroundMenu")].concat(ta()).concat(na()))},ia=function(t){return Gn("markers",V(t,Nn))},ua=function(t,n,e){!function(){var t=new Error;if(t.stack===undefined)return;var n=t.stack.split("\n");L(n,function(n){return 0<n.indexOf("alloy")&&!F($u,function(t){return-1<n.indexOf(t)})}).getOr(Xu)}();return vn(n,n,e,kn(function(e){return ut.value(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(undefined,t)})}))},aa=function(t){return ua(0,t,Ut(Z))},ca=function(t){return ua(0,t,Ut(st.none))},sa=function(t){return ua(0,t,Wt())},la=function(t){return ua(0,t,Wt())},fa=function(t,n){return ce(t,at(n))},da=function(t){return ce(t,ct)},ma=at(oa),ga=function(t,n,e,o,r,i){return{x:at(t),y:at(n),bubble:at(e),direction:at(o),boundsRestriction:at(r),label:at(i)}},pa=Vt([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),ha=pa.southeast,va=pa.southwest,ba=pa.northeast,ya=pa.northwest,xa=pa.south,wa=pa.north,Sa=pa.east,ka=pa.west,Ca=function(n,e){return function(t,n){for(var e={},o=0,r=t.length;o<r;o++){var i=t[o];e[String(i)]=n(i,o)}return e}(["left","right","top","bottom"],function(t){return Ft(e,t).map(function(t){return function(t,n){switch(n){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}}(n,t)})})},Oa=function(t){return t.x},_a=function(t,n){return t.x+t.width/2-n.width/2},Ta=function(t,n){return t.x+t.width-n.width},Ea=function(t,n){return t.y-n.height},Ba=function(t){return t.y+t.height},Da=function(t,n){return t.y+t.height/2-n.height/2},Aa=function(t,n,e){return ga(Oa(t),Ba(t),e.southeast(),ha(),Ca(t,{left:1,top:3}),"layout-se")},Ma=function(t,n,e){return ga(Ta(t,n),Ba(t),e.southwest(),va(),Ca(t,{right:0,top:3}),"layout-sw")},Fa=function(t,n,e){return ga(Oa(t),Ea(t,n),e.northeast(),ba(),Ca(t,{left:1,bottom:2}),"layout-ne")},Ia=function(t,n,e){return ga(Ta(t,n),Ea(t,n),e.northwest(),ya(),Ca(t,{right:0,bottom:2}),"layout-nw")},Ra=function(t,n,e){return ga(_a(t,n),Ea(t,n),e.north(),wa(),Ca(t,{bottom:2}),"layout-n")},Va=function(t,n,e){return ga(_a(t,n),Ba(t),e.south(),xa(),Ca(t,{top:3}),"layout-s")},Pa=function(t,n,e){return ga((o=t).x+o.width,Da(t,n),e.east(),Sa(),Ca(t,{left:0}),"layout-e");var o},Ha=function(t,n,e){return ga((o=n,t.x-o.width),Da(t,n),e.west(),ka(),Ca(t,{right:1}),"layout-w");var o},za=function(){return[Aa,Ma,Fa,Ia,Va,Ra,Pa,Ha]},Na=function(){return[Ma,Aa,Ia,Fa,Va,Ra,Pa,Ha]},La=function(){return[Fa,Ia,Aa,Ma,Ra,Va]},ja=function(){return[Aa,Ma,Fa,Ia,Va,Ra]},Ua=function(){return[Ma,Aa,Ia,Fa,Va,Ra]},Wa=function(e,o,r){return ur(function(t,n){r(t,e,o)})},Ga=function(t,n,e,o,r,i){var u=ln(t),a=Zn(n,[Kn("config",ln(t))]);return qa(u,a,n,e,o,r,i)},Xa=function(r,i,u){var t,n,e,o,a,c;return t=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=[e].concat(t);return e.config({name:at(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var n=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,t.config,t.state].concat(n))})},n=u,e=i.toString(),o=e.indexOf(")")+1,a=e.indexOf("("),c=e.substring(a+1,o-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:n,parameters:ni(c.slice(0,1).concat(c.slice(3)))}},t},Ya=function(t){return{key:t,value:undefined}},qa=function(e,t,o,r,n,i,u){var a=function(t){return Rt(t,o)?t[o]():st.none()},c=Tt(n,function(t,n){return Xa(o,t,n)}),s=Tt(i,function(t,n){return ei(t,n)}),l=et(et(et({},s),c),{revoke:g(Ya,o),config:function(t){var n=Tn(o+"-config",e,t);return{key:o,value:{config:n,me:l,configAsRaw:Lt(function(){return Tn(o+"-config",e,t)}),initialConfig:t,state:u}}},schema:function(){return t},exhibit:function(t,e){return a(t).bind(function(n){return Ft(r,"exhibit").map(function(t){return t(e,n.config,n.state)})}).getOr(si({}))},name:function(){return o},handlers:function(t){return a(t).map(function(t){return Ft(r,"events").getOr(function(){return{}})(t.config,t.state)}).getOr({})}});return l},Ka=function(t){return $t(t)},Ja=ln([Nn("fields"),Nn("name"),te("active",{}),te("apis",{}),te("state",ui),te("extra",{})]),$a=function(t){var n=Tn("Creating behaviour: "+t.name,Ja,t);return Ga(n.fields,n.name,n.active,n.apis,n.extra,n.state)},Qa=ln([Nn("branchKey"),Nn("branches"),Nn("name"),te("active",{}),te("apis",{}),te("state",ui),te("extra",{})]),Za=function(t){var n,e,o,r,i,u,a,c,s=Tn("Creating behaviour: "+t.name,Qa,t);return n=Dn(s.branchKey,s.branches),e=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,c=Zn(e,[Kn("config",a=n)]),qa(a,c,e,o,r,i,u)},tc=at(undefined),nc=/* */Object.freeze({__proto__:null,events:function(c){return qo([$o(xo(),function(r,t){var n,e,i=c.channels,o=Ct(i),u=t,a=(n=o,(e=u).universal()?n:H(n,function(t){return M(e.channels(),t)}));rt(a,function(t){var n=i[t],e=n.schema,o=Tn("channel["+t+"] data\nReceiver: "+Nr(r.element()),e,u.data());n.onReceive(r,o)})})])}}),ec=[Ln("channels",Cn(ut.value,ln([sa("onReceive"),te("schema",An())])))],oc=$a({fields:ec,name:"receiving",active:nc}),rc=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return si({classes:[],styles:n.useFixed()?{}:{position:"relative"}})}}),ic=function(t){return t.dom().focus()},uc=function(t){return void 0===t&&(t=fe.fromDom(nt.document)),st.from(t.dom().activeElement).map(fe.fromDom)},ac=function(n){return uc(Mi(n)).filter(function(t){return n.dom().contains(t.dom())})},cc=function(t,e){var o=gr(e),n=uc(o).bind(function(n){var r,i,t=function(t){return je(n,t)};return t(e)?st.some(e):(r=t,(i=function(t){for(var n=0;n<t.childNodes.length;n++){var e=fe.fromDom(t.childNodes[n]);if(r(e))return st.some(e);var o=i(t.childNodes[n]);if(o.isSome())return o}return st.none()})(e.dom()))}),r=t(e);return n.each(function(n){uc(o).filter(function(t){return je(t,n)}).fold(function(){ic(n)},Z)}),r},sc=function(t,n,e,o,r){return{position:at(t),left:at(n),top:at(e),right:at(o),bottom:at(r)}},lc=function(t,n){var e=function(t){return t+"px"};ji(t,{position:st.some(n.position()),left:n.left().map(e),top:n.top().map(e),right:n.right().map(e),bottom:n.bottom().map(e)})},fc=Vt([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),dc=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=n.x-e,h=n.y-o,v=r-(p+n.width),b=i-(h+n.height),y=st.some(p),x=st.some(h),w=st.some(v),S=st.some(b),k=st.none();return u=n.direction,a=function(){return sc(t,y,x,k,k)},c=function(){return sc(t,k,x,w,k)},s=function(){return sc(t,y,k,k,S)},l=function(){return sc(t,k,k,w,S)},f=function(){return sc(t,y,x,k,k)},d=function(){return sc(t,y,k,k,S)},m=function(){return sc(t,y,x,k,k)},g=function(){return sc(t,k,x,w,k)},u.fold(a,c,s,l,f,d,m,g)},mc=function(t,n){var e=g(Bu,n),o=t.fold(e,e,function(){var t=ku();return Bu(n).translate(-t.left(),-t.top())}),r=yu(n),i=fu(n);return Du(o.left(),o.top(),r,i)},gc=function(t,n,e){var o=mu(n,e);return t.fold(at(o),at(o),function(){var t=ku();return o.translate(-t.left(),-t.top())})},pc=(fc.none,fc.relative),hc=fc.fixed,vc=function(t,n){return e=n,{anchorBox:at(t),origin:at(e)};var e},bc=function(t,n,e,o){var r=t+n;return o<r?e:r<e?o:r},yc=function(t,n,e){return Math.min(Math.max(t,n),e)},xc=Vt([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),wc=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T,E,B,D,A,M,F,I,R,V,P=t.x(),H=t.y(),z=t.bubble().offset(),N=z.left(),L=z.top(),j=(r=o,i=t.boundsRestriction(),u=z,c=(a=function(n,e){var o="top"===n||"bottom"===n?u.top():u.left();return Ft(i,n).bind(ct).bind(function(t){return"left"===n||"top"===n?e<=t?st.some(t):st.none():t<=e?st.some(t):st.none()}).map(function(t){return t+o}).getOr(e)})("left",r.x),s=a("top",r.y),l=a("right",r.right),f=a("bottom",r.bottom),Du(c,s,l-c,f-s)),U=j.y,W=j.bottom,G=j.x,X=j.right,Y=H+L,q=(d=P+N,m=Y,g=n,p=e,v=(h=j).x,b=h.y,y=h.width,x=h.height,S=b<=m,k=(w=v<=d)&&S,C=d+g<=v+y&&m+p<=b+x,O=Math.abs(Math.min(g,w?v+y-d:v-(d+g))),_=Math.abs(Math.min(p,S?b+x-m:b-(m+p))),T=Math.max(h.x,h.right-g),E=Math.max(h.y,h.bottom-p),{originInBounds:k,sizeInBounds:C,limitX:yc(d,h.x,T),limitY:yc(m,h.y,E),deltaW:O,deltaH:_}),K=q.originInBounds,J=q.sizeInBounds,$=q.limitX,Q=q.limitY,Z=q.deltaW,tt=q.deltaH,nt=at(Q+tt-U),et=at(W-Q),ot=(B=t.direction(),A=D=et,M=nt,B.fold(D,D,M,M,D,M,A,A)),rt=at($+Z-G),it=at(X-$),ut={x:$,y:Q,width:Z,height:tt,maxHeight:ot,maxWidth:(F=t.direction(),R=I=it,V=rt,F.fold(I,V,I,V,R,R,I,V)),direction:t.direction(),classes:{on:t.bubble().classesOn(),off:t.bubble().classesOff()},label:t.label(),candidateYforTest:Y};return K&&J?xc.fit(ut):xc.nofit(ut,Z,tt)},Sc=function(t,n,e,o){Yi(n,"max-height"),Yi(n,"max-width");var r,i,u,a,c,s,l,f,d,m={width:yu(r=n),height:fu(r)};return i=o.preference,u=t,a=m,c=e,s=o.bounds,l=a.width,f=a.height,d=function(t,o,r,i){var n=t(u,a,c);return wc(n,l,f,s).fold(xc.fit,function(t,n,e){return i<e||r<n?xc.nofit(t,n,e):xc.nofit(o,r,i)})},N(i,function(t,n){var e=g(d,n);return t.fold(xc.fit,e)},xc.nofit({x:u.x,y:u.y,width:a.width,height:a.height,maxHeight:a.height,maxWidth:a.width,direction:ha(),classes:{on:[],off:[]},label:"none",candidateYforTest:u.y},-1,-1)).fold(ct,ct)},kc=function(t,n,e){var o,r;lc(t,(o=e.origin,r=n,o.fold(function(){return sc("absolute",st.some(r.x),st.some(r.y),st.none(),st.none())},function(t,n,e,o){return dc("absolute",r,t,n,e,o)},function(t,n,e,o){return dc("fixed",r,t,n,e,o)})))},Cc=function(t,n){var e,o,r;e=t,o=Math.floor(n),r=su.max(e,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]),Ni(e,"max-height",r+"px")},Oc=at(function(t,n){Cc(t,n),Li(t,{"overflow-x":"hidden","overflow-y":"auto"})}),_c=at(function(t,n){Cc(t,n)}),Tc=function(t,n,e){return t[n]===undefined?e:t[n]},Ec=function(t,n,e,o,r,i){var u,a=Tc(i,"maxHeightFunction",Oc()),c=Tc(i,"maxWidthFunction",Z),s=t.anchorBox(),l=t.origin(),f={bounds:(u=l,r.fold(function(){return u.fold(Fu,Fu,Du)},function(e){return u.fold(e,e,function(){var t=e(),n=gc(u,t.x,t.y);return Du(n.left(),n.top(),t.width,t.height)})})),origin:l,preference:o,maxHeightFunction:a,maxWidthFunction:c};Bc(s,n,e,f)},Bc=function(t,n,e,o){var r,i,u,a,c,s,l=Sc(t,n,e,o);kc(n,l,o),r=n,i=l.classes,Ti(r,i.off),_i(r,i.on),u=n,a=l,(0,o.maxHeightFunction)(u,a.maxHeight),c=n,s=l,(0,o.maxWidthFunction)(c,s.maxWidth)},Dc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],Ac=function(t,n,e){var r=function(t){return Ft(e,t).getOr([])},o=function(t,n,e){var o=X(Dc,e);return{offset:function(){return mu(t,n)},classesOn:function(){return U(e,r)},classesOff:function(){return U(o,r)}}};return{southeast:function(){return o(-t,n,["top","alignLeft"])},southwest:function(){return o(t,n,["top","alignRight"])},south:function(){return o(-t/2,n,["top","alignCentre"])},northeast:function(){return o(-t,-n,["bottom","alignLeft"])},northwest:function(){return o(t,-n,["bottom","alignRight"])},north:function(){return o(-t/2,-n,["bottom","alignCentre"])},east:function(){return o(t,-n/2,["valignCentre","left"])},west:function(){return o(-t,-n/2,["valignCentre","right"])},innerNorthwest:function(){return o(-t,n,["top","alignRight"])},innerNortheast:function(){return o(t,n,["top","alignLeft"])},innerNorth:function(){return o(-t/2,n,["top","alignCentre"])},innerSouthwest:function(){return o(-t,-n,["bottom","alignRight"])},innerSoutheast:function(){return o(t,-n,["bottom","alignLeft"])},innerSouth:function(){return o(-t/2,-n,["bottom","alignCentre"])},innerWest:function(){return o(t,-n/2,["valignCentre","right"])},innerEast:function(){return o(-t,-n/2,["valignCentre","left"])}}},Mc=function(){return Ac(0,0,{})},Fc=function(n,e){return function(t){return"rtl"===Ic(t)?e:n}},Ic=function(t){return"rtl"===Ui(t,"direction")?"rtl":"ltr"};(qu=Yu=Yu||{}).TopToBottom="toptobottom",qu.BottomToTop="bottomtotop";var Rc="data-alloy-vertical-dir",Vc=function(t){return zu(t,function(t){return lr(t)&&Ir(t,Rc)===Yu.BottomToTop})},Pc=function(){return Zn("layouts",[Nn("onLtr"),Nn("onRtl"),qn("onBottomLtr"),qn("onBottomRtl")])},Hc=function(n,t,e,o,r,i,u){var a=u.map(Vc).getOr(!1),c=t.layouts.map(function(t){return t.onLtr(n)}),s=t.layouts.map(function(t){return t.onRtl(n)}),l=a?t.layouts.bind(function(t){return t.onBottomLtr.map(function(t){return t(n)})}).or(c).getOr(r):c.getOr(e),f=a?t.layouts.bind(function(t){return t.onBottomRtl.map(function(t){return t(n)})}).or(s).getOr(i):s.getOr(o);return Fc(l,f)(n)},zc=[Nn("hotspot"),qn("bubble"),te("overrides",{}),Pc(),fa("placement",function(t,n,e){var o=n.hotspot,r=mc(e,o.element()),i=Hc(t.element(),n,ja(),Ua(),La(),[Ia,Fa,Ma,Aa,Ra,Va],st.some(n.hotspot.element()));return st.some({anchorBox:r,bubble:n.bubble.getOr(Mc()),overrides:n.overrides,layouts:i,placer:st.none()})})],Nc=[Nn("x"),Nn("y"),te("height",0),te("width",0),te("bubble",Mc()),te("overrides",{}),Pc(),fa("placement",function(t,n,e){var o=gc(e,n.x,n.y),r=Du(o.left(),o.top(),n.width,n.height),i=Hc(t.element(),n,za(),Na(),za(),Na(),st.none());return st.some({anchorBox:r,bubble:n.bubble,overrides:n.overrides,layouts:i,placer:st.none()})})],Lc=function(t,n,e,o){return{start:at(t),soffset:at(n),finish:at(e),foffset:at(o)}},jc=Vt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Uc=(jc.before,jc.on,jc.after,function(t){return t.fold(ct,ct,ct)}),Wc=Vt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Gc={domRange:Wc.domRange,relative:Wc.relative,exact:Wc.exact,exactFromRange:function(t){return Wc.exact(t.start(),t.soffset(),t.finish(),t.foffset())},getWin:function(t){var n=t.match({domRange:function(t){return fe.fromDom(t.startContainer)},relative:function(t,n){return Uc(t)},exact:function(t,n,e,o){return t}});return vr(n)},range:Lc},Xc=function(t,n,e){var o,r,i=t.document.createRange();return o=i,n.fold(function(t){o.setStartBefore(t.dom())},function(t,n){o.setStart(t.dom(),n)},function(t){o.setStartAfter(t.dom())}),r=i,e.fold(function(t){r.setEndBefore(t.dom())},function(t,n){r.setEnd(t.dom(),n)},function(t){r.setEndAfter(t.dom())}),i},Yc=function(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom(),e),i.setEnd(o.dom(),r),i},qc=function(t){return{left:at(t.left),top:at(t.top),right:at(t.right),bottom:at(t.bottom),width:at(t.width),height:at(t.height)}},Kc=Vt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Jc=function(t,n,e){return n(fe.fromDom(e.startContainer),e.startOffset,fe.fromDom(e.endContainer),e.endOffset)},$c=function(t,n){var r,e,o,i=(r=t,n.match({domRange:function(t){return{ltr:at(t),rtl:st.none}},relative:function(t,n){return{ltr:Lt(function(){return Xc(r,t,n)}),rtl:Lt(function(){return st.some(Xc(r,n,t))})}},exact:function(t,n,e,o){return{ltr:Lt(function(){return Yc(r,t,n,e,o)}),rtl:Lt(function(){return st.some(Yc(r,e,o,t,n))})}}}));return(o=(e=i).ltr()).collapsed?e.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return Kc.rtl(fe.fromDom(t.endContainer),t.endOffset,fe.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return Jc(0,Kc.ltr,o)}):Jc(0,Kc.ltr,o)};Kc.ltr,Kc.rtl;var Qc=function qF(e,o){var n=function(t){return e(t)?st.from(t.dom().nodeValue):st.none()};return{get:function(t){if(!e(t))throw new Error("Can only get "+o+" value of a "+o+" node");return n(t).getOr("")},getOption:n,set:function(t,n){if(!e(t))throw new Error("Can only set raw "+o+" value of a "+o+" node");t.dom().nodeValue=n}}}(fr,"text"),Zc=function(t){return Qc.getOption(t)},ts=["img","br"],ns=function(t){return Zc(t).filter(function(t){return 0!==t.trim().length||-1<t.indexOf("\xa0")}).isSome()||M(ts,cr(t))},es=function(t,i){var u=function(t){for(var n=xr(t),e=n.length-1;0<=e;e--){var o=n[e];if(i(o))return st.some(o);var r=u(o);if(r.isSome())return r}return st.none()};return u(t)},os=function(t,n){return e=n,r=(o=t)===undefined?nt.document:o.dom(),Le(r)?[]:V(r.querySelectorAll(e),fe.fromDom);var e,o,r},rs=function(t,n,e,o){var r,i,u,a,c,s=(i=n,u=e,a=o,(c=gr(r=t).dom().createRange()).setStart(r.dom(),i),c.setEnd(u.dom(),a),c),l=je(t,e)&&n===o;return s.collapsed&&!l},is=function(t){var n=fe.fromDom(t.anchorNode),e=fe.fromDom(t.focusNode);return rs(n,t.anchorOffset,e,t.focusOffset)?st.some(Lc(n,t.anchorOffset,e,t.focusOffset)):function(t){if(0<t.rangeCount){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return st.some(Lc(fe.fromDom(n.startContainer),n.startOffset,fe.fromDom(e.endContainer),e.endOffset))}return st.none()}(t)},us=function(t,n){var i,e,o,r,u=$c(i=t,n).match({ltr:function(t,n,e,o){var r=i.document.createRange();return r.setStart(t.dom(),n),r.setEnd(e.dom(),o),r},rtl:function(t,n,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(t.dom(),n),r}});return o=(e=u).getClientRects(),0<(r=0<o.length?o[0]:e.getBoundingClientRect()).width||0<r.height?st.some(r).map(qc):st.none()},as=function(t,n){return{element:t,offset:n}},cs=function(t,n){var e=xr(t);if(0===e.length)return as(t,n);if(n<e.length)return as(e[n],0);var o,r=e[e.length-1],i=fr(r)?(o=r,Qc.get(o).length):xr(r).length;return as(r,i)},ss=Vt([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),ls=function(t){return t.fold(ct,function(t,n,e){return t.translate(-n,-e)})},fs=function(t){return t.fold(ct,ct)},ds=function(t){return N(t,function(t,n){return t.translate(n.left(),n.top())},mu(0,0))},ms=function(t){var n=V(t,fs);return ds(n)},gs=ss.screen,ps=ss.absolute,hs=function(t,n,e){var o,r,i=gr(t.element()),u=ku(i),a=(o=t,r=vr(e.root).dom(),st.from(r.frameElement).map(fe.fromDom).filter(function(t){var n=gr(t),e=gr(o.element());return je(n,e)}).map(pu).getOr(u));return ps(a,u.left(),u.top())},vs=function(t,n,e,o){var r=t,i=n,u=e,a=o;t<0&&(r=0,u=e+t),n<0&&(i=0,a=o+n);var c=gs(mu(r,i));return st.some({point:c,width:u,height:a})},bs=function(t,l,f,d,m){return t.map(function(t){var n,e,o,r=[l,t.point],i=(n=function(){return ms(r)},e=function(){return ms(r)},o=function(){return t=V(r,ls),ds(t);var t},d.fold(n,e,o)),u={x:i.left(),y:i.top(),width:t.width,height:t.height},a=(f.showAbove?La:ja)(),c=(f.showAbove,Ua()),s=Hc(m,f,a,c,a,c,st.none());return{anchorBox:u,bubble:f.bubble.getOr(Mc()),overrides:f.overrides,layouts:s,placer:st.none()}})},ys=function(t,n){return fr(t)?{element:t,offset:n}:cs(t,n)},xs=function(n,t){return t.getSelection.getOrThunk(function(){return function(){return t=n,st.from(t.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(is);var t}})().map(function(t){var n=ys(t.start(),t.soffset()),e=ys(t.finish(),t.foffset());return Gc.range(n.element,n.offset,e.element,e.offset)})},ws=[qn("getSelection"),Nn("root"),qn("bubble"),Pc(),te("overrides",{}),te("showAbove",!1),fa("placement",function(t,n,e){var o=vr(n.root).dom(),r=hs(t,0,n),i=xs(o,n).bind(function(t){return us(o,Gc.exactFromRange(t)).orThunk(function(){var n=fe.fromText("\ufeff");return Sr(t.start(),n),us(o,Gc.exact(n,0,n,1)).map(function(t){return Er(n),t})}).bind(function(t){return vs(t.left(),t.top(),t.width(),t.height())})}),u=xs(o,n).bind(function(t){return lr(t.start())?st.some(t.start()):br(t.start())}).getOr(t.element());return bs(i,r,n,e,u)})],Ss=[Nn("node"),Nn("root"),qn("bubble"),Pc(),te("overrides",{}),te("showAbove",!1),fa("placement",function(r,i,u){var a=hs(r,0,i);return i.node.bind(function(t){var n=t.dom().getBoundingClientRect(),e=vs(n.left,n.top,n.width,n.height),o=i.node.getOr(r.element());return bs(e,a,i,u,o)})})],ks=function(t){return t.x+t.width},Cs=function(t,n){return t.x-n.width},Os=function(t,n){return t.y-n.height+t.height},_s=function(t){return t.y},Ts=function(t,n,e){return ga(ks(t),_s(t),e.southeast(),ha(),Ca(t,{left:0,top:2}),"link-layout-se")},Es=function(t,n,e){return ga(Cs(t,n),_s(t),e.southwest(),va(),Ca(t,{right:1,top:2}),"link-layout-sw")},Bs=function(t,n,e){return ga(ks(t),Os(t,n),e.northeast(),ba(),Ca(t,{left:0,bottom:3}),"link-layout-ne")},Ds=function(t,n,e){return ga(Cs(t,n),Os(t,n),e.northwest(),ya(),Ca(t,{right:1,bottom:3}),"link-layout-nw")},As=function(){return[Ts,Es,Bs,Ds]},Ms=function(){return[Es,Ts,Ds,Bs]},Fs=[Nn("item"),Pc(),te("overrides",{}),fa("placement",function(t,n,e){var o=mc(e,n.item.element()),r=Hc(t.element(),n,As(),Ms(),As(),Ms(),st.none());return st.some({anchorBox:o,bubble:Mc(),overrides:n.overrides,layouts:r,placer:st.none()})})],Is=Dn("anchor",{selection:ws,node:Ss,hotspot:zc,submenu:Fs,makeshift:Nc}),Rs=function(t,n,e,o,r){var i=vc(e.anchorBox,n);Ec(i,r.element(),e.bubble,e.layouts,o,e.overrides)},Vs=function(t,n,e,o,r,i){var u=i.map(Au);return Ps(t,n,e,o,r,u)},Ps=function(c,s,t,n,l,f){var d=Tn("positioning anchor.info",Is,n);cc(function(){Ni(l.element(),"position","fixed");var t=Gi(l.element(),"visibility");Ni(l.element(),"visibility","hidden");var n,e,o,r,i=s.useFixed()?(r=nt.document.documentElement,hc(0,0,r.clientWidth,r.clientHeight)):(e=pu((n=c).element()),o=n.element().dom().getBoundingClientRect(),pc(e.left(),e.top(),o.width,o.height)),u=d.placement,a=f.map(at).or(s.getBounds);u(c,d,i).each(function(t){t.placer.getOr(Rs)(c,i,t,a,l)}),t.fold(function(){Yi(l.element(),"visibility")},function(t){Ni(l.element(),"visibility",t)}),Gi(l.element(),"left").isNone()&&Gi(l.element(),"top").isNone()&&Gi(l.element(),"right").isNone()&&Gi(l.element(),"bottom").isNone()&&Gi(l.element(),"position").is("fixed")&&Yi(l.element(),"position")},l.element())},Hs=/* */Object.freeze({__proto__:null,position:function(t,n,e,o,r){Vs(t,n,e,o,r,st.none())},positionWithin:Vs,positionWithinBounds:Ps,getMode:function(t,n,e){return n.useFixed()?"fixed":"absolute"}}),zs=[te("useFixed",c),qn("getBounds")],Ns=$a({fields:zs,name:"positioning",active:rc,apis:Hs}),Ls=function(t){jo(t,Fo());var n=t.components();rt(n,Ls)},js=function(t){var n=t.components();rt(n,js),jo(t,Mo())},Us=function(t,n){Or(t.element(),n.element())},Ws=function(n,t){var e,o=n.components();rt((e=n).components(),function(t){return Er(t.element())}),Tr(e.element()),e.syncComponents();var r=X(o,t);rt(r,function(t){Ls(t),n.getSystem().removeFromWorld(t)}),rt(t,function(t){t.getSystem().isConnected()?Us(n,t):(n.getSystem().addToWorld(t),Us(n,t),Ri(n.element())&&js(t)),n.syncComponents()})},Gs=function(t,n){Xs(t,n,Or)},Xs=function(t,n,e){t.getSystem().addToWorld(n),e(t.element(),n.element()),Ri(t.element())&&js(n),t.syncComponents()},Ys=function(t){Ls(t),Er(t.element()),t.getSystem().removeFromWorld(t)},qs=function(n){var t=br(n.element()).bind(function(t){return n.getSystem().getByDom(t).toOption()});Ys(n),t.each(function(t){t.syncComponents()})},Ks=function(t){var n=t.components();rt(n,Ys),Tr(t.element()),t.syncComponents()},Js=function(t,n){$s(t,n,Or)},$s=function(t,n,e){e(t,n.element());var o=xr(n.element());rt(o,function(t){n.getByDom(t).each(js)})},Qs=function(n){var t=xr(n.element());rt(t,function(t){n.getByDom(t).each(Ls)}),Er(n.element())},Zs=function(n,t,e,o){e.get().each(function(t){Ks(n)});var r=t.getAttachPoint(n);Gs(r,n);var i=n.getSystem().build(o);return Gs(n,i),e.set(i),i},tl=function(t,n,e,o){var r=Zs(t,n,e,o);return n.onOpen(t,r),r},nl=function(n,e,o){o.get().each(function(t){Ks(n),qs(n),e.onClose(n,t),o.clear()})},el=function(t,n,e){return e.isOpen()},ol=function(t,n,e){var o,r,i,u,a=n.getAttachPoint(t);Ni(t.element(),"position",Ns.getMode(a)),o=t,r="visibility",i=n.cloakVisibilityAttr,u="hidden",Gi(o.element(),r).fold(function(){Pr(o.element(),i)},function(t){Fr(o.element(),i,t)}),Ni(o.element(),r,u)},rl=function(t,n,e){var o,r,i,u;o=t.element(),F(["top","left","right","bottom"],function(t){return Gi(o,t).isSome()})||Yi(t.element(),"position"),r=t,i="visibility",u=n.cloakVisibilityAttr,Rr(r.element(),u).fold(function(){return Yi(r.element(),i)},function(t){return Ni(r.element(),i,t)})},il=/* */Object.freeze({__proto__:null,cloak:ol,decloak:rl,open:tl,openWhileCloaked:function(t,n,e,o,r){ol(t,n),tl(t,n,e,o),r(),rl(t,n)},close:nl,isOpen:el,isPartOf:function(n,e,t,o){return el(0,0,t)&&t.get().exists(function(t){return e.isPartOf(n,t,o)})},getState:function(t,n,e){return e.get()},setContent:function(t,n,e,o){return e.get().map(function(){return Zs(t,n,e,o)})}}),ul=/* */Object.freeze({__proto__:null,events:function(e,o){return qo([$o(Oo(),function(t,n){nl(t,e,o)})])}}),al=[aa("onOpen"),aa("onClose"),Nn("isPartOf"),Nn("getAttachPoint"),te("cloakVisibilityAttr","data-precloak-visibility")],cl=$a({fields:al,name:"sandboxing",active:ul,apis:il,state:/* */Object.freeze({__proto__:null,init:function(){var n=se(st.none()),t=at("not-implemented");return ai({readState:t,isOpen:function(){return n.get().isSome()},clear:function(){n.set(st.none())},set:function(t){n.set(st.some(t))},get:function(){return n.get()}})}})}),sl=at("dismiss.popups"),ll=at("reposition.popups"),fl=at("mouse.released"),dl=ln([te("isExtraPart",at(!1)),Zn("fireEventInstead",[te("event",Io())])]),ml=function(t){var n,e=Tn("Dismissal",dl,t);return(n={})[sl()]={schema:ln([Nn("target")]),onReceive:function(n,t){cl.isOpen(n)&&(cl.isPartOf(n,t.target)||e.isExtraPart(n,t.target)||e.fireEventInstead.fold(function(){return cl.close(n)},function(t){return jo(n,t.event)}))}},n},gl=ln([Zn("fireEventInstead",[te("event",Ro())]),Wn("doReposition")]),pl=function(t){var n,e=Tn("Reposition",gl,t);return(n={})[ll()]={onReceive:function(n){cl.isOpen(n)&&e.fireEventInstead.fold(function(){return e.doReposition(n)},function(t){return jo(n,t.event)})}},n},hl=function(t,n,e){n.store.manager.onLoad(t,n,e)},vl=function(t,n,e){n.store.manager.onUnload(t,n,e)},bl=/* */Object.freeze({__proto__:null,onLoad:hl,onUnload:vl,setValue:function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},getValue:function(t,n,e){return n.store.manager.getValue(t,n,e)},getState:function(t,n,e){return e}}),yl=/* */Object.freeze({__proto__:null,events:function(e,o){var t=e.resetOnDom?[rr(function(t,n){hl(t,e,o)}),ir(function(t,n){vl(t,e,o)})]:[Wa(e,o,hl)];return qo(t)}}),xl=function(){var t=se(null);return ai({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})},wl=function(){var i=se({}),u=se({});return ai({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return Ft(i.get(),t).orThunk(function(){return Ft(u.get(),t)})},update:function(t){var n=i.get(),e=u.get(),o={},r={};rt(t,function(n){o[n.value]=n,Ft(n,"meta").each(function(t){Ft(t,"text").each(function(t){r[t]=n})})}),i.set(et(et({},n),o)),u.set(et(et({},e),r))},clear:function(){i.set({}),u.set({})}})},Sl=/* */Object.freeze({__proto__:null,memory:xl,dataset:wl,manual:function(){return ai({readState:function(){}})},init:function(t){return t.store.manager.state(t)}}),kl=function(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)},Cl=[qn("initialValue"),Nn("getFallbackEntry"),Nn("getDataKey"),Nn("setValue"),fa("manager",{setValue:kl,getValue:function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(t){return t})},onLoad:function(n,e,o){e.store.initialValue.each(function(t){kl(n,e,o,t)})},onUnload:function(t,n,e){e.clear()},state:wl})],Ol=[Nn("getValue"),te("setValue",Z),qn("initialValue"),fa("manager",{setValue:function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},getValue:function(t,n,e){return n.store.getValue(t)},onLoad:function(n,e,t){e.store.initialValue.each(function(t){e.store.setValue(n,t)})},onUnload:Z,state:ui.init})],_l=[qn("initialValue"),fa("manager",{setValue:function(t,n,e,o){e.set(o),n.onSetValue(t,o)},getValue:function(t,n,e){return e.get()},onLoad:function(t,n,e){n.store.initialValue.each(function(t){e.isNotSet()&&e.set(t)})},onUnload:function(t,n,e){e.clear()},state:xl})],Tl=[ne("store",{mode:"memory"},Dn("mode",{memory:_l,manual:Ol,dataset:Cl})),aa("onSetValue"),te("resetOnDom",!1)],El=$a({fields:Tl,name:"representing",active:yl,apis:bl,extra:{setValueFrom:function(t,n){var e=El.getValue(n);El.setValue(t,e)}},state:Sl}),Bl=function(o,t){return ae(o,{},V(t,function(t){return n=t.name(),e="Cannot configure "+t.name()+" for "+o,vn(n,n,Gt(),sn(function(t){return yt("The field: "+n+" is forbidden. "+e)}));var n,e}).concat([ce("dump",ct)]))},Dl=function(t){return t.dump},Al=function(t,n){return et(et({},t.dump),Ka(n))},Ml=Bl,Fl=Al,Il="placeholder",Rl=Vt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Vl=function(t){return It(t,"uiType")},Pl=function(t,n,e,o){return Vl(e)&&e.uiType===Il?(i=e,u=o,(r=t).exists(function(t){return t!==i.owner})?Rl.single(!0,at(i)):Ft(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+Ct(u)+"]\nNamespace: "+r.getOr("none")+"\nSpec: "+JSON.stringify(i,null,2))},function(t){return t.replace()})):Rl.single(!1,at(e));var r,i,u},Hl=function(i,u,a,c){return Pl(i,0,a,c).fold(function(t,n){var e=Vl(a)?n(u,a.config,a.validated):n(u),o=Ft(e,"components").getOr([]),r=U(o,function(t){return Hl(i,u,t,c)});return[et(et({},e),{components:r})]},function(t,n){if(Vl(a)){var e=n(u,a.config,a.validated);return a.validated.preprocess.getOr(ct)(e)}return n(u)})},zl=function(n,e,t,o){var r,i,u,a=Tt(o,function(t,n){return o=t,r=!1,{name:at(e=n),required:function(){return o.fold(function(t,n){return t},function(t,n){return t})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+e);return r=!0,o}};var e,o,r}),c=(r=n,i=e,u=a,U(t,function(t){return Hl(r,i,t,u)}));return _t(a,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+n.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),c},Nl=Rl.single,Ll=Rl.multiple,jl=at(Il),Ul=Vt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Wl=te("factory",{sketch:ct}),Gl=te("schema",[]),Xl=Nn("name"),Yl=vn("pname","pname",Xt(function(t){return"<alloy."+Wr(t.name)+">"}),An()),ql=ce("schema",function(){return[qn("preprocess")]}),Kl=te("defaults",at({})),Jl=te("overrides",at({})),$l=fn([Wl,Gl,Xl,Yl,Kl,Jl]),Ql=fn([Wl,Gl,Xl,Kl,Jl]),Zl=fn([Wl,Gl,Xl,Yl,Kl,Jl]),tf=fn([Wl,ql,Xl,Nn("unit"),Yl,Kl,Jl]),nf=function(t){return t.fold(st.some,st.none,st.some,st.some)},ef=function(t){var n=function(t){return t.name};return t.fold(n,n,n,n)},of=function(e,o){return function(t){var n=Tn("Converting part type",o,t);return e(n)}},rf=of(Ul.required,$l),uf=of(Ul.external,Ql),af=of(Ul.optional,Zl),cf=of(Ul.group,tf),sf=at("entirety"),lf=/* */Object.freeze({__proto__:null,required:rf,external:uf,optional:af,group:cf,asNamedPart:nf,name:ef,asCommon:function(t){return t.fold(ct,ct,ct,ct)},original:sf}),ff=function(t,n,e,o){return zt(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))},df=function(r,t){var n={};return rt(t,function(t){nf(t).each(function(e){var o=mf(r,e.pname);n[e.name]=function(t){var n=Tn("Part: "+e.name+" in "+r,fn(e.schema),t);return et(et({},o),{config:t,validated:n})}})}),n},mf=function(t,n){return{uiType:jl(),owner:t,name:n}},gf=function(t,n,e){return{uiType:jl(),owner:t,name:n,config:e,validated:{}}},pf=function(t){return U(t,function(t){return t.fold(st.none,st.some,st.none,st.none).map(function(t){return Gn(t.name,t.schema.concat([da(sf())]))}).toArray()})},hf=function(t){return V(t,ef)},vf=function(t,n,e){return o=n,i={},r={},rt(e,function(t){t.fold(function(o){i[o.pname]=Nl(!0,function(t,n,e){return o.factory.sketch(ff(t,o,n,e))})},function(t){var n=o.parts[t.name];r[t.name]=at(t.factory.sketch(ff(o,t,n[sf()]),n))},function(o){i[o.pname]=Nl(!1,function(t,n,e){return o.factory.sketch(ff(t,o,n,e))})},function(r){i[r.pname]=Ll(!0,function(n,t,e){var o=n[r.name];return V(o,function(t){return r.factory.sketch(zt(r.defaults(n,t,e),t,r.overrides(n,t)))})})})}),{internals:at(i),externals:at(r)};var o,i,r},bf=function(t,n,e){return zl(st.some(t),n,n.components,e)},yf=function(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOption()},xf=function(t,n,e){return yf(t,n,e).getOrDie("Could not find part: "+e)},wf=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return rt(e,function(t){o[t]=at(i.getByUid(r[t]))}),o},Sf=function(t,n){var e=t.getSystem();return Tt(n.partUids,function(t,n){return at(e.getByUid(t))})},kf=function(t){return Ct(t.partUids)},Cf=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return rt(e,function(t){o[t]=at(i.getByUid(r[t]).getOrDie())}),o},Of=function(n,t){var e=hf(t);return $t(V(e,function(t){return{key:t,value:n+"-"+t}}))},_f=function(n){return vn("partUids","partUids",Yt(function(t){return Of(t.uid,n)}),An())},Tf=/* */Object.freeze({__proto__:null,generate:df,generateOne:gf,schemas:pf,names:hf,substitutes:vf,components:bf,defaultUids:Of,defaultUidsSchema:_f,getAllParts:Sf,getAllPartNames:kf,getPart:yf,getPartOrDie:xf,getParts:wf,getPartsOrDie:Cf}),Ef=function(t,n,e,o,r){var i,u,a=(u=r,(0<(i=o).length?[Gn("parts",i)]:[]).concat([Nn("uid"),te("dom",{}),te("components",[]),da("originalSpec"),te("debug.sketcher",{})]).concat(u));return Tn(t+" [SpecSchema]",ln(a.concat(n)),e)},Bf=function(t,n,e,o,r){var i=Df(r),u=pf(e),a=_f(e),c=Ef(t,n,i,u,[a]),s=vf(0,c,e);return o(c,bf(t,c,s.internals()),i,s.externals())},Df=function(t){return It(t,"uid")?t:et(et({},t),{uid:$r("uid")})};var Af,Mf,Ff=ln([Nn("name"),Nn("factory"),Nn("configFields"),te("apis",{}),te("extraApis",{})]),If=ln([Nn("name"),Nn("factory"),Nn("configFields"),Nn("partFields"),te("apis",{}),te("extraApis",{})]),Rf=function(t){var i=Tn("Sketcher for "+t.name,Ff,t),n=Tt(i.apis,ii),e=Tt(i.extraApis,function(t,n){return ei(t,n)});return et(et({name:at(i.name),configFields:at(i.configFields),sketch:function(t){return n=i.name,e=i.configFields,o=i.factory,r=Df(t),o(Ef(n,e,r,[],[]),r);var n,e,o,r}},n),e)},Vf=function(t){var n=Tn("Sketcher for "+t.name,If,t),e=df(n.name,n.partFields),o=Tt(n.apis,ii),r=Tt(n.extraApis,function(t,n){return ei(t,n)});return et(et({name:at(n.name),partFields:at(n.partFields),configFields:at(n.configFields),sketch:function(t){return Bf(n.name,n.configFields,n.partFields,n.factory,t)},parts:at(e)},o),r)},Pf=function(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n},Hf=function(t){return"input"===cr(t)&&"radio"!==Ir(t,"type")||"textarea"===cr(t)},zf=/* */Object.freeze({__proto__:null,getCurrent:function(t,n,e){return n.find(t)}}),Nf=[Nn("find")],Lf=$a({fields:Nf,name:"composing",apis:zf}),jf=function(e,o,t,r){var n=os(e.element(),"."+o.highlightClass);rt(n,function(n){F(r,function(t){return t.element()===n})||(Ci(n,o.highlightClass),e.getSystem().getByDom(n).each(function(t){o.onDehighlight(e,t),jo(t,Lo())}))})},Uf=function(t,n,e,o){jf(t,n,0,[o]),Wf(t,n,e,o)||(Si(o.element(),n.highlightClass),n.onHighlight(t,o),jo(o,No()))},Wf=function(t,n,e,o){return Oi(o.element(),n.highlightClass)},Gf=function(t,n,e,o){var r=os(t.element(),"."+n.itemClass);return st.from(r[o]).fold(function(){return ut.error("No element found with index "+o)},t.getSystem().getByDom)},Xf=function(n,t,e){return Lu(n.element(),"."+t.itemClass).bind(function(t){return n.getSystem().getByDom(t).toOption()})},Yf=function(n,t,e){var o=os(n.element(),"."+t.itemClass);return(0<o.length?st.some(o[o.length-1]):st.none()).bind(function(t){return n.getSystem().getByDom(t).toOption()})},qf=function(e,n,t,o){var r=os(e.element(),"."+n.itemClass);return j(r,function(t){return Oi(t,n.highlightClass)}).bind(function(t){var n=bc(t,o,0,r.length-1);return e.getSystem().getByDom(r[n]).toOption()})},Kf=function(n,t,e){var o=os(n.element(),"."+t.itemClass);return Pf(V(o,function(t){return n.getSystem().getByDom(t).toOption()}))},Jf=/* */Object.freeze({__proto__:null,dehighlightAll:function(t,n,e){return jf(t,n,0,[])},dehighlight:function(t,n,e,o){Wf(t,n,e,o)&&(Ci(o.element(),n.highlightClass),n.onDehighlight(t,o),jo(o,Lo()))},highlight:Uf,highlightFirst:function(n,e,o){Xf(n,e).each(function(t){Uf(n,e,o,t)})},highlightLast:function(n,e,o){Yf(n,e).each(function(t){Uf(n,e,o,t)})},highlightAt:function(n,e,o,t){Gf(n,e,o,t).fold(function(t){throw new Error(t)},function(t){Uf(n,e,o,t)})},highlightBy:function(n,e,o,t){var r=Kf(n,e);L(r,t).each(function(t){Uf(n,e,o,t)})},isHighlighted:Wf,getHighlighted:function(n,t,e){return Lu(n.element(),"."+t.highlightClass).bind(function(t){return n.getSystem().getByDom(t).toOption()})},getFirst:Xf,getLast:Yf,getPrevious:function(t,n,e){return qf(t,n,0,-1)},getNext:function(t,n,e){return qf(t,n,0,1)},getCandidates:Kf}),$f=[Nn("highlightClass"),Nn("itemClass"),aa("onHighlight"),aa("onDehighlight")],Qf=$a({fields:$f,name:"highlighting",apis:Jf}),Zf=function(){return[13]},td=function(){return[27]},nd=function(){return[32]},ed=function(){return[37]},od=function(){return[38]},rd=function(){return[39]},id=function(){return[40]},ud=function(t,n,e){var o=G(t.slice(0,n)),r=G(t.slice(n+1));return L(o.concat(r),e)},ad=function(t,n,e){var o=G(t.slice(0,n));return L(o,e)},cd=function(t,n,e){var o=t.slice(0,n),r=t.slice(n+1);return L(r.concat(o),e)},sd=function(t,n,e){var o=t.slice(n+1);return L(o,e)},ld=function(e){return function(t){var n=t.raw();return M(e,n.which)}},fd=function(t){return function(n){return W(t,function(t){return t(n)})}},dd=function(t){return!0===t.raw().shiftKey},md=function(t){return!0===t.raw().ctrlKey},gd=x(dd),pd=function(t,n){return{matches:t,classification:n}},hd=function(t,n,e){n.exists(function(n){return e.exists(function(t){return je(t,n)})})||Uo(t,Vo(),{prevFocus:n,newFocus:e})},vd=function(){var r=function(t){return ac(t.element())};return{get:r,set:function(t,n){var e=r(t);t.getSystem().triggerFocus(n,t.element());var o=r(t);hd(t,e,o)}}},bd=function(){var r=function(t){return Qf.getHighlighted(t).map(function(t){return t.element()})};return{get:r,set:function(n,t){var e=r(n);n.getSystem().getByDom(t).fold(Z,function(t){Qf.highlight(n,t)});var o=r(n);hd(n,e,o)}}};(Mf=Af=Af||{}).OnFocusMode="onFocus",Mf.OnEnterOrSpaceMode="onEnterOrSpace",Mf.OnApiMode="onApi";var yd,xd=function(t,n,e,o,a){var c=function(n,e,t,o,r){var i,u,a=t(n,e,o,r);return i=a,u=e.event(),L(i,function(t){return t.matches(u)}).map(function(t){return t.classification}).bind(function(t){return t(n,e,o,r)})},r={schema:function(){return t.concat([te("focusManager",vd()),ne("focusInside","onFocus",kn(function(t){return M(["onFocus","onEnterOrSpace","onApi"],t)?ut.value(t):ut.error("Invalid value for focusInside")})),fa("handler",r),fa("state",n),fa("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==Af.OnFocusMode?st.none():a(i).map(function(e){return $o(vo(),function(t,n){e(t,i,u),n.stop()})}),n=[$o(co(),function(o,r){c(o,r,e,i,u).fold(function(){var n,e,t;n=o,e=r,t=ld(nd().concat(Zf()))(e.event()),i.focusInside===Af.OnEnterOrSpaceMode&&t&&qe(n,e)&&a(i).each(function(t){t(n,i,u),e.stop()})},function(t){r.stop()})}),$o(so(),function(t,n){c(t,n,o,i,u).each(function(t){n.stop()})})];return qo(t.toArray().concat(n))}};return r},wd=function(t){var n=[qn("onEscape"),qn("onEnter"),te("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),te("firstTabstop",0),te("useTabstopAt",at(!0)),qn("visibilitySelector")].concat([t]),u=function(t,n){var e=t.visibilitySelector.bind(function(t){return ju(n,t)}).getOr(n);return 0<lu(e)},e=function(n,e,t){var o,r,i;o=e,r=os(n.element(),o.selector),i=H(r,function(t){return u(o,t)}),st.from(i[o.firstTabstop]).each(function(t){e.focusManager.set(n,t)})},a=function(n,t,e,o,r){return r(t,e,function(t){return u(n=o,e=t)&&n.useTabstopAt(e);var n,e}).fold(function(){return o.cyclic?st.some(!0):st.none()},function(t){return o.focusManager.set(n,t),st.some(!0)})},r=function(n,t,e,o){var r,i,u=os(n.element(),e.selector);return r=n,(i=e).focusManager.get(r).bind(function(t){return ju(t,i.selector)}).bind(function(t){return j(u,g(je,t)).bind(function(t){return a(n,u,t,e,o)})})},o=at([pd(fd([dd,ld([9])]),function(t,n,e){var o=e.cyclic?ud:ad;return r(t,0,e,o)}),pd(ld([9]),function(t,n,e){var o=e.cyclic?cd:sd;return r(t,0,e,o)}),pd(ld(td()),function(n,e,t){return t.onEscape.bind(function(t){return t(n,e)})}),pd(fd([gd,ld(Zf())]),function(n,e,t){return t.onEnter.bind(function(t){return t(n,e)})})]),i=at([]);return xd(n,ui.init,o,i,function(){return st.some(e)})},Sd=wd(ce("cyclic",at(!1))),kd=wd(ce("cyclic",at(!0))),Cd=function(t,n,e){return Hf(e)&&ld(nd())(n.event())?st.none():(Go(t,e,wo()),st.some(!0))},Od=function(t,n){return st.some(!0)},_d=[te("execute",Cd),te("useSpace",!1),te("useEnter",!0),te("useControlEnter",!1),te("useDown",!1)],Td=function(t,n,e){return e.execute(t,n,t.element())},Ed=xd(_d,ui.init,function(t,n,e,o){var r=e.useSpace&&!Hf(t.element())?nd():[],i=e.useEnter?Zf():[],u=e.useDown?id():[],a=r.concat(i).concat(u);return[pd(ld(a),Td)].concat(e.useControlEnter?[pd(fd([md,ld(Zf())]),Td)]:[])},function(t,n,e,o){return e.useSpace&&!Hf(t.element())?[pd(ld(nd()),Od)]:[]},function(){return st.none()}),Bd=function(){var e=se(st.none());return ai({readState:function(){return e.get().map(function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,n){e.set(st.some({numRows:t,numColumns:n}))},getNumRows:function(){return e.get().map(function(t){return t.numRows})},getNumColumns:function(){return e.get().map(function(t){return t.numColumns})}})},Dd=/* */Object.freeze({__proto__:null,flatgrid:Bd,init:function(t){return t.state(t)}}),Ad=function(i){return function(t,n,e,o){var r=i(t.element());return Rd(r,t,n,e,o)}},Md=function(t,n){var e=Fc(t,n);return Ad(e)},Fd=function(t,n){var e=Fc(n,t);return Ad(e)},Id=function(r){return function(t,n,e,o){return Rd(r,t,n,e,o)}},Rd=function(n,e,t,o,r){return o.focusManager.get(e).bind(function(t){return n(e.element(),t,o,r)}).map(function(t){return o.focusManager.set(e,t),!0})},Vd=Id,Pd=Id,Hd=Id,zd=function(t){return!((n=t.dom()).offsetWidth<=0&&n.offsetHeight<=0);var n},Nd=function(t,n,e){var o,r=os(t,e),i=H(r,zd);return j(o=i,function(t){return je(t,n)}).map(function(t){return{index:at(t),candidates:at(o)}})},Ld=function(t,n){return j(t,function(t){return je(n,t)})},jd=function(e,t,o,n){return n(Math.floor(t/o),t%o).bind(function(t){var n=t.row()*o+t.column();return 0<=n&&n<e.length?st.some(e[n]):st.none()})},Ud=function(r,t,i,u,a){return jd(r,t,u,function(t,n){var e=t===i-1?r.length-t*u:u,o=bc(n,a,0,e-1);return st.some({row:at(t),column:at(o)})})},Wd=function(i,t,u,a,c){return jd(i,t,a,function(t,n){var e=bc(t,c,0,u-1),o=e===u-1?i.length-e*a:a,r=yc(n,0,o-1);return st.some({row:at(e),column:at(r)})})},Gd=[Nn("selector"),te("execute",Cd),ca("onEscape"),te("captureTab",!1),ma()],Xd=function(n,e,t){Lu(n.element(),e.selector).each(function(t){e.focusManager.set(n,t)})},Yd=function(r){return function(t,n,e,o){return Nd(t,n,e.selector).bind(function(t){return r(t.candidates(),t.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}},qd=function(t,n,e){return e.captureTab?st.some(!0):st.none()},Kd=Yd(function(t,n,e,o){return Ud(t,n,e,o,-1)}),Jd=Yd(function(t,n,e,o){return Ud(t,n,e,o,1)}),$d=Yd(function(t,n,e,o){return Wd(t,n,e,o,-1)}),Qd=Yd(function(t,n,e,o){return Wd(t,n,e,o,1)}),Zd=at([pd(ld(ed()),Md(Kd,Jd)),pd(ld(rd()),Fd(Kd,Jd)),pd(ld(od()),Vd($d)),pd(ld(id()),Pd(Qd)),pd(fd([dd,ld([9])]),qd),pd(fd([gd,ld([9])]),qd),pd(ld(td()),function(t,n,e){return e.onEscape(t,n)}),pd(ld(nd().concat(Zf())),function(n,e,o,t){return r=n,(i=o).focusManager.get(r).bind(function(t){return ju(t,i.selector)}).bind(function(t){return o.execute(n,e,t)});var r,i})]),tm=at([pd(ld(nd()),Od)]),nm=xd(Gd,Bd,Zd,tm,function(){return st.some(Xd)}),em=function(t,n,e,i){var u=function(t,n,e){var o,r=bc(n,i,0,e.length-1);return r===t?st.none():(o=e[r],"button"===cr(o)&&"disabled"===Ir(o,"disabled")?u(t,r,e):st.from(e[r]))};return Nd(t,e,n).bind(function(t){var n=t.index(),e=t.candidates();return u(n,n,e)})},om=[Nn("selector"),te("getInitial",st.none),te("execute",Cd),ca("onEscape"),te("executeOnMove",!1),te("allowVertical",!0)],rm=function(n,e,o){return t=n,(r=o).focusManager.get(t).bind(function(t){return ju(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var t,r},im=function(n,e,t){e.getInitial(n).orThunk(function(){return Lu(n.element(),e.selector)}).each(function(t){e.focusManager.set(n,t)})},um=function(t,n,e){return em(t,e.selector,n,-1)},am=function(t,n,e){return em(t,e.selector,n,1)},cm=function(r){return function(t,n,e,o){return r(t,n,e,o).bind(function(){return e.executeOnMove?rm(t,n,e):st.some(!0)})}},sm=function(t,n,e){return e.onEscape(t,n)},lm=at([pd(ld(nd()),Od)]),fm=xd(om,ui.init,function(t,n,e,o){var r=ed().concat(e.allowVertical?od():[]),i=rd().concat(e.allowVertical?id():[]);return[pd(ld(r),cm(Md(um,am))),pd(ld(i),cm(Fd(um,am))),pd(ld(Zf()),rm),pd(ld(nd()),rm),pd(ld(td()),sm)]},lm,function(){return st.some(im)}),dm=function(t,n,e){return st.from(t[n]).bind(function(t){return st.from(t[e]).map(function(t){return{rowIndex:n,columnIndex:e,cell:t}})})},mm=function(t,n,e,o){var r=t[n].length,i=bc(e,o,0,r-1);return dm(t,n,i)},gm=function(t,n,e,o){var r=bc(e,o,0,t.length-1),i=t[r].length,u=yc(n,0,i-1);return dm(t,r,u)},pm=function(t,n,e,o){var r=t[n].length,i=yc(e+o,0,r-1);return dm(t,n,i)},hm=function(t,n,e,o){var r=yc(e+o,0,t.length-1),i=t[r].length,u=yc(n,0,i-1);return dm(t,r,u)},vm=[Gn("selectors",[Nn("row"),Nn("cell")]),te("cycles",!0),te("previousSelector",st.none),te("execute",Cd)],bm=function(n,e,t){e.previousSelector(n).orThunk(function(){var t=e.selectors;return Lu(n.element(),t.cell)}).each(function(t){e.focusManager.set(n,t)})},ym=function(t,n){return function(e,o,i){var u=i.cycles?t:n;return ju(o,i.selectors.row).bind(function(t){var n=os(t,i.selectors.cell);return Ld(n,o).bind(function(o){var r=os(e,i.selectors.row);return Ld(r,t).bind(function(t){var n,e=(n=i,V(r,function(t){return os(t,n.selectors.cell)}));return u(e,t,o).map(function(t){return t.cell})})})})}},xm=ym(function(t,n,e){return mm(t,n,e,-1)},function(t,n,e){return pm(t,n,e,-1)}),wm=ym(function(t,n,e){return mm(t,n,e,1)},function(t,n,e){return pm(t,n,e,1)}),Sm=ym(function(t,n,e){return gm(t,e,n,-1)},function(t,n,e){return hm(t,e,n,-1)}),km=ym(function(t,n,e){return gm(t,e,n,1)},function(t,n,e){return hm(t,e,n,1)}),Cm=at([pd(ld(ed()),Md(xm,wm)),pd(ld(rd()),Fd(xm,wm)),pd(ld(od()),Vd(Sm)),pd(ld(id()),Pd(km)),pd(ld(nd().concat(Zf())),function(n,e,o){return ac(n.element()).bind(function(t){return o.execute(n,e,t)})})]),Om=at([pd(ld(nd()),Od)]),_m=xd(vm,ui.init,Cm,Om,function(){return st.some(bm)}),Tm=[Nn("selector"),te("execute",Cd),te("moveOnTab",!1)],Em=function(n,e,o){return o.focusManager.get(n).bind(function(t){return o.execute(n,e,t)})},Bm=function(n,e,t){Lu(n.element(),e.selector).each(function(t){e.focusManager.set(n,t)})},Dm=function(t,n,e){return em(t,e.selector,n,-1)},Am=function(t,n,e){return em(t,e.selector,n,1)},Mm=at([pd(ld(od()),Hd(Dm)),pd(ld(id()),Hd(Am)),pd(fd([dd,ld([9])]),function(t,n,e,o){return e.moveOnTab?Hd(Dm)(t,n,e,o):st.none()}),pd(fd([gd,ld([9])]),function(t,n,e,o){return e.moveOnTab?Hd(Am)(t,n,e,o):st.none()}),pd(ld(Zf()),Em),pd(ld(nd()),Em)]),Fm=at([pd(ld(nd()),Od)]),Im=xd(Tm,ui.init,Mm,Fm,function(){return st.some(Bm)}),Rm=[ca("onSpace"),ca("onEnter"),ca("onShiftEnter"),ca("onLeft"),ca("onRight"),ca("onTab"),ca("onShiftTab"),ca("onUp"),ca("onDown"),ca("onEscape"),te("stopSpaceKeyup",!1),qn("focusIn")],Vm=xd(Rm,ui.init,function(t,n,e){return[pd(ld(nd()),e.onSpace),pd(fd([gd,ld(Zf())]),e.onEnter),pd(fd([dd,ld(Zf())]),e.onShiftEnter),pd(fd([dd,ld([9])]),e.onShiftTab),pd(fd([gd,ld([9])]),e.onTab),pd(ld(od()),e.onUp),pd(ld(id()),e.onDown),pd(ld(ed()),e.onLeft),pd(ld(rd()),e.onRight),pd(ld(nd()),e.onSpace),pd(ld(td()),e.onEscape)]},function(t,n,e){return e.stopSpaceKeyup?[pd(ld(nd()),Od)]:[]},function(t){return t.focusIn}),Pm=Sd.schema(),Hm=kd.schema(),zm=fm.schema(),Nm=nm.schema(),Lm=_m.schema(),jm=Ed.schema(),Um=Im.schema(),Wm=Vm.schema(),Gm=Za({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:Pm,cyclic:Hm,flow:zm,flatgrid:Nm,matrix:Lm,execution:jm,menu:Um,special:Wm}),name:"keying",active:{events:function(t,n){return t.handler.toEvents(t,n)}},apis:{focusIn:function(n,e,o){e.sendFocusIn(e).fold(function(){n.getSystem().triggerFocus(n.element(),n.element())},function(t){t(n,e,o)})},setGridSize:function(t,n,e,o,r){Rt(e,"setGridSize")?e.setGridSize(o,r):nt.console.error("Layout does not support setGridSize")}},state:Dd}),Xm=function(t,n,e,o){var r=t.getSystem().build(o);Xs(t,r,e)},Ym=function(t,n,e,o){var r=qm(t);L(r,function(t){return je(o.element(),t.element())}).each(qs)},qm=function(t,n){return t.components()},Km=function(n,t,e,r,o){var i=qm(n);return st.from(i[r]).map(function(t){return Ym(n,0,0,t),o.each(function(t){Xm(n,0,function(t,n){var e,o;o=n,wr(e=t,r).fold(function(){Or(e,o)},function(t){Sr(t,o)})},t)}),t})},Jm=$a({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(t,n,e,o){Xm(t,0,Or,o)},prepend:function(t,n,e,o){Xm(t,0,Cr,o)},remove:Ym,replaceAt:Km,replaceBy:function(n,t,e,o,r){var i=qm(n);return j(i,o).bind(function(t){return Km(n,0,0,t,r)})},set:function(n,t,e,o){cc(function(){var t=V(o,n.getSystem().build);Ws(n,t)},n.element())},contents:qm})}),$m=function(t,n){var e,o;return{key:t,value:{config:{},me:(e=t,o=qo(n),$a({fields:[Nn("enabled")],name:e,active:{events:at(o)}})),configAsRaw:at({}),initialConfig:{},state:ui}}},Qm=function(t,n){n.ignore||(ic(t.element()),n.onFocus(t))},Zm=/* */Object.freeze({__proto__:null,focus:Qm,blur:function(t,n){n.ignore||t.element().dom().blur()},isFocused:function(t){return n=t.element(),e=Mi(n).dom(),n.dom()===e.activeElement;var n,e}}),tg=/* */Object.freeze({__proto__:null,exhibit:function(t,n){var e=n.ignore?{}:{attributes:{tabindex:"-1"}};return si(e)},events:function(e){return qo([$o(vo(),function(t,n){Qm(t,e),n.stop()})].concat(e.stopMousedown?[$o(no(),function(t,n){n.event().prevent()})]:[]))}}),ng=[aa("onFocus"),te("stopMousedown",!1),te("ignore",!1)],eg=$a({fields:ng,name:"focusing",active:tg,apis:Zm}),og=function(t,n,e){var o=n.aria;o.update(t,o,e.get())},rg=function(n,t,e){t.toggleClass.each(function(t){(e.get()?Si:Ci)(n.element(),t)})},ig=function(t,n,e){cg(t,n,e,!e.get())},ug=function(t,n,e){e.set(!0),rg(t,n,e),og(t,n,e)},ag=function(t,n,e){e.set(!1),rg(t,n,e),og(t,n,e)},cg=function(t,n,e,o){(o?ug:ag)(t,n,e)},sg=function(t,n,e){cg(t,n,e,n.selected)},lg=/* */Object.freeze({__proto__:null,onLoad:sg,toggle:ig,isOn:function(t,n,e){return e.get()},on:ug,off:ag,set:cg}),fg=/* */Object.freeze({__proto__:null,exhibit:function(){return si({})},events:function(t,n){var e,o,r,i=(e=t,o=n,r=ig,ar(function(t){r(t,e,o)})),u=Wa(t,n,sg);return qo(it([t.toggleOnExecute?[i]:[],[u]]))}}),dg=function(t,n,e){Fr(t.element(),"aria-expanded",e)},mg=[te("selected",!1),qn("toggleClass"),te("toggleOnExecute",!0),ne("aria",{mode:"none"},Dn("mode",{pressed:[te("syncWithExpanded",!1),fa("update",function(t,n,e){Fr(t.element(),"aria-pressed",e),n.syncWithExpanded&&dg(t,n,e)})],checked:[fa("update",function(t,n,e){Fr(t.element(),"aria-checked",e)})],expanded:[fa("update",dg)],selected:[fa("update",function(t,n,e){Fr(t.element(),"aria-selected",e)})],none:[fa("update",Z)]}))],gg=$a({fields:mg,name:"toggling",active:fg,apis:lg,state:(yd=!1,{init:function(){var n=se(yd);return{get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(yd)},readState:function(){return n.get()}}}})}),pg=function(){var t=function(t,n){n.stop(),Wo(t)};return[$o(mo(),t),$o(ko(),t),er($e()),er(no())]},hg=function(t){return qo(it([t.map(function(e){return ar(function(t,n){e(t),n.stop()})}).toArray(),pg()]))},vg="alloy.item-hover",bg="alloy.item-focus",yg=function(t){(ac(t.element()).isNone()||eg.isFocused(t))&&(eg.isFocused(t)||eg.focus(t),Uo(t,vg,{item:t}))},xg=function(t){Uo(t,bg,{item:t})},wg=at(vg),Sg=at(bg),kg=[Nn("data"),Nn("components"),Nn("dom"),te("hasSubmenu",!1),qn("toggling"),Ml("itemBehaviours",[gg,eg,Gm,El]),te("ignoreFocus",!1),te("domModification",{}),fa("builder",function(t){return{dom:t.dom,domModification:et(et({},t.domModification),{attributes:et(et(et({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Fl(t.itemBehaviours,[t.toggling.fold(gg.revoke,function(t){return gg.config(et({aria:{mode:"checked"}},t))}),eg.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){xg(t)}}),Gm.config({mode:"execution"}),El.config({store:{mode:"memory",initialValue:t.data}}),$m("item-type-events",b(pg(),[$o(io(),yg),$o(So(),eg.focus)]))]),components:t.components,eventOrder:t.eventOrder}}),te("eventOrder",{})],Cg=[Nn("dom"),Nn("components"),fa("builder",function(t){return{dom:t.dom,components:t.components,events:qo([(n=So(),$o(n,function(t,n){n.stop()}))])};var n})],Og=function(){return"item-widget"},_g=at([rf({name:"widget",overrides:function(n){return{behaviours:Ka([El.config({store:{mode:"manual",getValue:function(t){return n.data},setValue:function(){}}})])}}})]),Tg=[Nn("uid"),Nn("data"),Nn("components"),Nn("dom"),te("autofocus",!1),te("ignoreFocus",!1),Ml("widgetBehaviours",[El,eg,Gm]),te("domModification",{}),_f(_g()),fa("builder",function(e){var t=vf(Og(),e,_g()),n=bf(Og(),e,t.internals()),o=function(t){return yf(t,e,"widget").map(function(t){return Gm.focusIn(t),t})},r=function(t,n){return Hf(n.event().target())||e.autofocus&&n.setSource(t.element()),st.none()};return{dom:e.dom,components:n,domModification:e.domModification,events:qo([ar(function(t,n){o(t).each(function(t){n.stop()})}),$o(io(),yg),$o(So(),function(t,n){e.autofocus?o(t):eg.focus(t)})]),behaviours:Fl(e.widgetBehaviours,[El.config({store:{mode:"memory",initialValue:e.data}}),eg.config({ignore:e.ignoreFocus,onFocus:function(t){xg(t)}}),Gm.config({mode:"special",focusIn:e.autofocus?function(t){o(t)}:tc(),onLeft:r,onRight:r,onEscape:function(t,n){return eg.isFocused(t)||e.autofocus?(e.autofocus&&n.setSource(t.element()),st.none()):(eg.focus(t),st.some(!0))}})])}})],Eg=Dn("type",{widget:Tg,item:kg,separator:Cg}),Bg=at([cf({factory:{sketch:function(t){var n=Tn("menu.spec item",Eg,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return n.hasOwnProperty("uid")?n:et(et({},n),{uid:$r("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Dg=at([Nn("value"),Nn("items"),Nn("dom"),Nn("components"),te("eventOrder",{}),Bl("menuBehaviours",[Qf,El,Lf,Gm]),ne("movement",{mode:"menu",moveOnTab:!0},Dn("mode",{grid:[ma(),fa("config",function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}})],matrix:[fa("config",function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),Nn("rowSelector")],menu:[te("moveOnTab",!0),fa("config",function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}})]})),Ln("markers",ea()),te("fakeFocus",!1),te("focusManager",vd()),aa("onHighlight")]),Ag=at("alloy.menu-focus"),Mg=Vf({name:"Menu",configFields:Dg(),partFields:Bg(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:Al(t.menuBehaviours,[Qf.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),El.config({store:{mode:"memory",initialValue:t.value}}),Lf.config({find:st.some}),Gm.config(t.movement.config(t,t.movement))]),events:qo([$o(Sg(),function(n,e){var t=e.event();n.getSystem().getByDom(t.target()).each(function(t){Qf.highlight(n,t),e.stop(),Uo(n,Ag(),{menu:n,item:t})})}),$o(wg(),function(t,n){var e=n.event().item();Qf.highlight(t,e)})]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Fg=function(e,o,r,t){return Ft(r,t).bind(function(t){return Ft(e,t).bind(function(t){var n=Fg(e,o,r,t);return st.some([t].concat(n))})}).getOr([])},Ig=function(t,n){var e={};_t(t,function(t,n){rt(t,function(t){e[t]=n})});var o=n,r=Et(n,function(t,n){return{k:t,v:n}}),i=Tt(r,function(t,n){return[n].concat(Fg(e,o,r,n))});return Tt(e,function(t){return Ft(i,t).getOr([t])})},Rg=function(t){return"prepared"===t.type?st.some(t.menu):st.none()},Vg={init:function(){var i=se({}),u=se({}),a=se({}),c=se(st.none()),s=se({}),r=function(t,o,r){return e(t).bind(function(n){return e=t,At(i.get(),function(t,n){return t===e}).bind(function(t){return o(t).map(function(t){return{triggeredMenu:n,triggeringItem:t,triggeringPath:r}})});var e})},e=function(t){return n(t).bind(Rg)},n=function(t){return Ft(u.get(),t)},l=function(t){return Ft(i.get(),t)};return{setMenuBuilt:function(t,n){var e;u.set(et(et({},u.get()),((e={})[t]={type:"prepared",menu:n},e)))},setContents:function(t,n,e,o){c.set(st.some(t)),i.set(e),u.set(n),s.set(o);var r=Ig(o,e);a.set(r)},expand:function(e){return Ft(i.get(),e).map(function(t){var n=Ft(a.get(),e).getOr([]);return[t].concat(n)})},refresh:function(t){return Ft(a.get(),t)},collapse:function(t){return Ft(a.get(),t).bind(function(t){return 1<t.length?st.some(t.slice(1)):st.none()})},lookupMenu:n,lookupItem:l,otherMenus:function(t){var n=s.get();return X(Ct(n),t)},getPrimary:function(){return c.get().bind(e)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(st.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(t,o){var n=H(l(t).toArray(),function(t){return e(t).isSome()});return Ft(a.get(),t).bind(function(t){var e=G(n.concat(t));return function(t){for(var n=[],e=0;e<t.length;e++){var o=t[e];if(!o.isSome())return st.none();n.push(o.getOrDie())}return st.some(n)}(U(e,function(t,n){return r(t,o,e.slice(0,n+1)).fold(function(){return c.get().is(t)?[]:[st.none()]},function(t){return[st.some(t)]})}))})}}},extractPreparedMenu:Rg},Pg=at("collapse-item"),Hg=Rf({name:"TieredMenu",configFields:[la("onExecute"),la("onEscape"),sa("onOpenMenu"),sa("onOpenSubmenu"),aa("onRepositionMenu"),aa("onCollapseMenu"),te("highlightImmediately",!0),Gn("data",[Nn("primary"),Nn("menus"),Nn("expansions")]),te("fakeFocus",!1),aa("onHighlight"),aa("onHover"),ra(),Nn("dom"),te("navigateOnHover",!0),te("stayInDom",!1),Bl("tmenuBehaviours",[Gm,Qf,Lf,Jm]),te("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)},repositionMenus:function(t,n){t.repositionMenus(n)}},factory:function(a,t){var c,n,i=se(st.none()),s=Vg.init(),e=function(t){var o,r,n,e=(o=t,r=a.data.primary,n=a.data.menus,Tt(n,function(t,n){var e=function(){return Mg.sketch(et(et({},t),{value:n,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:(a.fakeFocus?bd:vd)()}))};return n===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})),i=u();return s.setContents(a.data.primary,e,a.data.expansions,i),s.getPrimary()},l=function(t){return El.getValue(t).value},u=function(t){return Tt(a.data.menus,function(t,n){return U(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})})},f=function(n,t){Qf.highlight(n,t),Qf.getHighlighted(t).orThunk(function(){return Qf.getFirst(t)}).each(function(t){Go(n,t.element(),So())})},d=function(n,t){return Pf(V(t,function(t){return n.lookupMenu(t).bind(function(t){return"prepared"===t.type?st.some(t.menu):st.none()})}))},m=function(n,t,e){var o=d(t,t.otherMenus(e));rt(o,function(t){Ti(t.element(),[a.markers.backgroundMenu]),a.stayInDom||Jm.remove(n,t)})},g=function(t,o){var r,n=(r=t,i.get().getOrThunk(function(){var e={},t=os(r.element(),"."+a.markers.item),n=H(t,function(t){return"true"===Ir(t,"aria-haspopup")});return rt(n,function(t){r.getSystem().getByDom(t).each(function(t){var n=l(t);e[n]=t})}),i.set(st.some(e)),e}));_t(n,function(t,n){var e=M(o,n);Fr(t.element(),"aria-expanded",e)})},p=function(o,r,i){return st.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return st.none();var n=t.menu,e=d(r,i.slice(1));return rt(e,function(t){Si(t.element(),a.markers.backgroundMenu)}),Ri(n.element())||Jm.append(o,au(n)),Ti(n.element(),[a.markers.backgroundMenu]),f(o,n),m(o,r,i),st.some(n)})})};(n=c=c||{})[n.HighlightSubmenu=0]="HighlightSubmenu",n[n.HighlightParent=1]="HighlightParent";var h=function(r,i,u){void 0===u&&(u=c.HighlightSubmenu);var t=l(i);return s.expand(t).bind(function(o){return g(r,o),st.from(o[0]).bind(function(e){return s.lookupMenu(e).bind(function(t){var n=function(t,n,e){if("notbuilt"!==e.type)return e.menu;var o=t.getSystem().build(e.nbMenu());return s.setMenuBuilt(n,o),o}(r,e,t);return Ri(n.element())||Jm.append(r,au(n)),a.onOpenSubmenu(r,i,n,G(o)),u===c.HighlightSubmenu?(Qf.highlightFirst(n),p(r,s,o)):(Qf.dehighlightAll(n),st.some(i))})})})},o=function(n,e){var t=l(e);return s.collapse(t).bind(function(t){return g(n,t),p(n,s,t).map(function(t){return a.onCollapseMenu(n,e,t),t})})},r=function(e){return function(n,t){return ju(t.getSource(),"."+a.markers.item).bind(function(t){return n.getSystem().getByDom(t).toOption().bind(function(t){return e(n,t).map(function(){return!0})})})}},v=qo([$o(Ag(),function(e,o){var t=o.event().item();s.lookupItem(l(t)).each(function(){var t=o.event().menu();Qf.highlight(e,t);var n=l(o.event().item());s.refresh(n).each(function(t){return m(e,s,t)})})}),ar(function(n,t){var e=t.event().target();n.getSystem().getByDom(e).each(function(t){0===l(t).indexOf("collapse-item")&&o(n,t),h(n,t,c.HighlightSubmenu).fold(function(){a.onExecute(n,t)},function(){})})}),rr(function(n,t){e(n).each(function(t){Jm.append(n,au(t)),a.onOpenMenu(n,t),a.highlightImmediately&&f(n,t)})})].concat(a.navigateOnHover?[$o(wg(),function(t,n){var e,o,r=n.event().item();e=t,o=l(r),s.refresh(o).bind(function(t){return g(e,t),p(e,s,t)}),h(t,r,c.HighlightParent),a.onHover(t,r)})]:[])),b=function(t){return Qf.getHighlighted(t).bind(Qf.getHighlighted)},y={collapseMenu:function(n){b(n).each(function(t){o(n,t)})},highlightPrimary:function(n){s.getPrimary().each(function(t){f(n,t)})},repositionMenus:function(o){s.getPrimary().bind(function(n){return b(o).bind(function(t){var n=l(t),e=Mt(s.getMenus()),o=Pf(V(e,Vg.extractPreparedMenu));return s.getTriggeringPath(n,function(t){return e=t,$(o,function(t){if(!t.getSystem().isConnected())return st.none();var n=Qf.getCandidates(t);return L(n,function(t){return l(t)===e})});var e})}).map(function(t){return{primary:n,triggeringPath:t}})}).fold(function(){var t;t=o,st.from(t.components()[0]).filter(function(t){return"menu"===Ir(t.element(),"role")}).each(function(t){a.onRepositionMenu(o,t,[])})},function(t){var n=t.primary,e=t.triggeringPath;a.onRepositionMenu(o,n,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Al(a.tmenuBehaviours,[Gm.config({mode:"special",onRight:r(function(t,n){return Hf(n.element())?st.none():h(t,n,c.HighlightSubmenu)}),onLeft:r(function(t,n){return Hf(n.element())?st.none():o(t,n)}),onEscape:r(function(t,n){return o(t,n).orThunk(function(){return a.onEscape(t,n).map(function(){return t})})}),focusIn:function(n,t){s.getPrimary().each(function(t){Go(n,t.element(),So())})}}),Qf.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),Lf.config({find:function(t){return Qf.getHighlighted(t)}}),Jm.config({})]),eventOrder:a.eventOrder,apis:y,events:v}},extraApis:{tieredData:function(t,n,e){return{primary:t,menus:n,expansions:e}},singleData:function(t,n){return{primary:t,menus:Jt(t,n),expansions:{}}},collapseItem:function(t){return{value:Wr(Pg()),meta:{text:t}}}}}),zg=Rf({name:"InlineView",configFields:[Nn("lazySink"),aa("onShow"),aa("onHide"),Qn("onEscape"),Bl("inlineBehaviours",[cl,El,oc]),Zn("fireDismissalEventInstead",[te("event",Io())]),Zn("fireRepositionEventInstead",[te("event",Ro())]),te("getRelated",st.none),te("isExtraPart",c),te("eventOrder",st.none)],factory:function(m,t){var o=function(t,n,e,o){r(t,n,e,function(){return o.map(function(t){return Au(t)})})},r=function(t,n,e,o){var r=m.lazySink(t).getOrDie();cl.openWhileCloaked(t,e,function(){return Ns.positionWithinBounds(r,n,t,o())}),El.setValue(t,st.some({mode:"position",anchor:n,getBounds:o}))},i=function(t,n,e,o){var r,i,u,a,c,s,l,f,d=(r=m,i=t,u=n,c=o,s=function(){return r.lazySink(i)},l="horizontal"===(a=e).type?{layouts:{onLtr:function(){return ja()},onRtl:function(){return Ua()}}}:{},f=function(t){return 2===t.length?l:{}},Hg.sketch({dom:{tag:"div"},data:a.data,markers:a.menu.markers,highlightImmediately:a.menu.highlightImmediately,onEscape:function(){return cl.close(i),r.onEscape.map(function(t){return t(i)}),st.some(!0)},onExecute:function(){return st.some(!0)},onOpenMenu:function(t,n){Ns.positionWithinBounds(s().getOrDie(),u,n,c())},onOpenSubmenu:function(t,n,e,o){var r=s().getOrDie();Ns.position(r,et({anchor:"submenu",item:n},f(o)),e)},onRepositionMenu:function(t,n,e){var o=s().getOrDie();Ns.positionWithinBounds(o,u,n,c()),rt(e,function(t){var n=f(t.triggeringPath);Ns.position(o,et({anchor:"submenu",item:t.triggeringItem},n),t.triggeredMenu)})}}));cl.open(t,d),El.setValue(t,st.some({mode:"menu",menu:d}))},n=function(e){cl.isOpen(e)&&El.getValue(e).each(function(t){switch(t.mode){case"menu":cl.getState(e).each(function(t){Hg.repositionMenus(t)});break;case"position":var n=m.lazySink(e).getOrDie();Ns.positionWithinBounds(n,t.anchor,e,t.getBounds())}})},e={setContent:function(t,n){cl.setContent(t,n)},showAt:function(t,n,e){o(t,n,e,st.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(t,n,e){i(t,n,e,function(){return st.none()})},showMenuWithinBounds:i,hide:function(t){cl.isOpen(t)&&(El.setValue(t,st.none()),cl.close(t))},getContent:function(t){return cl.getState(t)},reposition:n,isOpen:cl.isOpen};return{uid:m.uid,dom:m.dom,behaviours:Al(m.inlineBehaviours,[cl.config({isPartOf:function(t,n,e){return Gu(n,e)||(o=t,r=e,m.getRelated(o).exists(function(t){return Gu(t,r)}));var o,r},getAttachPoint:function(t){return m.lazySink(t).getOrDie()},onOpen:function(t){m.onShow(t)},onClose:function(t){m.onHide(t)}}),El.config({store:{mode:"memory",initialValue:st.none()}}),oc.config({channels:et(et({},ml(et({isExtraPart:t.isExtraPart},m.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),pl(et(et({},m.fireRepositionEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})),{doReposition:n})))})]),eventOrder:m.eventOrder,apis:e}},apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showWithinBounds:function(t,n,e,o,r){t.showWithinBounds(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},showMenuWithinBounds:function(t,n,e,o,r){t.showMenuWithinBounds(n,e,o,r)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)},reposition:function(t,n){t.reposition(n)}}}),Ng=function(t){return t.x},Lg=function(t,n){return t.x+t.width/2-n.width/2},jg=function(t,n){return t.x+t.width-n.width},Ug=function(t){return t.y},Wg=function(t,n){return t.y+t.height-n.height},Gg=function(t,n,e){return ga(jg(t,n),Wg(t,n),e.innerSoutheast(),ya(),Ca(t,{right:0,bottom:3}),"layout-inner-se")},Xg=function(t,n,e){return ga(Ng(t),Wg(t,n),e.innerSouthwest(),ba(),Ca(t,{left:1,bottom:3}),"layout-inner-sw")},Yg=function(t,n,e){return ga(jg(t,n),Ug(t),e.innerNortheast(),va(),Ca(t,{right:0,top:2}),"layout-inner-ne")},qg=function(t,n,e){return ga(Ng(t),Ug(t),e.innerNorthwest(),ha(),Ca(t,{left:1,top:2}),"layout-inner-nw")},Kg=function(t,n,e){return ga(Lg(t,n),Ug(t),e.innerNorth(),xa(),Ca(t,{top:2}),"layout-inner-n")},Jg=function(t,n,e){return ga(Lg(t,n),Wg(t,n),e.innerSouth(),wa(),Ca(t,{bottom:3}),"layout-inner-s")},$g=tinymce.util.Tools.resolve("tinymce.util.Delay"),Qg=Rf({name:"Button",factory:function(t){var n=hg(t.action),e=t.dom.tag,o=function(n){return Ft(t.dom,"attributes").bind(function(t){return Ft(t,n)})};return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:Fl(t.buttonBehaviours,[eg.config({}),Gm.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==e)return{role:o("role").getOr("button")};var t=o("type").getOr("button"),n=o("role").map(function(t){return{role:t}}).getOr({});return et({type:t},n)}()},eventOrder:t.eventOrder}},configFields:[te("uid",undefined),Nn("dom"),te("components",[]),Ml("buttonBehaviours",[eg,Gm]),qn("action"),qn("role"),te("eventOrder",{})]}),Zg=function(t){var n=function e(t){return t.uid!==undefined}(t)&&Rt(t,"uid")?t.uid:$r("memento");return{get:function(t){return t.getSystem().getByUid(n).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(n).toOption()},asSpec:function(){return et(et({},t),{uid:n})}}},tp=function(t){return st.from(t()["temporary-placeholder"]).getOr("!not found!")},np=function(t,n){return st.from(n()[t.toLowerCase()]).getOrThunk(function(){return tp(n)})},ep={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},op=Rf({name:"Notification",factory:function(n){var t,e,o=Zg({dom:{tag:"p",innerHtml:n.translationProvider(n.text)},behaviours:Ka([Jm.config({})])}),r=function(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}},i=function(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}},u=Zg({dom:{tag:"div",classes:n.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(0)]},i(0)],behaviours:Ka([Jm.config({})])}),a={updateProgress:function(t,n){t.getSystem().isConnected()&&u.getOpt(t).each(function(t){Jm.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(n)]},i(n)])})},updateText:function(t,n){if(t.getSystem().isConnected()){var e=o.get(t);Jm.set(e,[ou(n)])}}},c=it([n.icon.toArray(),n.level.toArray(),n.level.bind(function(t){return st.from(ep[t])}).toArray()]);return{uid:n.uid,dom:{tag:"div",attributes:{role:"alert"},classes:n.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:(t=c,e=n.iconProvider,$(t,function(t){return st.from(e()[t.toLowerCase()])}).getOrThunk(function(){return tp(e)}))}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:Ka([Jm.config({})])}].concat(n.progress?[u.asSpec()]:[]).concat(n.closeButton?[Qg.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:np("close",n.iconProvider),attributes:{"aria-label":n.translationProvider("Close")}}}],action:function(t){n.onAction(t)}})]:[]),apis:a}},configFields:[qn("level"),Nn("progress"),Nn("icon"),Nn("onAction"),Nn("text"),Nn("iconProvider"),Nn("translationProvider"),ie("closeButton",!0)],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function rp(t,u,a){var c=u.backstage;return{open:function(t,n){var e=!t.closeButton&&t.timeout&&(0<t.timeout||t.timeout<0),o=function(){n(),zg.hide(i)},r=uu(op.sketch({text:t.text,level:M(["success","error","warning","warn","info"],t.type)?t.type:undefined,progress:!0===t.progressBar,icon:st.from(t.icon),closeButton:!e,onAction:o,iconProvider:c.shared.providers.icons,translationProvider:c.shared.providers.translate})),i=uu(zg.sketch(et({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}},c.shared.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));return a.add(i),0<t.timeout&&$g.setTimeout(function(){o()},t.timeout),{close:o,moveTo:function(t,n){zg.showAt(i,{anchor:"makeshift",x:t,y:n},au(r))},moveRel:function(t,n){if("banner"!==n){var e=function(t){switch(t){case"bc-bc":return Jg;case"tc-tc":return Kg;case"tc-bc":return Ra;case"bc-tc":default:return Va}}(n),o={anchor:"node",root:Vi(),node:st.some(fe.fromDom(t)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}};zg.showAt(i,o,au(r))}else zg.showAt(i,u.backstage.shared.anchors.banner(),au(r))},text:function(t){op.updateText(r,t)},settings:t,getEl:function(){return r.element().dom()},progressBar:{value:function(t){op.updateProgress(r,t)}}}},close:function(t){t.close()},reposition:function(t){var e;rt(t,function(t){return t.moveTo(0,0)}),0<(e=t).length&&(q(e).each(function(t){return t.moveRel(null,"banner")}),rt(e,function(t,n){0<n&&t.moveRel(e[n-1].getEl(),"bc-tc")}))},getArgs:function(t){return t.settings}}}var ip,up,ap=function(e,o){var r=null;return{cancel:function(){null!==r&&(nt.clearTimeout(r),r=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null!==r&&nt.clearTimeout(r),r=nt.setTimeout(function(){e.apply(null,t),r=null},o)}}},cp=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),sp=function(o,t,n,e,r){var i=cp(o,function(t){return e=t,(n=o).isBlock(e)||M(["BR","IMG","HR","INPUT"],e.nodeName)||"false"===n.getContentEditable(e);var n,e});return st.from(i.backwards(t,n,e,r))},lp=function(e,n){return fp(fe.fromDom(e.selection.getNode())).getOrThunk(function(){var t=fe.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());return Or(t,fe.fromDom(n.extractContents())),n.insertNode(t.dom()),br(t).each(function(t){return t.dom().normalize()}),es(t,ns).map(function(t){var n;e.selection.setCursorLocation(t.dom(),"img"===cr(n=t)?1:Zc(n).fold(function(){return xr(n).length},function(t){return t.length}))}),t})},fp=function(t){return ju(t,"[data-mce-autocompleter]")},dp=function(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")},mp=function(t){return""!==t&&-1!==" \xa0\f\n\r\t\x0B".indexOf(t)},gp=function(t,n){return t.substring(n.length)},pp=function(t,o,r,i){if(void 0===i&&(i=0),!(n=o).collapsed||3!==n.startContainer.nodeType)return st.none();var n,e=t.getParent(o.startContainer,t.isBlock)||t.getRoot();return sp(t,o.startContainer,o.startOffset,function(t,n,e){return function(t,n,e){var o;for(o=n-1;0<=o;o--){var r=t.charAt(o);if(mp(r))return st.none();if(r===e)break}return st.some(o)}(e,n,r).getOr(n)},e).bind(function(t){var n=o.cloneRange();if(n.setStart(t.container,t.offset),n.setEnd(o.endContainer,o.endOffset),n.collapsed)return st.none();var e=dp(n);return 0!==e.lastIndexOf(r)||gp(e,r).length<i?st.none():st.some({text:gp(e,r),range:n,triggerChar:r})})},hp=function(o,t,r,n){return void 0===n&&(n=0),fp(fe.fromDom(t.startContainer)).fold(function(){return pp(o,t,r,n)},function(t){var n=o.createRng();n.selectNode(t.dom());var e=dp(n);return st.some({range:n,text:gp(e,r),triggerChar:r})})},vp=function(e,t){t.on("keypress compositionend",e.onKeypress.throttle),t.on("remove",e.onKeypress.cancel);var o=function(t,n){Uo(t,co(),{raw:n})};t.on("keydown",function(n){var t=function(){return e.getView().bind(Qf.getHighlighted)};8===n.which&&e.onKeypress.throttle(n),e.isActive()&&(27===n.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===n.which?(t().each(Wo),n.preventDefault()):40===n.which?(t().fold(function(){e.getView().each(Qf.highlightFirst)},function(t){o(t,n)}),n.preventDefault(),n.stopImmediatePropagation()):37!==n.which&&38!==n.which&&39!==n.which||t().each(function(t){o(t,n),n.preventDefault(),n.stopImmediatePropagation()}):13!==n.which&&38!==n.which&&40!==n.which||e.cancelIfNecessary())}),t.on("NodeChange",function(t){e.isActive()&&!e.isProcessingAction()&&fp(fe.fromDom(t.element)).isNone()&&e.cancelIfNecessary()})},bp=tinymce.util.Tools.resolve("tinymce.util.Promise"),yp=function(t,n){return{container:t,offset:n}},xp=function(t){if(t.nodeType===nt.Node.TEXT_NODE)return yp(t,t.data.length);var n=t.childNodes;return 0<n.length?xp(n[n.length-1]):yp(t,n.length)},wp=function(t,n){var e=t.childNodes;return 0<e.length&&n<e.length?wp(e[n],0):0<e.length&&t.nodeType===nt.Node.ELEMENT_NODE&&e.length===n?xp(e[e.length-1]):yp(t,n)},Sp=function(r){return function(t){var n,e,o=wp(t.startContainer,t.startOffset);return!sp(n=r,(e=o).container,e.offset,function(t,n){return 0===n?-1:n},n.getRoot()).filter(function(t){var n=t.container.data.charAt(t.offset-1);return!mp(n)}).isSome()}},kp=function(n,e){var o,r,t=e(),i=n.selection.getRng();return o=n.dom,r=i,$(t.triggerChars,function(t){return hp(o,r,t)}).bind(function(t){return Cp(n,e,t)})},Cp=function(n,t,e,o){void 0===o&&(o={});var r=t(),i=n.selection.getRng().startContainer.nodeValue,u=H(r.lookupByChar(e.triggerChar),function(t){return e.text.length>=t.minChars&&t.matches.getOrThunk(function(){return Sp(n.dom)})(e.range,i,e.text)});if(0===u.length)return st.none();var a=bp.all(V(u,function(n){return n.fetch(e.text,n.maxResults,o).then(function(t){return{matchText:e.text,items:t,columns:n.columns,onAction:n.onAction}})}));return st.some({lookupData:a,context:e})},Op=fn([jn("type"),$n("text")]),_p=fn([ce("type",function(){return"autocompleteitem"}),ce("active",function(){return!1}),ce("disabled",function(){return!1}),te("meta",{}),jn("value"),$n("text"),$n("icon")]),Tp=fn([jn("type"),jn("ch"),ee("minChars",1),te("columns",1),ee("maxResults",10),Qn("matches"),Wn("fetch"),Wn("onAction")]),Ep=function(t){var n,e,o=t.ui.registry.getAll().popups,r=Tt(o,function(t){return On("Autocompleter",Tp,t).fold(function(t){throw new Error(En(t))},function(t){return t})}),i=(n=Dt(r,function(t){return t.ch}),e={},rt(n,function(t){e[t]={}}),Ct(e)),u=Mt(r);return{dataset:r,triggerChars:i,lookupByChar:function(n){return H(u,function(t){return t.ch===n})}}};(up=ip=ip||{})[up.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",up[up.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var Bp,Dp,Ap=ip,Mp="tox-menu-nav__js",Fp="tox-collection__item",Ip="tox-swatch",Rp={normal:Mp,color:Ip},Vp="tox-collection__item--enabled",Pp="tox-collection__item-label",Hp="tox-collection__item-caret",zp="tox-collection__item--active",Np=function(t){return Ft(Rp,t).getOr(Mp)},Lp=function(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:"color"===t?"tox-swatches":"tox-menu",tieredMenu:"tox-tiered-menu"}},jp=function(t){var n=Lp(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:Np(t)}},Up=[Mg.parts().items({})],Wp=function(t,n,e){var o=Lp(e);return{dom:{tag:"div",classes:it([[o.tieredMenu]])},markers:jp(e)}},Gp=function(e,o){return function(t){var n=R(t,o);return V(n,function(t){return{dom:e,components:t}})}},Xp=function(t,e){var o=[],r=[];return rt(t,function(t,n){e(t,n)?(0<r.length&&o.push(r),r=[],It(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),V(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}})},Yp=function(n,e,t){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===n?["tox-collection--list"]:["tox-collection--grid"])},components:[Mg.parts().items({preprocess:function(t){return"auto"!==n&&1<n?Gp({tag:"div",classes:["tox-collection__group"]},n)(t):Xp(t,function(t,n){return"separator"===e[n].type})}})]}},qp=function(t){return F(t,function(t){return"icon"in t&&t.icon!==undefined})},Kp=function(t){return nt.console.error(En(t)),nt.console.log(t),st.none()},Jp=function(t,n,e,o,r){var i,u=(i=e,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Mg.parts().items({preprocess:function(t){return Xp(t,function(t,n){return"separator"===i[n].type})}})]});return{value:t,dom:u.dom,components:u.components,items:e}},$p=function(t,n,e,o,r){var i,u,a,c,s,l;return"color"===r?{value:t,dom:(l=(i=o,{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Mg.parts().items({preprocess:"auto"!==i?Gp({tag:"div",classes:["tox-swatches__row"]},i):ct})]}]})).dom,components:l.components,items:e}:"normal"===r&&"auto"===o?{value:t,dom:(l=Yp(o,e)).dom,components:l.components,items:e}:"normal"===r&&1===o?{value:t,dom:(l=Yp(1,e)).dom,components:l.components,items:e}:"normal"===r?{value:t,dom:(l=Yp(o,e)).dom,components:l.components,items:e}:"listpreview"!==r||"auto"===o?{value:t,dom:(a=n,c=o,s=Lp(r),{tag:"div",classes:it([[s.menu,"tox-menu-"+c+"-column"],a?[s.hasIcons]:[]])}),components:Up,items:e}:{value:t,dom:(l=(u=o,{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Mg.parts().items({preprocess:Gp({tag:"div",classes:["tox-collection__group"]},u)})]})).dom,components:l.components,items:e}},Qp=[ie("disabled",!1),$n("text"),$n("shortcut"),vn("value","value",Xt(function(){return Wr("menuitem-value")}),An()),te("meta",{})],Zp=fn([jn("type"),ie("active",!1),$n("icon")].concat(Qp)),th=fn([jn("type"),Un("fancytype",["inserttable","colorswatch"]),ue("onAction",Z)]),nh=fn([jn("type"),ue("onSetup",function(){return Z}),ue("onAction",Z),$n("icon")].concat(Qp)),eh=fn([jn("type"),Wn("getSubmenuItems"),ue("onSetup",function(){return Z}),$n("icon")].concat(Qp)),oh=fn([jn("type"),$n("icon"),ie("active",!1),ue("onSetup",function(){return Z}),Wn("onAction")].concat(Qp)),rh=function(t,o,n){var r=os(t.element(),"."+n);if(0<r.length){var e=j(r,function(t){var n=t.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(n-e)>o}).getOr(r.length);return st.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return st.none()},ih=function(t,n){return Ka([$m(t,n)])},uh=function(t){return ih(Wr("unnamed-events"),t)},ah=[Nn("lazySink"),Nn("tooltipDom"),te("exclusive",!0),te("tooltipComponents",[]),te("delay",300),re("mode","normal",["normal","follow-highlight"]),te("anchor",function(t){return{anchor:"hotspot",hotspot:t,layouts:{onLtr:at([Va,Ra,Aa,Fa,Ma,Ia]),onRtl:at([Va,Ra,Aa,Fa,Ma,Ia])}}}),aa("onHide"),aa("onShow")],ch=/* */Object.freeze({__proto__:null,init:function(){var e=se(st.none()),n=se(st.none()),o=function(){e.get().each(function(t){nt.clearTimeout(t)})},t=at("not-implemented");return ai({getTooltip:function(){return n.get()},isShowing:function(){return n.get().isSome()},setTooltip:function(t){n.set(st.some(t))},clearTooltip:function(){n.set(st.none())},clearTimer:o,resetTimer:function(t,n){o(),e.set(st.some(nt.setTimeout(function(){t()},n)))},readState:t})}}),sh=Wr("tooltip.exclusive"),lh=Wr("tooltip.show"),fh=Wr("tooltip.hide"),dh=function(t,n,e){t.getSystem().broadcastOn([sh],{})},mh=/* */Object.freeze({__proto__:null,hideAllExclusive:dh,setComponents:function(t,n,e,o){e.getTooltip().each(function(t){t.getSystem().isConnected()&&Jm.set(t,o)})}}),gh=$a({fields:ah,name:"tooltipping",active:/* */Object.freeze({__proto__:null,events:function(o,r){var e=function(n){r.getTooltip().each(function(t){qs(t),o.onHide(n,t),r.clearTooltip()}),r.clearTimer()};return qo(it([[$o(lh,function(t){r.resetTimer(function(){!function(n){if(!r.isShowing()){dh(n);var t=o.lazySink(n).getOrDie(),e=n.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:qo("normal"===o.mode?[$o(io(),function(t){jo(n,lh)}),$o(oo(),function(t){jo(n,fh)})]:[]),behaviours:Ka([Jm.config({})])});r.setTooltip(e),Gs(t,e),o.onShow(n,e),Ns.position(t,o.anchor(n),e)}}(t)},o.delay)}),$o(fh,function(t){r.resetTimer(function(){e(t)},o.delay)}),$o(xo(),function(t,n){M(n.channels(),sh)&&e(t)}),ir(function(t){e(t)})],"normal"===o.mode?[$o(uo(),function(t){jo(t,lh)}),$o(bo(),function(t){jo(t,fh)}),$o(io(),function(t){jo(t,lh)}),$o(oo(),function(t){jo(t,fh)})]:[$o(No(),function(t,n){jo(t,lh)}),$o(Lo(),function(t){jo(t,fh)})]]))}}),state:ch,apis:mh}),ph=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),hh=tinymce.util.Tools.resolve("tinymce.util.I18n"),vh=["input","button","textarea","select"],bh=function(t,n,e){(n.disabled()?Ch:Oh)(t,n,e)},yh=function(t,n){return!0===n.useNative&&M(vh,cr(t.element()))},xh=function(t){Fr(t.element(),"disabled","disabled")},wh=function(t){Pr(t.element(),"disabled")},Sh=function(t){Fr(t.element(),"aria-disabled","true")},kh=function(t){Fr(t.element(),"aria-disabled","false")},Ch=function(n,t,e){t.disableClass.each(function(t){Si(n.element(),t)}),(yh(n,t)?xh:Sh)(n),t.onDisabled(n)},Oh=function(n,t,e){t.disableClass.each(function(t){Ci(n.element(),t)}),(yh(n,t)?wh:kh)(n),t.onEnabled(n)},_h=function(t,n){return yh(t,n)?Vr(t.element(),"disabled"):"true"===Ir(t.element(),"aria-disabled")},Th=/* */Object.freeze({__proto__:null,enable:Oh,disable:Ch,isDisabled:_h,onLoad:bh,set:function(t,n,e,o){(o?Ch:Oh)(t,n,e)}}),Eh=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return si({classes:n.disabled()?n.disableClass.toArray():[]})},events:function(e,t){return qo([Ko(wo(),function(t,n){return _h(t,e)}),Wa(e,t,bh)])}}),Bh=[ue("disabled",c),te("useNative",!0),qn("disableClass"),aa("onDisabled"),aa("onEnabled")],Dh=$a({fields:Bh,name:"disabling",active:Eh,apis:Th}),Ah=tinymce.util.Tools.resolve("tinymce.EditorManager"),Mh=function(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))},Fh=function(t){return t.getParam("width",ph.DOM.getStyle(t.getElement(),"width"))},Ih=function(t){return st.from(t.getParam("min_width")).filter(ot)},Rh=function(t){return st.from(t.getParam("min_height")).filter(ot)},Vh=function(t){return st.from(t.getParam("max_width")).filter(ot)},Ph=function(t){return st.from(t.getParam("max_height")).filter(ot)},Hh=function(t){return!1!==t.getParam("menubar",!0,"boolean")},zh=function(t){var n=t.getParam("toolbar",!0),e=!0===n,o=w(n),r=v(n)&&0<n.length;return!Lh(t)&&(r||o||e)},Nh=function(n){var t=I(9,function(t){return n.getParam("toolbar"+(t+1),!1,"string")}),e=H(t,function(t){return"string"==typeof t});return 0<e.length?st.some(e):st.none()},Lh=function(t){return Nh(t).fold(function(){return 0<t.getParam("toolbar",[],"string[]").length},function(){return!0})};(Dp=Bp=Bp||{})["default"]="wrap",Dp.floating="floating",Dp.sliding="sliding",Dp.scrolling="scrolling";var jh,Uh,Wh=function(t){return t.getParam("toolbar_mode","","string")};(Uh=jh=jh||{}).auto="auto",Uh.top="top",Uh.bottom="bottom";var Gh=function(t){return t.getParam("toolbar_location",jh.auto,"string")},Xh=function(t){return Gh(t)===jh.bottom},Yh=function(t){var n=t.getParam("fixed_toolbar_container","","string");return 0<n.length&&t.inline?Lu(Vi(),n):st.none()},qh=function(t){return t.inline&&Yh(t).isSome()},Kh=function(t){return t.inline&&!Hh(t)&&!zh(t)&&!Lh(t)},Jh=function(t){return(t.getParam("toolbar_sticky",!1,"boolean")||t.inline)&&!qh(t)&&!Kh(t)},$h="silver.readonly",Qh=fn([Ln("readonly",Rn)]),Zh=function(t,n){var e=t.outerContainer.element();n&&(t.mothership.broadcastOn([sl()],{target:e}),t.uiMothership.broadcastOn([sl()],{target:e})),t.mothership.broadcastOn([$h],{readonly:n}),t.uiMothership.broadcastOn([$h],{readonly:n})},tv=function(t,n){t.on("init",function(){t.mode.isReadOnly()&&Zh(n,!0)}),t.on("SwitchMode",function(){return Zh(n,t.mode.isReadOnly())}),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly")},nv=function(){var t;return oc.config({channels:((t={})[$h]={schema:Qh,onReceive:function(t,n){Dh.set(t,n.readonly)}},t)})},ev=function(t){return Dh.config({disabled:t,disableClass:"tox-collection__item--state-disabled"})},ov=function(t){return Dh.config({disabled:t})},rv=function(t){return Dh.config({disabled:t,disableClass:"tox-tbtn--disabled"})},iv=function(t){return Dh.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},uv=function(t,n){var e=t.getApi(n);return function(t){t(e)}},av=function(e,o){return rr(function(t){uv(e,t)(function(t){var n=e.onSetup(t);null!==n&&n!==undefined&&o.set(n)})})},cv=function(n,e){return ir(function(t){return uv(n,t)(e.get())})},sv={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},lv=function(t){return U(t,function(t){return t.toArray()})},fv=function(t,n,e,o){var r,i,u=se(Z);return{type:"item",dom:n.dom,components:lv(n.optComponents),data:t.data,eventOrder:sv,hasSubmenu:t.triggersSubmenu,itemBehaviours:Ka([$m("item-events",[(r=t,i=e,ar(function(t,n){uv(r,t)(r.onAction),r.triggersSubmenu||i!==Ap.CLOSE_ON_EXECUTE||(jo(t,Oo()),n.stop())})),av(t,u),cv(t,u)]),ev(function(){return t.disabled||o.isReadOnly()}),nv(),Jm.config({})].concat(t.itemBehaviours))}},dv=function(t){return{value:t.value,meta:et({text:t.text.getOr("")},t.meta)}},mv=tinymce.util.Tools.resolve("tinymce.Env"),gv=function(t){return{dom:{tag:"div",classes:["tox-collection__item-icon"],innerHtml:t}}},pv=function(t){return{dom:{tag:"div",classes:[Pp]},components:[ou(hh.translate(t))]}},hv=function(t,n){return{dom:{tag:"div",classes:[Pp]},components:[{dom:{tag:t.tag,styles:t.styles},components:[ou(hh.translate(n))]}]}},vv=function(t){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:(n=t,e=mv.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},o=n.split("+"),r=V(o,function(t){var n=t.toLowerCase().trim();return It(e,n)?e[n]:t}),mv.mac?r.join(""):r.join("+"))}};var n,e,o,r},bv=function(t){return{dom:{tag:"div",classes:["tox-collection__item-checkmark"],innerHtml:np("checkmark",t)}}},yv=function(t,n,e,o,r){var i=e?n.or(st.some("")).map(gv):st.none(),u=t.checkMark,a=t.ariaLabel.map(function(t){return{attributes:{title:hh.translate(t)}}}).getOr({});return{dom:et({tag:"div",classes:[Mp,Fp].concat(r?["tox-collection__item-icon-rtl"]:[])},a),optComponents:[i,t.htmlContent.fold(function(){return t.textContent.map(o)},function(t){return st.some({dom:{tag:"div",classes:[Pp],innerHtml:t}})}),t.shortcutContent.map(vv),u,t.caret]}},xv=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],wv=["list-bull-circle","list-bull-default","list-bull-square"],Sv=function(t,r,n,i){void 0===i&&(i=st.none());var e,o,u,a,c,s=hh.isRtl()&&t.iconContent.exists(function(t){return M(wv,t)}),l=t.iconContent.map(function(t){return hh.isRtl()&&M(xv,t)?t+"-rtl":t}).map(function(t){return n=t,e=r.icons,o=i,st.from(e()[n.toLowerCase()]).or(o).getOrThunk(function(){return tp(e)});var n,e,o}),f=st.from(t.meta).fold(function(){return pv},function(t){return It(t,"style")?g(hv,t.style):pv});return"color"===t.presets?(e=t.ariaLabel,o=t.value,u=r,{dom:(a=l.getOr(""),c={tag:"div",attributes:e.map(function(t){return{title:u.translate(t)}}).getOr({}),classes:["tox-swatch"]},et(et({},c),"custom"===o?{tag:"button",classes:b(c.classes,["tox-swatches__picker-btn"]),innerHtml:a}:"remove"===o?{classes:b(c.classes,["tox-swatch--remove"]),innerHtml:a}:{attributes:et(et({},c.attributes),{"data-mce-color":o}),styles:{"background-color":o}})),optComponents:[]}):yv(t,l,n,f,s)},kv=function(t,n){var e,o=hh.translate(t),r=(e=o,ph.DOM.encode(e));if(0<n.length){var i=new RegExp(n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return r.replace(i,function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"})}return r},Cv=at(df(Og(),_g())),Ov=Wr("cell-over"),_v=Wr("cell-execute"),Tv=function(n,e,t){var o,r=function(t){return Uo(t,_v,{row:n,col:e})},i=function(t,n){n.stop(),r(t)};return uu({dom:{tag:"div",attributes:((o={role:"button"})["aria-labelledby"]=t,o)},behaviours:Ka([$m("insert-table-picker-cell",[$o(io(),eg.focus),$o(wo(),r),$o(mo(),i),$o(ko(),i)]),gg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),eg.config({onFocus:function(t){return Uo(t,Ov,{row:n,col:e})}})])})},Ev=function(t){return{value:t}},Bv=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Dv=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Av=function(t){return Bv.test(t)||Dv.test(t)},Mv=function(t){var n={value:t.value.replace(Bv,function(t,n,e,o){return n+n+e+e+o+o})},e=Dv.exec(n.value);return null===e?["FFFFFF","FF","FF","FF"]:e},Fv=function(t){var n=t.toString(16);return 1===n.length?"0"+n:n},Iv=function(t){var n=Fv(t.red)+Fv(t.green)+Fv(t.blue);return Ev(n)},Rv=Math.min,Vv=Math.max,Pv=Math.round,Hv=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,zv=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,Nv=function(t,n,e,o){return{red:t,green:n,blue:e,alpha:o}},Lv=function(t){var n=parseInt(t,10);return n.toString()===t&&0<=n&&n<=255},jv=function(t){var n,e,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100;if(i=Vv(0,Rv(i,1)),u=Vv(0,Rv(u,1)),0===i)return n=e=o=Pv(255*u),Nv(n,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),l=u-c;switch(Math.floor(a)){case 0:n=c,e=s,o=0;break;case 1:n=s,e=c,o=0;break;case 2:n=0,e=c,o=s;break;case 3:n=0,e=s,o=c;break;case 4:n=s,e=0,o=c;break;case 5:n=c,e=0,o=s;break;default:n=e=o=0}return n=Pv(255*(n+l)),e=Pv(255*(e+l)),o=Pv(255*(o+l)),Nv(n,e,o,1)},Uv=function(t){var n=Mv(t),e=parseInt(n[1],16),o=parseInt(n[2],16),r=parseInt(n[3],16);return Nv(e,o,r,1)},Wv=function(t,n,e,o){var r=parseInt(t,10),i=parseInt(n,10),u=parseInt(e,10),a=parseFloat(o);return Nv(r,i,u,a)},Gv=function(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"},Xv=Nv(255,0,0,1),Yv=function(t,n){return t.fire("ResizeContent",n)},qv=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),Kv="tinymce-custom-colors";var Jv="choiceitem",$v=[{type:Jv,text:"Light Green",value:"#BFEDD2"},{type:Jv,text:"Light Yellow",value:"#FBEEB8"},{type:Jv,text:"Light Red",value:"#F8CAC6"},{type:Jv,text:"Light Purple",value:"#ECCAFA"},{type:Jv,text:"Light Blue",value:"#C2E0F4"},{type:Jv,text:"Green",value:"#2DC26B"},{type:Jv,text:"Yellow",value:"#F1C40F"},{type:Jv,text:"Red",value:"#E03E2D"},{type:Jv,text:"Purple",value:"#B96AD9"},{type:Jv,text:"Blue",value:"#3598DB"},{type:Jv,text:"Dark Turquoise",value:"#169179"},{type:Jv,text:"Orange",value:"#E67E23"},{type:Jv,text:"Dark Red",value:"#BA372A"},{type:Jv,text:"Dark Purple",value:"#843FA1"},{type:Jv,text:"Dark Blue",value:"#236FA1"},{type:Jv,text:"Light Gray",value:"#ECF0F1"},{type:Jv,text:"Medium Gray",value:"#CED4D9"},{type:Jv,text:"Gray",value:"#95A5A6"},{type:Jv,text:"Dark Gray",value:"#7E8C8D"},{type:Jv,text:"Navy Blue",value:"#34495E"},{type:Jv,text:"Black",value:"#000000"},{type:Jv,text:"White",value:"#ffffff"}],Qv=function KF(e){void 0===e&&(e=10);var t,n=qv.getItem(Kv),o=w(n)?JSON.parse(n):[],r=e-(t=o).length<0?t.slice(0,e):t,i=function(t){r.splice(t,1)};return{add:function(t){var n;(-1===(n=A(r,t))?st.none():st.some(n)).each(i),r.unshift(t),r.length>e&&r.pop(),qv.setItem(Kv,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),Zv=function(t){return!1!==t.getParam("custom_colors")},tb=function(t){var n=t.getParam("color_map");return n!==undefined?function(t){var n=[],u=nt.document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(t,n){var e=n/255;return("0"+Math.round(t*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=function(t){if(/^[0-9A-Fa-f]{6}$/.test(t))return"#"+t.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=t,a.fillRect(0,0,1,1);var n=a.getImageData(0,0,1,1).data,e=n[0],o=n[1],r=n[2],i=n[3];return"#"+c(e,i)+c(o,i)+c(r,i)},o=0;o<t.length;o+=2)n.push({text:t[o+1],value:e(t[o]),type:"choiceitem"});return n}(n):$v},nb=function(t){Qv.add(t)},eb=function(i){i.addCommand("mceApplyTextcolor",function(t,n){var e,o,r;o=t,r=n,(e=i).undoManager.transact(function(){e.focus(),e.formatter.apply(o,{value:r}),e.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var n,e;e=t,(n=i).undoManager.transact(function(){n.focus(),n.formatter.remove(e,{value:null},null,!0),n.nodeChanged()})})},ob=function(t){var n,e,o=tb(t),r=(n=o.length,Math.max(5,Math.ceil(Math.sqrt(n))));return e=r,t.getParam("color_cols",e,"number")},rb=function(n,e,t,o){"custom"===t?lb(n)(function(t){t.each(function(t){nb(t),n.execCommand("mceApplyTextcolor",e,t),o(t)})},"#000000"):"remove"===t?(o(""),n.execCommand("mceRemoveTextcolor",e)):(o(t),n.execCommand("mceApplyTextcolor",e,t))},ib=function(t,n){return t.concat(V(Qv.state(),function(t){return{type:Jv,text:t,value:t}}).concat((o={type:e="choiceitem",text:"Remove color",icon:"color-swatch-remove-color",value:"remove"},n?[o,{type:e,text:"Custom color",icon:"color-picker",value:"custom"}]:[o])));var e,o},ub=function(n,e){return function(t){t(ib(n,e))}},ab=function(t,n,e){var o,r;o="forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,t.setIconFill(o,r),t.setIconStroke(o,r)},cb=function(i,e,u,t,o){i.ui.registry.addSplitButton(e,{tooltip:t,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){var t,o,r;return st.from((o=u,(t=i).dom.getParents(t.selection.getStart(),function(t){var n;(n=t.style["forecolor"===o?"color":"background-color"])&&(r=r||n)}),r)).bind(function(t){return function(t){if("transparent"===t)return st.some(Nv(0,0,0,0));var n=Hv.exec(t);if(null!==n)return st.some(Wv(n[1],n[2],n[3],"1"));var e=zv.exec(t);return null!==e?st.some(Wv(e[1],e[2],e[3],e[4])):st.none()}(t).map(function(t){var n=Iv(t).value;return Be(e.toLowerCase(),n)})}).getOr(!1)},columns:ob(i),fetch:ub(tb(i),Zv(i)),onAction:function(t){null!==o.get()&&rb(i,u,o.get(),function(){})},onItemAction:function(t,n){rb(i,u,n,function(t){var n;o.set(t),n={name:e,color:t},i.fire("TextColorChange",n)})},onSetup:function(n){null!==o.get()&&ab(n,e,o.get());var t=function(t){t.name===e&&ab(n,t.name,t.color)};return i.on("TextColorChange",t),function(){i.off("TextColorChange",t)}}})},sb=function(n,t,e,o){n.ui.registry.addNestedMenuItem(t,{text:o,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(t){rb(n,e,t.value,Z)}}]}})},lb=function(i){return function(t,n){var e,o={colorpicker:n},r=(e=t,function(t){var n=t.getData();e(st.from(n.colorpicker)),t.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(t,n){"hex-valid"===n.name&&(n.value?t.enable("ok"):t.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){t(st.none())}})}},fb=function(t,n,e,o,r,i,u,a){var c=qp(n),s=db(n,e,o,"color"!==r?"normal":"color",i,u,a);return $p(t,c,s,o,r)},db=function(e,o,r,i,u,a,c){return Pf(V(e,function(n){return"choiceitem"===n.type?On("choicemenuitem",Zp,n).fold(Kp,function(t){return st.some(function(n,t,e,o,r,i,u,a){void 0===a&&(a=!0);var c=Sv({presets:e,textContent:t?n.text:st.none(),htmlContent:st.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:t?n.shortcut:st.none(),checkMark:t?st.some(bv(u.icons)):st.none(),caret:st.none(),value:n.value},u,a);return zt(fv({data:dv(n),disabled:n.disabled,getApi:function(n){return{setActive:function(t){gg.set(n,t)},isActive:function(){return gg.isOn(n)},isDisabled:function(){return Dh.isDisabled(n)},setDisabled:function(t){return Dh.set(n,t)}}},onAction:function(t){return o(n.value)},onSetup:function(t){return t.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},c,i,u),{toggling:{toggleClass:Vp,toggleOnExecute:!1,selected:n.active}})}(t,1===r,i,o,a(n.value),u,c,qp(e)))}):st.none()}))},mb=function(t,n){var e=jp(n);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===n?"tox-swatches__row":"tox-collection__group")}};var gb,pb,hb={inserttable:function(o){var t=Wr("size-label"),i=function(t,n,e){for(var o=[],r=0;r<n;r++){for(var i=[],u=0;u<e;u++)i.push(Tv(r,u,t));o.push(i)}return o}(t,10,10),u=Zg({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[ou("0x0")],behaviours:Ka([Jm.config({})])});return{type:"widget",data:{value:Wr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Cv().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:U(i,function(t){return V(t,au)}).concat(u.asSpec()),behaviours:Ka([$m("insert-table-picker",[nr(Ov,function(t,n,e){var o=e.event().row(),r=e.event().col();!function(t,n,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)gg.set(t[i][u],i<=n&&u<=e)}(i,o,r,10,10),Jm.set(u.get(t),[ou(r+1+"x"+(o+1))])}),nr(_v,function(t,n,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),jo(t,Oo())})]),Gm.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function JF(n,t){var e=ib(t.colorinput.getColors(),t.colorinput.hasCustomColors()),o=t.colorinput.getColorCols(),r=fb(Wr("menu-value"),e,function(t){n.onAction({value:t})},o,"color",Ap.CLOSE_ON_EXECUTE,function(){return!1},t.shared.providers),i=et(et({},r),{markers:jp("color"),movement:mb(o,"color")});return{type:"widget",data:{value:Wr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Cv().widget(Mg.sketch(i))]}}},vb=function(n,e,t,o,r,i,u,a){void 0===a&&(a=!0);var c,s,l=Sv({presets:o,textContent:st.none(),htmlContent:t?n.text.map(function(t){return kv(t,e)}):st.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:st.none(),checkMark:st.none(),caret:st.none(),value:n.value},u.providers,a,n.icon);return fv({data:dv(n),disabled:n.disabled,getApi:function(){return{}},onAction:function(t){return r(n.value,n.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:(c=n.meta,s=u,Ft(c,"tooltipWorker").map(function(e){return[gh.config({lazySink:s.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{anchor:"submenu",item:t,overrides:{maxHeightFunction:_c}}},mode:"follow-highlight",onShow:function(n,t){e(function(t){gh.setComponents(n,[ru({element:fe.fromDom(t)})])})}})]}).getOr([]))},l,i,u.providers)},bb=function(t){var n=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:et({tag:"div",classes:[Fp,"tox-collection__group-heading"]},n),components:[]}},yb=function(t,n,e,o){void 0===o&&(o=!0);var r=Sv({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,caret:st.none(),checkMark:st.none(),shortcutContent:t.shortcut},e,o);return fv({data:dv(t),getApi:function(n){return{isDisabled:function(){return Dh.isDisabled(n)},setDisabled:function(t){return Dh.set(n,t)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e)},xb=function(t,n,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i,u,a=r?(u=e.icons,{dom:{tag:"div",classes:[Hp],innerHtml:np("chevron-down",u)}}):(i=e.icons,{dom:{tag:"div",classes:[Hp],innerHtml:np("chevron-right",i)}}),c=Sv({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,caret:st.some(a),checkMark:st.none(),shortcutContent:t.shortcut},e,o);return fv({data:dv(t),getApi:function(n){return{isDisabled:function(){return Dh.isDisabled(n)},setDisabled:function(t){return Dh.set(n,t)}}},disabled:t.disabled,onAction:Z,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},c,n,e)},wb=function(t,n,e,o){void 0===o&&(o=!0);var r=Sv({iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,checkMark:st.some(bv(e.icons)),caret:st.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,o);return zt(fv({data:dv(t),disabled:t.disabled,getApi:function(n){return{setActive:function(t){gg.set(n,t)},isActive:function(){return gg.isOn(n)},isDisabled:function(){return Dh.isDisabled(n)},setDisabled:function(t){return Dh.set(n,t)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e),{toggling:{toggleClass:Vp,toggleOnExecute:!1,selected:t.active}})},Sb=function(n,e){return t=hb,o=n.fancytype,(Object.prototype.hasOwnProperty.call(t,o)?st.some(t[o]):st.none()).map(function(t){return t(n,e)});var t,o};(pb=gb=gb||{})[pb.ContentFocus=0]="ContentFocus",pb[pb.UiFocus=1]="UiFocus";var kb=function(t,n,e,o,r){var i=e.shared.providers,u=function(t){return r?et(et({},t),{shortcut:st.none(),icon:t.text.isSome()?st.none():t.icon}):t};switch(t.type){case"menuitem":return On("menuitem",nh,t).fold(Kp,function(t){return st.some(yb(u(t),n,i,o))});case"nestedmenuitem":return On("nestedmenuitem",eh,t).fold(Kp,function(t){return st.some(xb(u(t),n,i,o,r))});case"togglemenuitem":return On("togglemenuitem",oh,t).fold(Kp,function(t){return st.some(wb(u(t),n,i,o))});case"separator":return On("separatormenuitem",Op,t).fold(Kp,function(t){return st.some(bb(t))});case"fancymenuitem":return On("fancymenuitem",th,t).fold(Kp,function(t){return Sb(u(t),e)});default:return nt.console.error("Unknown item in general menu",t),st.none()}},Cb=function(t,n,e,o,r,i){var u=1===o,a=!u||qp(t);return Pf(V(t,function(t){return"separator"===t.type?On("Autocompleter.Separator",Op,t).fold(Kp,function(t){return st.some(bb(t))}):On("Autocompleter.Item",_p,t).fold(Kp,function(t){return st.some(vb(t,n,u,"normal",e,r,i,a))})}))},Ob=function(t,n,e,o,r){var i=qp(n),u=Pf(V(n,function(t){var n=function(t){return kb(t,e,o,(n=t,r?!n.hasOwnProperty("text"):i),r);var n};return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?n(et(et({},t),{disabled:!0})):n(t)}));return(r?Jp:$p)(t,i,u,1,"normal")},_b=function(t){return Hg.singleData(t.value,t)},Tb=function(d,c){var e=se(st.none()),s=se(!1),m=uu(zg.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:Ka([$m("dismissAutocompleter",[$o(Io(),function(){return f()})])]),lazySink:c.getSink})),o=function(){return e.get().isSome()},l=function(){o()&&zg.hide(m)},f=function(){if(o()){var t=e.get().map(function(t){return t.element});fp(t.getOr(fe.fromDom(d.selection.getNode()))).each(Br),l(),e.set(st.none()),s.set(!1)}},r=Lt(function(){return Ep(d)}),g=function(t,n,e,o){t.matchLength=n.text.length;var r,i,u,a,c,s,l,f=$(e,function(t){return st.from(t.columns)}).getOr(1);zg.showAt(m,{anchor:"node",root:fe.fromDom(d.getBody()),node:st.from(t.element)},Mg.sketch((r=$p("autocompleter-value",!0,o,f,"normal"),i=f,u=gb.ContentFocus,a="normal",c=(u===gb.ContentFocus?bd:vd)(),s=mb(i,a),l=jp(a),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:l.selectedItem,item:l.item},movement:s,fakeFocus:u===gb.ContentFocus,focusManager:c,menuBehaviours:uh("auto"!==i?[]:[rr(function(o,t){rh(o,4,l.item).each(function(t){var n=t.numColumns,e=t.numRows;Gm.setGridSize(o,e,n)})})])}))),zg.getContent(m).each(Qf.highlightFirst)},p=function(t){var n;n=t,e.get().map(function(t){return hp(d.dom,d.selection.getRng(),t.triggerChar).bind(function(t){return Cp(d,r,t,n)})}).getOrThunk(function(){return kp(d,r)}).fold(f,function(a){!function(t){if(!o()){var n=lp(d,t.range);e.set(st.some({triggerChar:t.triggerChar,element:n,matchLength:t.text.length})),s.set(!1)}}(a.context),a.lookupData.then(function(u){e.get().map(function(t){var n,e,o,r=a.context;if(t.triggerChar===r.triggerChar){var i=(n=r.triggerChar,o=$(e=u,function(t){return st.from(t.columns)}).getOr(1),U(e,function(i){var t=i.items;return Cb(t,i.matchText,function(o,r){var t=d.selection.getRng();hp(d.dom,t,n).fold(function(){return nt.console.error("Lost context. Cursor probably moved")},function(t){var n=t.range,e={hide:function(){f()},reload:function(t){l(),p(t)}};s.set(!0),i.onAction(e,n,o,r),s.set(!1)})},o,Ap.BUBBLE_TO_SANDBOX,c)}));0<i.length?g(t,r,u,i):(10<=r.text.length-t.matchLength?f:l)()}})})})},t={onKeypress:ap(function(t){27!==t.which&&p()},50),cancelIfNecessary:f,isMenuOpen:function(){return zg.isOpen(m)},isActive:o,isProcessingAction:s.get,getView:function(){return zg.getContent(m)}};vp(t,d)},Eb=at(!0),Bb=function(t,n,e){return wu(t,n,Eb,e,!1)},Db=function(t,n,e){return wu(t,n,Eb,e,!0)},Ab=xu,Mb=function(t,n,e){return ju(t,n,e).isSome()};function Fb(e,o){var r=null;return{cancel:function(){null!==r&&(nt.clearTimeout(r),r=null)},schedule:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r=nt.setTimeout(function(){e.apply(null,t),r=null},o)}}}var Ib=function(t){var n=t.raw();return n.touches===undefined||1!==n.touches.length?st.none():st.some(n.touches[0])},Rb=function(e){var u=se(st.none()),o=se(!1),r=Fb(function(t){e.triggerEvent(Co(),t),o.set(!0)},400),i=$t([{key:$e(),value:function(e){return Ib(e).each(function(t){r.cancel();var n={x:t.clientX,y:t.clientY,target:e.target()};r.schedule(e),o.set(!1),u.set(st.some(n))}),st.none()}},{key:Qe(),value:function(t){return r.cancel(),Ib(t).each(function(i){u.get().each(function(t){var n,e,o,r;n=i,e=t,o=Math.abs(n.clientX-e.x),r=Math.abs(n.clientY-e.y),(5<o||5<r)&&u.set(st.none())})}),st.none()}},{key:Ze(),value:function(n){r.cancel();return u.get().filter(function(t){return je(t.target,n.target())}).map(function(t){return o.get()?(n.prevent(),!1):e.triggerEvent(ko(),n)})}}]);return{fireIfReady:function(n,t){return Ft(i,t).bind(function(t){return t(n)})}}},Vb=function(){return ze().browser.isFirefox()},Pb=ln([Wn("triggerEvent"),te("stopBackspace",!0)]),Hb=function(n,t){var e,o,r,i,u=Tn("Getting GUI events settings",Pb,t),a=Rb(u),c=V(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return Bb(n,t,function(n){a.fireIfReady(n,t).each(function(t){t&&n.kill()}),u.triggerEvent(t,n)&&n.kill()})}),s=se(st.none()),l=Bb(n,"paste",function(n){a.fireIfReady(n,"paste").each(function(t){t&&n.kill()}),u.triggerEvent("paste",n)&&n.kill(),s.set(st.some(nt.setTimeout(function(){u.triggerEvent(yo(),n)},0)))}),f=Bb(n,"keydown",function(t){var n;u.triggerEvent("keydown",t)?t.kill():!0!==u.stopBackspace||(8!==(n=t).raw().which||M(["input","textarea"],cr(n.target()))||Mb(n.target(),'[contenteditable="true"]'))||t.prevent()}),d=(e=n,o=function(t){u.triggerEvent("focusin",t)&&t.kill()},Vb()?Db(e,"focus",o):Bb(e,"focusin",o)),m=se(st.none()),g=(r=n,i=function(t){u.triggerEvent("focusout",t)&&t.kill(),m.set(st.some(nt.setTimeout(function(){u.triggerEvent(bo(),t)},0)))},Vb()?Db(r,"blur",i):Bb(r,"focusout",i));return{unbind:function(){rt(c,function(t){t.unbind()}),f.unbind(),d.unbind(),g.unbind(),l.unbind(),s.get().each(nt.clearTimeout),m.get().each(nt.clearTimeout)}}},zb=function(t,n){var e=Ft(t,"target").map(function(t){return t()}).getOr(n);return se(e)},Nb=Vt([{stopped:[]},{resume:["element"]},{complete:[]}]),Lb=function(t,o,n,e,r,i){var u,a,c,s,l=t(o,e),f=(u=n,a=r,c=se(!1),s=se(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:at(u),setSource:a.set,getSource:a.get});return l.fold(function(){return i.logEventNoHandlers(o,e),Nb.complete()},function(n){var e=n.descHandler;return fi(e)(f),f.isStopped()?(i.logEventStopped(o,n.element,e.purpose()),Nb.stopped()):f.isCut()?(i.logEventCut(o,n.element,e.purpose()),Nb.complete()):br(n.element).fold(function(){return i.logNoParent(o,n.element,e.purpose()),Nb.complete()},function(t){return i.logEventResponse(o,n.element,e.purpose()),Nb.resume(t)})})},jb=function(n,e,o,t,r,i){return Lb(n,e,o,t,r,i).fold(function(){return!0},function(t){return jb(n,e,o,t,r,i)},function(){return!1})},Ub=function(t,n,e){var o,r,i=(o=n,r=se(!1),{stop:function(){r.set(!0)},cut:Z,isStopped:r.get,isCut:at(!1),event:at(o),setSource:u("Cannot set source of a broadcasted event"),getSource:u("Cannot get source of a broadcasted event")});return rt(t,function(t){var n=t.descHandler();fi(n)(i)}),i.isStopped()},Wb=function(t,n,e,o,r){var i=zb(e,o);return jb(t,n,e,o,i,r)},Gb=function(t,n){return{element:t,descHandler:n}},Xb=function(t,n){return{id:at(t),descHandler:at(n)}};function Yb(){var i={};return{registerId:function(o,r,t){_t(t,function(t,n){var e=i[n]!==undefined?i[n]:{};e[r]=li(t,o),i[n]=e})},unregisterId:function(e){_t(i,function(t,n){t.hasOwnProperty(e)&&delete t[e]})},filterByType:function(t){return Ft(i,t).map(function(t){return Dt(t,function(t,n){return Xb(n,t)})}).getOr([])},find:function(t,n,e){var r=Ft(i,n);return Ye(e,function(t){return e=r,Jr(o=t).fold(function(){return st.none()},function(n){return e.bind(function(t){return Ft(t,n)}).map(function(t){return Gb(o,t)})});var e,o},t)}}}function qb(){var o=Yb(),r={},i=function(o){var t=o.element();return Jr(t).fold(function(){return t="uid-",n=o.element(),e=Wr(Yr+t),Kr(n,e),e;var t,n,e},function(t){return t})},u=function(t){Jr(t.element()).each(function(t){delete r[t],o.unregisterId(t)})};return{find:function(t,n,e){return o.find(t,n,e)},filter:function(t){return o.filterByType(t)},register:function(t){var n=i(t);Rt(r,n)&&function(t,n){var e=r[n];if(e!==t)throw new Error('The tagId "'+n+'" is already used by: '+Nr(e.element())+"\nCannot use it for: "+Nr(t.element())+"\nThe conflicting element is"+(Ri(e.element())?" ":" not ")+"already in the DOM");u(t)}(t,n);var e=[t];o.registerId(e,n,t.events()),r[n]=t},unregister:u,getById:function(t){return Ft(r,t)}}}var Kb,Jb,$b,Qb,Zb=Rf({name:"Container",factory:function(t){var n=t.dom,e=n.attributes,o=y(n,["attributes"]);return{uid:t.uid,dom:et({tag:"div",attributes:et({role:"presentation"},e)},o),components:t.components,behaviours:Dl(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[te("components",[]),Bl("containerBehaviours",[]),te("events",{}),te("domModification",{}),te("eventOrder",{})]}),ty=function(e){var o=function(n){return br(e.element()).fold(function(){return!0},function(t){return je(n,t)})},r=qb(),s=function(t,n){return r.find(o,t,n)},t=Hb(e.element(),{triggerEvent:function(u,a){return Qu(u,a.target(),function(t){return n=s,e=u,r=t,i=(o=a).target(),Wb(n,e,o,i,r);var n,e,o,r,i})}}),i={debugInfo:at("real"),triggerEvent:function(n,e,o){Qu(n,e,function(t){return Wb(s,n,o,e,t)})},triggerFocus:function(a,c){Jr(a).fold(function(){ic(a)},function(t){Qu(vo(),a,function(t){var n,e,o,r,i,u;return n=s,e=vo(),o={originator:at(c),kill:Z,prevent:Z,target:at(a)},i=t,u=zb(o,r=a),Lb(n,e,o,r,u,i),!1})})},triggerEscape:function(t,n){i.triggerEvent("keydown",t.element(),n.event())},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:uu,addToGui:function(t){a(t)},removeFromGui:function(t){c(t)},addToWorld:function(t){n(t)},removeFromWorld:function(t){u(t)},broadcast:function(t){f(t)},broadcastOn:function(t,n){d(t,n)},broadcastEvent:function(t,n){m(t,n)},isConnected:at(!0)},n=function(t){t.connect(i),fr(t.element())||(r.register(t),rt(t.components(),n),i.triggerEvent(To(),t.element(),{target:at(t.element())}))},u=function(t){fr(t.element())||(rt(t.components(),u),r.unregister(t)),t.disconnect()},a=function(t){Gs(e,t)},c=function(t){qs(t)},l=function(e){var t=r.filter(xo());rt(t,function(t){var n=t.descHandler();fi(n)(e)})},f=function(t){l({universal:at(!0),data:at(t)})},d=function(t,n){l({universal:at(!1),channels:at(t),data:at(n)})},m=function(t,n){var e=r.filter(t);return Ub(e,n)},g=function(t){return r.getById(t).fold(function(){return ut.error(new Error('Could not find component with uid: "'+t+'" in system.'))},ut.value)},p=function(t){var n=Jr(t).getOr("not found");return g(n)};return n(e),{root:at(e),element:e.element,destroy:function(){t.unbind(),Er(e.element())},add:a,remove:c,getByUid:g,getByDom:p,addToWorld:n,removeFromWorld:u,broadcast:f,broadcastOn:d,broadcastEvent:m}},ny=at([te("prefix","form-field"),Bl("fieldBehaviours",[Lf,El])]),ey=at([af({schema:[Nn("dom")],name:"label"}),af({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Nn("text")],name:"aria-descriptor"}),rf({factory:{sketch:function(t){var n=Kt(t,["factory"]);return t.factory.sketch(n)}},schema:[Nn("factory")],name:"field"})]),oy=Vf({name:"FormField",configFields:ny(),partFields:ey(),factory:function(r,t,n,e){var o=Al(r.fieldBehaviours,[Lf.config({find:function(t){return yf(t,r,"field")}}),El.config({store:{mode:"manual",getValue:function(t){return Lf.getCurrent(t).bind(El.getValue)},setValue:function(t,n){Lf.getCurrent(t).each(function(t){El.setValue(t,n)})}}})]),i=qo([rr(function(t,n){var o=wf(t,r,["label","field","aria-descriptor"]);o.field().each(function(e){var n=Wr(r.prefix);o.label().each(function(t){Fr(t.element(),"for",n),Fr(e.element(),"id",n)}),o["aria-descriptor"]().each(function(t){var n=Wr(r.prefix);Fr(t.element(),"id",n),Fr(e.element(),"aria-describedby",n)})})})]),u={getField:function(t){return yf(t,r,"field")},getLabel:function(t){return yf(t,r,"label")}};return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:u}},apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),ry=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return si({attributes:$t([{key:n.tabAttr,value:"true"}])})}}),iy=[te("tabAttr","data-alloy-tabstop")],uy=$a({fields:iy,name:"tabstopping",active:ry}),ay=function(t,n,e,o){var r=cy(t,n,e,o);return oy.sketch(r)},cy=function(t,n,e,o){return{dom:sy(e),components:t.toArray().concat([n]),fieldBehaviours:Ka(o)}},sy=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},ly=function(t,n){return oy.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})},fy=Wr("form-component-change"),dy=Wr("form-close"),my=Wr("form-cancel"),gy=Wr("form-action"),py=Wr("form-submit"),hy=Wr("form-block"),vy=Wr("form-unblock"),by=Wr("form-tabchange"),yy=Wr("form-resize"),xy=function(a,c){var t,n,e=a.label.map(function(t){return ly(t,c)}),o=function(o){return function(n,e){ju(e.event().target(),"[data-collection-item-value]").each(function(t){o(n,e,t,Ir(t,"data-collection-item-value"))})}},r=function(t,n){var e=V(n,function(t){var n,e=hh.translate(t.text),o=1===a.columns?'<div class="tox-collection__item-label">'+e+"</div>":"",r='<div class="tox-collection__item-icon">'+t.icon+"</div>",i={_:" "," - ":" ","-":" "},u=e.replace(/\_| \- |\-/g,function(t){return i[t]});return'<div class="tox-collection__item'+(c.isReadOnly()?" tox-collection__item--state-disabled":"")+'" tabindex="-1" data-collection-item-value="'+('"'===(n=t.value)?"&quot;":n)+'" title="'+u+'" aria-label="'+u+'">'+r+o+"</div>"}),o=1<a.columns&&"auto"!==a.columns?R(e,a.columns):[e],r=V(o,function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"});Ar(t.element(),r.join(""))},i=o(function(t,n,e,o){n.stop(),c.isReadOnly()||Uo(t,gy,{name:a.name,value:o})}),u=[$o(io(),o(function(t,n,e){ic(e)})),$o(mo(),i),$o(ko(),i),$o(uo(),o(function(t,n,e){Lu(t.element(),"."+zp).each(function(t){Ci(t,zp)}),Si(e,zp)})),$o(ao(),o(function(t){Lu(t.element(),"."+zp).each(function(t){Ci(t,zp)})})),ar(o(function(t,n,e,o){Uo(t,gy,{name:a.name,value:o})}))],s=function(t,n){return V(os(t.element(),".tox-collection__item"),n)},l=oy.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==a.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:ct},behaviours:Ka([Dh.config({disabled:c.isReadOnly,onDisabled:function(t){s(t,function(t){Si(t,"tox-collection__item--state-disabled"),Fr(t,"aria-disabled",!0)})},onEnabled:function(t){s(t,function(t){Ci(t,"tox-collection__item--state-disabled"),Pr(t,"aria-disabled")})}}),nv(),Jm.config({}),El.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){r(o,t),"auto"===a.columns&&rh(o,5,"tox-collection__item").each(function(t){var n=t.numRows,e=t.numColumns;Gm.setGridSize(o,n,e)}),jo(o,yy)}}),uy.config({}),Gm.config((t=a.columns,n="normal",1===t?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===t?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===n?".tox-swatches__row":".tox-collection__group",cell:"color"===n?"."+Ip:"."+Fp}})),$m("collection-events",u)]),eventOrder:{"alloy.execute":["disabling","alloy.base.behaviour","collection-events"]}});return ay(e,l,["tox-form__group--collection"],[])},wy=at([qn("data"),te("inputAttributes",{}),te("inputStyles",{}),te("tag","input"),te("inputClasses",[]),aa("onSetValue"),te("styles",{}),te("eventOrder",{}),Bl("inputBehaviours",[El,eg]),te("selectOnFocus",!0)]),Sy=function(t){return Ka([eg.config({onFocus:t.selectOnFocus?function(t){var n=t.element(),e=Ki(n);n.dom().setSelectionRange(0,e.length)}:Z})])},ky=function(t){return{tag:t.tag,attributes:et({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}},Cy=Rf({name:"Input",configFields:wy(),factory:function(t,n){return{uid:t.uid,dom:ky(t),components:[],behaviours:et(et({},Sy(e=t)),Al(e.inputBehaviours,[El.config({store:et(et({mode:"manual"},e.data.map(function(t){return{initialValue:t}}).getOr({})),{getValue:function(t){return Ki(t.element())},setValue:function(t,n){Ki(t.element())!==n&&Ji(t.element(),n)}}),onSetValue:e.onSetValue})])),eventOrder:t.eventOrder};var e}}),Oy={},_y={exports:Oy};Kb=undefined,Jb=Oy,$b=_y,Qb=undefined,function(t){if("object"==typeof Jb&&void 0!==$b)$b.exports=t();else if("function"==typeof Kb&&Kb.amd)Kb([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=t()}}(function(){return function l(i,u,a){function c(n,t){if(!u[n]){if(!i[n]){var e="function"==typeof Qb&&Qb;if(!t&&e)return e(n,!0);if(s)return s(n,!0);var o=new Error("Cannot find module '"+n+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[n]={exports:{}};i[n][0].call(r.exports,function(t){return c(i[n][1][t]||t)},r,r.exports,l,i,u,a)}return u[n].exports}for(var s="function"==typeof Qb&&Qb,t=0;t<a.length;t++)c(a[t]);return c}({1:[function(t,n,e){var o,r,i=n.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(t){if(o===setTimeout)return setTimeout(t,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(t,0);try{return o(t,0)}catch(n){try{return o.call(null,t,0)}catch(n){return o.call(this,t,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(t){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var s,l=[],f=!1,d=-1;function m(){f&&s&&(f=!1,s.length?l=s.concat(l):d=-1,l.length&&g())}function g(){if(!f){var t=c(m);f=!0;for(var n=l.length;n;){for(s=l,l=[];++d<n;)s&&s[d].run();d=-1,n=l.length}s=null,f=!1,function e(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{return r(t)}catch(n){try{return r.call(null,t)}catch(n){return r.call(this,t)}}}(t)}}function p(t,n){this.fun=t,this.array=n}function h(){}i.nextTick=function(t){var n=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];l.push(new p(t,n)),1!==l.length||f||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(t,f,n){(function(n){function o(){}function i(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],l(t,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,i._immediateFn(function(){var t=1===o._state?r.onFulfilled:r.onRejected;if(null!==t){var n;try{n=t(o._value)}catch(e){return void a(r.promise,e)}u(r.promise,n)}else(1===o._state?u:a)(r.promise,o._value)})):o._deferreds.push(r)}function u(t,n){try{if(n===t)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if(n instanceof i)return t._state=3,t._value=n,void c(t);if("function"==typeof e)return void l(function o(t,n){return function(){t.apply(n,arguments)}}(e,n),t)}t._state=1,t._value=n,c(t)}catch(r){a(t,r)}}function a(t,n){t._state=2,t._value=n,c(t)}function c(t){2===t._state&&0===t._deferreds.length&&i._immediateFn(function(){t._handled||i._unhandledRejectionFn(t._value)});for(var n=0,e=t._deferreds.length;n<e;n++)r(t,t._deferreds[n]);t._deferreds=null}function s(t,n,e){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.promise=e}function l(t,n){var e=!1;try{t(function(t){e||(e=!0,u(n,t))},function(t){e||(e=!0,a(n,t))})}catch(o){if(e)return;e=!0,a(n,o)}}var t,e;t=this,e=setTimeout,i.prototype["catch"]=function(t){return this.then(null,t)},i.prototype.then=function(t,n){var e=new this.constructor(o);return r(this,new s(t,n,e)),e},i.all=function(t){var c=Array.prototype.slice.call(t);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}for(var t=0;t<c.length;t++)a(t,c[t])})},i.resolve=function(n){return n&&"object"==typeof n&&n.constructor===i?n:new i(function(t){t(n)})},i.reject=function(e){return new i(function(t,n){n(e)})},i.race=function(r){return new i(function(t,n){for(var e=0,o=r.length;e<o;e++)r[e].then(t,n)})},i._immediateFn="function"==typeof n?function(t){n(t)}:function(t){e(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},i._setImmediateFn=function(t){i._immediateFn=t},i._setUnhandledRejectionFn=function(t){i._unhandledRejectionFn=t},void 0!==f&&f.exports?f.exports=i:t.Promise||(t.Promise=i)}).call(this,t("timers").setImmediate)},{timers:3}],3:[function(c,t,s){(function(t,n){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(t,n){this._id=t,this._clearFn=n}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(t){t.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(t,n){clearTimeout(t._idleTimeoutId),t._idleTimeout=n},s.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},s._unrefActive=s.active=function(t){clearTimeout(t._idleTimeoutId);var n=t._idleTimeout;0<=n&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},n))},s.setImmediate="function"==typeof t?t:function(t){var n=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[n]=!0,o(function(){i[n]&&(e?t.apply(null,e):t.call(null),s.clearImmediate(n))}),n},s.clearImmediate="function"==typeof n?n:function(t){delete i[t]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(t,n,e){var o=t("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();n.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});var Ty,Ey,By=_y.exports.boltExport,Dy=function(t){var e=st.none(),n=[],o=function(t){r()?u(t):n.push(t)},r=function(){return e.isSome()},i=function(t){rt(t,u)},u=function(n){e.each(function(t){nt.setTimeout(function(){n(t)},0)})};return t(function(t){r()||(e=st.some(t),i(n),n=[])}),{get:o,map:function(e){return Dy(function(n){o(function(t){n(e(t))})})},isReady:r}},Ay={nu:Dy,pure:function(n){return Dy(function(t){t(n)})}},My=function(t){nt.setTimeout(function(){throw t},0)},Fy=function(e){var t=function(t){e().then(t,My)};return{map:function(t){return Fy(function(){return e().then(t)})},bind:function(n){return Fy(function(){return e().then(function(t){return n(t).toPromise()})})},anonBind:function(t){return Fy(function(){return e().then(function(){return t.toPromise()})})},toLazy:function(){return Ay.nu(t)},toCached:function(){var t=null;return Fy(function(){return null===t&&(t=e()),t})},toPromise:e,get:t}},Iy=function(t){return Fy(function(){return new By(t)})},Ry=function(t){return Fy(function(){return By.resolve(t)})},Vy=["input","textarea"],Py=function(t){var n=cr(t);return M(Vy,n)},Hy=function(t,n){var e=n.getRoot(t).getOr(t.element());Ci(e,n.invalidClass),n.notify.each(function(n){Py(t.element())&&Fr(t.element(),"aria-invalid",!1),n.getContainer(t).each(function(t){Ar(t,n.validHtml)}),n.onValid(t)})},zy=function(n,t,e,o){var r=t.getRoot(n).getOr(n.element());Si(r,t.invalidClass),t.notify.each(function(t){Py(n.element())&&Fr(n.element(),"aria-invalid",!0),t.getContainer(n).each(function(t){Ar(t,o)}),t.onInvalid(n,o)})},Ny=function(n,t,e){return t.validator.fold(function(){return Ry(ut.value(!0))},function(t){return t.validate(n)})},Ly=function(n,e,t){return e.notify.each(function(t){t.onValidate(n)}),Ny(n,e).map(function(t){return n.getSystem().isConnected()?t.fold(function(t){return zy(n,e,0,t),ut.error(t)},function(t){return Hy(n,e),ut.value(t)}):ut.error("No longer in system")})},jy=/* */Object.freeze({__proto__:null,markValid:Hy,markInvalid:zy,query:Ny,run:Ly,isInvalid:function(t,n){var e=n.getRoot(t).getOr(t.element());return Oi(e,n.invalidClass)}}),Uy=/* */Object.freeze({__proto__:null,events:function(n,t){return n.validator.map(function(t){return qo([$o(t.onEvent,function(t){Ly(t,n).get(ct)})].concat(t.validateOnLoad?[rr(function(t){Ly(t,n).get(Z)})]:[]))}).getOr({})}}),Wy=[Nn("invalidClass"),te("getRoot",st.none),Zn("notify",[te("aria","alert"),te("getContainer",st.none),te("validHtml",""),aa("onValid"),aa("onInvalid"),aa("onValidate")]),Zn("validator",[Nn("validate"),te("onEvent","input"),te("validateOnLoad",!0)])],Gy=$a({fields:Wy,name:"invalidating",active:Uy,apis:jy,extra:{validation:function(e){return function(t){var n=El.getValue(t);return Ry(e(n))}}}}),Xy=/* */Object.freeze({__proto__:null,getCoupled:function(t,n,e,o){return e.getOrCreate(t,n,o)}}),Yy=[Ln("others",Cn(ut.value,An()))],qy=$a({fields:Yy,name:"coupling",apis:Xy,state:/* */Object.freeze({__proto__:null,init:function(){var i={},t=at({});return ai({readState:t,getOrCreate:function(e,o,r){var t=Ct(o.others);if(t)return Ft(i,r).getOrThunk(function(){var t=Ft(o.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(t);return i[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(t,null,2))}})}})}),Ky=at("sink"),Jy=at(af({name:Ky(),overrides:at({dom:{tag:"div"},behaviours:Ka([Ns.config({useFixed:i})]),events:qo([er(co()),er(no()),er(mo())])})}));(Ey=Ty=Ty||{})[Ey.HighlightFirst=0]="HighlightFirst",Ey[Ey.HighlightNone=1]="HighlightNone";var $y=function(t,n){var e=t.getHotspot(n).getOr(n),o=t.getAnchorOverrides();return t.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(t){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:t}})},Qy=function(t,n,e,o,r,i,u){var a,c,s,l,f,d,m,g,p,h,v=$y(t,e);return(c=v,l=o,f=r,d=u,m=n,g=s=e,p=(0,(a=t).fetch)(g).map(m),h=ex(s,a),p.map(function(t){return t.bind(function(t){return st.from(Hg.sketch(et(et({},f.menu()),{uid:$r(""),data:t,highlightImmediately:d===Ty.HighlightFirst,onOpenMenu:function(t,n){var e=h().getOrDie();Ns.position(e,c,n),cl.decloak(l)},onOpenSubmenu:function(t,n,e){var o=h().getOrDie();Ns.position(o,{anchor:"submenu",item:n},e),cl.decloak(l)},onRepositionMenu:function(t,n,e){var o=h().getOrDie();Ns.position(o,c,n),rt(e,function(t){Ns.position(o,{anchor:"submenu",item:t.triggeringItem},t.triggeredMenu)})},onEscape:function(){return eg.focus(s),cl.close(l),st.some(!0)}})))})})).map(function(t){return t.fold(function(){cl.isOpen(o)&&cl.close(o)},function(t){cl.cloak(o),cl.open(o,t),i(o)}),o})},Zy=function(t,n,e,o,r,i,u){return cl.close(o),Ry(o)},tx=function(t,n,e,o,r,i){var u=qy.getCoupled(e,"sandbox");return(cl.isOpen(u)?Zy:Qy)(t,n,e,u,o,r,i)},nx=function(t,n,e){var o,r,i=Lf.getCurrent(n).getOr(n),u=bu(t.element());e?Ni(i.element(),"min-width",u+"px"):(o=i.element(),r=u,vu.set(o,r))},ex=function(n,t){return n.getSystem().getByUid(t.uid+"-"+Ky()).map(function(t){return function(){return ut.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return ut.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(n)}})})},ox=function(t){cl.getState(t).each(function(t){Hg.repositionMenus(t)})},rx=function(o,r,i){var u=Uu(),t=ex(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id,role:"listbox"}},behaviours:Fl(o.sandboxBehaviours,[El.config({store:{mode:"memory",initialValue:r}}),cl.config({onOpen:function(t,n){var e=$y(o,r);u.link(r.element()),o.matchWidth&&nx(e.hotspot,n,o.useMinWidth),o.onOpen(e,t,n),i!==undefined&&i.onOpen!==undefined&&i.onOpen(t,n)},onClose:function(t,n){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(t,n)},isPartOf:function(t,n,e){return Gu(n,e)||Gu(r,e)},getAttachPoint:function(){return t().getOrDie()}}),Lf.config({find:function(t){return cl.getState(t).bind(function(t){return Lf.getCurrent(t)})}}),oc.config({channels:et(et({},ml({isExtraPart:c})),pl({doReposition:ox}))})])}},ix=function(t){var n=qy.getCoupled(t,"sandbox");ox(n)},ux=function(){return[te("sandboxClasses",[]),Ml("sandboxBehaviours",[Lf,oc,cl,El])]},ax=at([Nn("dom"),Nn("fetch"),aa("onOpen"),ca("onExecute"),te("getHotspot",st.some),te("getAnchorOverrides",at({})),Pc(),Bl("dropdownBehaviours",[gg,qy,Gm,eg]),Nn("toggleClass"),te("eventOrder",{}),qn("lazySink"),te("matchWidth",!1),te("useMinWidth",!1),qn("role")].concat(ux())),cx=at([uf({schema:[ra()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),Jy()]),sx=Vf({name:"Dropdown",configFields:ax(),partFields:cx(),factory:function(n,t,e,o){var r,i,u=function(t){cl.getState(t).each(function(t){Hg.highlightPrimary(t)})},a={expand:function(t){gg.isOn(t)||tx(n,function(t){return t},t,o,Z,Ty.HighlightNone).get(Z)},open:function(t){gg.isOn(t)||tx(n,function(t){return t},t,o,Z,Ty.HighlightFirst).get(Z)},isOpen:gg.isOn,close:function(t){gg.isOn(t)&&tx(n,function(t){return t},t,o,Z,Ty.HighlightFirst).get(Z)},repositionMenus:function(t){gg.isOn(t)&&ix(t)}},c=function(t,n){return Wo(t),st.some(!0)};return{uid:n.uid,dom:n.dom,components:t,behaviours:Al(n.dropdownBehaviours,[gg.config({toggleClass:n.toggleClass,aria:{mode:"expanded"}}),qy.config({others:{sandbox:function(t){return rx(n,t,{onOpen:function(){gg.on(t)},onClose:function(){gg.off(t)}})}}}),Gm.config({mode:"special",onSpace:c,onEnter:c,onDown:function(t,n){if(sx.isOpen(t)){var e=qy.getCoupled(t,"sandbox");u(e)}else sx.open(t);return st.some(!0)},onEscape:function(t,n){return sx.isOpen(t)?(sx.close(t),st.some(!0)):st.none()}}),eg.config({})]),events:hg(st.some(function(t){tx(n,function(t){return t},t,o,u,Ty.HighlightFirst).get(Z)})),eventOrder:et(et({},n.eventOrder),((r={})[wo()]=["disabling","toggling","alloy.base.behaviour"],r)),apis:a,domModification:{attributes:et(et({"aria-haspopup":"true"},n.role.fold(function(){return{}},function(t){return{role:t}})),"button"===n.dom.tag?{type:(i="type",Ft(n.dom,"attributes").bind(function(t){return Ft(t,i)}).getOr("button"))}:{})}}},apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)},repositionMenus:function(t,n){return t.repositionMenus(n)}}}),lx=$a({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return qo([Ko(po(),at(!0))])},exhibit:function(){return si({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),fx=Wr("color-input-change"),dx=Wr("color-swatch-change"),mx=Wr("color-picker-cancel"),gx=function(e,n,o){var r,i,t=oy.parts().field({factory:Cy,inputClasses:["tox-textfield"],onSetValue:function(t){return Gy.run(t).get(function(){})},inputBehaviours:Ka([Dh.config({disabled:n.providers.isReadOnly}),nv(),uy.config({}),Gy.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return br(t.element())},notify:{onValid:function(t){var n=El.getValue(t);Uo(t,fx,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=El.getValue(t);if(0===n.length)return Ry(ut.value(!0));var e=fe.fromTag("span");Ni(e,"background-color",n);var o=Gi(e,"background-color").fold(function(){return ut.error("blah")},function(t){return ut.value(n)});return Ry(o)}}})]),selectOnFocus:!1}),u=e.label.map(function(t){return ly(t,n.providers)}),a=function(t,n){Uo(t,dx,{value:n})},c=Zg((r={dom:{tag:"span",attributes:{"aria-label":n.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[Ma,Aa,Va]},onLtr:function(){return[Aa,Ma,Va]}},components:[],fetch:ub(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(t,e){c.getOpt(t).each(function(n){"custom"===e?o.colorPicker(function(t){t.fold(function(){return jo(n,mx)},function(t){a(n,t),nb(t)})},"#ffffff"):a(n,"remove"===e?"":e)})}},i=n,sx.sketch({dom:r.dom,components:r.components,toggleClass:"mce-active",dropdownBehaviours:Ka([ov(i.providers.isReadOnly),nv(),lx.config({}),uy.config({})]),layouts:r.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:i.getSink,fetch:function(n){return Iy(function(t){return r.fetch(t)}).map(function(t){return st.from(_b(zt(fb(Wr("menu-value"),t,function(t){r.onItemAction(n,t)},r.columns,r.presets,Ap.CLOSE_ON_EXECUTE,function(){return!1},i.providers),{movement:mb(r.columns,r.presets)})))})},parts:{menu:Wp(0,0,r.presets)}})));return oy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:u.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[t,c.asSpec()]}]),fieldBehaviours:Ka([$m("form-field-events",[$o(fx,function(t,n){c.getOpt(t).each(function(t){Ni(t.element(),"background-color",n.event().color())}),Uo(t,fy,{name:e.name})}),$o(dx,function(n,e){oy.getField(n).each(function(t){El.setValue(t,e.event().value()),Lf.getCurrent(n).each(eg.focus)})}),$o(mx,function(n,t){oy.getField(n).each(function(t){Lf.getCurrent(n).each(eg.focus)})})])])})},px=function(t,n,e){return{hue:t,saturation:n,value:e}},hx=Wr("rgb-hex-update"),vx=Wr("slider-update"),bx=Wr("palette-update"),yx=af({schema:[Nn("dom")],name:"label"}),xx=function(n){return af({name:n+"-edge",overrides:function(t){return t.model.manager.edgeActions[n].fold(function(){return{}},function(o){return{events:qo([Qo($e(),function(t,n,e){return o(t,e)},[t]),Qo(no(),function(t,n,e){return o(t,e)},[t]),Qo(eo(),function(t,n,e){e.mouseIsDown.get()&&o(t,e)},[t])])}})}})},wx=xx("top-left"),Sx=xx("top"),kx=xx("top-right"),Cx=xx("right"),Ox=xx("bottom-right"),_x=xx("bottom"),Tx=xx("bottom-left"),Ex=[yx,xx("left"),Cx,Sx,_x,wx,kx,Tx,Ox,rf({name:"thumb",defaults:at({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:qo([tr($e(),t,"spectrum"),tr(Qe(),t,"spectrum"),tr(Ze(),t,"spectrum"),tr(no(),t,"spectrum"),tr(eo(),t,"spectrum"),tr(ro(),t,"spectrum")])}}}),rf({schema:[ce("mouseIsDown",function(){return se(!1)})],name:"spectrum",overrides:function(e){var o=e.model.manager,r=function(n,t){return o.getValueFromEvent(t).map(function(t){return o.setValueFrom(n,e,t)})};return{behaviours:Ka([Gm.config({mode:"special",onLeft:function(t){return o.onLeft(t,e)},onRight:function(t){return o.onRight(t,e)},onUp:function(t){return o.onUp(t,e)},onDown:function(t){return o.onDown(t,e)}}),eg.config({})]),events:qo([$o($e(),r),$o(Qe(),r),$o(no(),r),$o(eo(),function(t,n){e.mouseIsDown.get()&&r(t,n)})])}}})],Bx=at("slider.change.value"),Dx=function(t){var n=t.event().raw();if(-1!==n.type.indexOf("touch")){return n.touches!==undefined&&1===n.touches.length?st.some(n.touches[0]).map(function(t){return mu(t.clientX,t.clientY)}):st.none()}return n.clientX!==undefined?st.some(n).map(function(t){return mu(t.clientX,t.clientY)}):st.none()},Ax=function(t){return t.model.minX},Mx=function(t){return t.model.minY},Fx=function(t){return t.model.minX-1},Ix=function(t){return t.model.minY-1},Rx=function(t){return t.model.maxX},Vx=function(t){return t.model.maxY},Px=function(t){return t.model.maxX+1},Hx=function(t){return t.model.maxY+1},zx=function(t,n,e){return n(t)-e(t)},Nx=function(t){return zx(t,Rx,Ax)},Lx=function(t){return zx(t,Vx,Mx)},jx=function(t){return Nx(t)/2},Ux=function(t){return Lx(t)/2},Wx=function(t){return t.stepSize},Gx=function(t){return t.snapToGrid},Xx=function(t){return t.snapStart},Yx=function(t){return t.rounded},qx=function(t,n){return t[n+"-edge"]!==undefined},Kx=function(t){return qx(t,"left")},Jx=function(t){return qx(t,"right")},$x=function(t){return qx(t,"top")},Qx=function(t){return qx(t,"bottom")},Zx=function(t){return t.model.value.get()},tw=function(t){return{x:at(t)}},nw=function(t){return{y:at(t)}},ew=function(t,n){return{x:at(t),y:at(n)}},ow=function(t,n){Uo(t,Bx(),{value:n})},rw=function(t,n,e,o){return t<n?t:e<t?e:t===n?n-1:Math.max(n,t-o)},iw=function(t,n,e,o){return e<t?t:t<n?n:t===e?e+1:Math.min(e,t+o)},uw=function(t,n,e){return Math.max(n,Math.min(e,t))},aw=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,l=t.hasMaxEdge,f=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=l?e+1:e;if(r<f)return g;if(d<r)return p;var h,v,b,y,x,w,S,k=(x=r,w=f,S=d,Math.min(S,Math.max(x,w))-w),C=uw(k/m*o+n,g,p);return u&&n<=C&&C<=e?(h=C,v=n,b=e,y=i,a.fold(function(){var t=h-v,n=Math.round(t/y)*y;return uw(v+n,v-1,b+1)},function(t){var n=(h-t)%y,e=Math.round(n/y),o=Math.floor((h-t)/y),r=Math.floor((b-t)/y),i=t+Math.min(r,o+e)*y;return Math.max(t,i)})):c?Math.round(C):C},cw=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,l=t.centerMaxEdge;return r<n?i?0:s:e<r?u?a:l:(r-n)/o*c},sw="left",lw=function(t){return t.element().dom().getBoundingClientRect()},fw=function(t,n){return t[n]},dw=function(t){var n=lw(t);return fw(n,sw)},mw=function(t){var n=lw(t);return fw(n,"right")},gw=function(t){var n=lw(t);return fw(n,"top")},pw=function(t){var n=lw(t);return fw(n,"bottom")},hw=function(t){var n=lw(t);return fw(n,"width")},vw=function(t){var n=lw(t);return fw(n,"height")},bw=function(t,n,e){return(t+n)/2-e},yw=function(t,n){var e=lw(t),o=lw(n),r=fw(e,sw),i=fw(e,"right"),u=fw(o,sw);return bw(r,i,u)},xw=function(t,n){var e=lw(t),o=lw(n),r=fw(e,"top"),i=fw(e,"bottom"),u=fw(o,"top");return bw(r,i,u)},ww=function(t,n){Uo(t,Bx(),{value:n})},Sw=function(t){return{x:at(t)}},kw=function(t,n,e){var o={min:Ax(n),max:Rx(n),range:Nx(n),value:e,step:Wx(n),snap:Gx(n),snapStart:Xx(n),rounded:Yx(n),hasMinEdge:Kx(n),hasMaxEdge:Jx(n),minBound:dw(t),maxBound:mw(t),screenRange:hw(t)};return aw(o)},Cw=function(i){return function(t,n){return e=t,r=(0<i?iw:rw)(Zx(o=n).x(),Ax(o),Rx(o),Wx(o)),ww(e,Sw(r)),st.some(r).map(function(){return!0});var e,o,r}},Ow=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=hw(u=n),d=s.bind(function(t){return st.some(yw(t,u))}).getOr(0),m=l.bind(function(t){return st.some(yw(t,u))}).getOr(f),g={min:Ax(a),max:Rx(a),range:Nx(a),value:c,hasMinEdge:Kx(a),hasMaxEdge:Jx(a),minBound:dw(u),minOffset:0,maxBound:mw(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},cw(g));return dw(n)-dw(t)+p},_w=Cw(-1),Tw=Cw(1),Ew=st.none,Bw=st.none,Dw={"top-left":st.none(),top:st.none(),"top-right":st.none(),right:st.some(function(t,n){ow(t,tw(Px(n)))}),"bottom-right":st.none(),bottom:st.none(),"bottom-left":st.none(),left:st.some(function(t,n){ow(t,tw(Fx(n)))})},Aw=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=kw(t,n,e),r=Sw(o);return ww(t,r),o},setToMin:function(t,n){var e=Ax(n);ww(t,Sw(e))},setToMax:function(t,n){var e=Rx(n);ww(t,Sw(e))},findValueOfOffset:kw,getValueFromEvent:function(t){return Dx(t).map(function(t){return t.left()})},findPositionOfValue:Ow,setPositionFromValue:function(t,n,e,o){var r=Zx(e),i=Ow(t,o.getSpectrum(t),r.x(),o.getLeftEdge(t),o.getRightEdge(t),e),u=bu(n.element())/2;Ni(n.element(),"left",i-u+"px")},onLeft:_w,onRight:Tw,onUp:Ew,onDown:Bw,edgeActions:Dw}),Mw=function(t,n){Uo(t,Bx(),{value:n})},Fw=function(t){return{y:at(t)}},Iw=function(t,n,e){var o={min:Mx(n),max:Vx(n),range:Lx(n),value:e,step:Wx(n),snap:Gx(n),snapStart:Xx(n),rounded:Yx(n),hasMinEdge:$x(n),hasMaxEdge:Qx(n),minBound:gw(t),maxBound:pw(t),screenRange:vw(t)};return aw(o)},Rw=function(i){return function(t,n){return e=t,r=(0<i?iw:rw)(Zx(o=n).y(),Mx(o),Vx(o),Wx(o)),Mw(e,Fw(r)),st.some(r).map(function(){return!0});var e,o,r}},Vw=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=vw(u=n),d=s.bind(function(t){return st.some(xw(t,u))}).getOr(0),m=l.bind(function(t){return st.some(xw(t,u))}).getOr(f),g={min:Mx(a),max:Vx(a),range:Lx(a),value:c,hasMinEdge:$x(a),hasMaxEdge:Qx(a),minBound:gw(u),minOffset:0,maxBound:pw(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},cw(g));return gw(n)-gw(t)+p},Pw=st.none,Hw=st.none,zw=Rw(-1),Nw=Rw(1),Lw={"top-left":st.none(),top:st.some(function(t,n){ow(t,nw(Ix(n)))}),"top-right":st.none(),right:st.none(),"bottom-right":st.none(),bottom:st.some(function(t,n){ow(t,nw(Hx(n)))}),"bottom-left":st.none(),left:st.none()},jw=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=Iw(t,n,e),r=Fw(o);return Mw(t,r),o},setToMin:function(t,n){var e=Mx(n);Mw(t,Fw(e))},setToMax:function(t,n){var e=Vx(n);Mw(t,Fw(e))},findValueOfOffset:Iw,getValueFromEvent:function(t){return Dx(t).map(function(t){return t.top()})},findPositionOfValue:Vw,setPositionFromValue:function(t,n,e,o){var r=Zx(e),i=Vw(t,o.getSpectrum(t),r.y(),o.getTopEdge(t),o.getBottomEdge(t),e),u=lu(n.element())/2;Ni(n.element(),"top",i-u+"px")},onLeft:Pw,onRight:Hw,onUp:zw,onDown:Nw,edgeActions:Lw}),Uw=function(t,n){Uo(t,Bx(),{value:n})},Ww=function(t,n){return{x:at(t),y:at(n)}},Gw=function(c,s){return function(t,n){return o=t,r=n,i=0<c?iw:rw,u=(e=s)?Zx(r).x():i(Zx(r).x(),Ax(r),Rx(r),Wx(r)),a=e?i(Zx(r).y(),Mx(r),Vx(r),Wx(r)):Zx(r).y(),Uw(o,Ww(u,a)),st.some(u).map(function(){return!0});var e,o,r,i,u,a}},Xw=Gw(-1,!1),Yw=Gw(1,!1),qw=Gw(-1,!0),Kw=Gw(1,!0),Jw={"top-left":st.some(function(t,n){ow(t,ew(Fx(n),Ix(n)))}),top:st.some(function(t,n){ow(t,ew(jx(n),Ix(n)))}),"top-right":st.some(function(t,n){ow(t,ew(Px(n),Ix(n)))}),right:st.some(function(t,n){ow(t,ew(Px(n),Ux(n)))}),"bottom-right":st.some(function(t,n){ow(t,ew(Px(n),Hx(n)))}),bottom:st.some(function(t,n){ow(t,ew(jx(n),Hx(n)))}),"bottom-left":st.some(function(t,n){ow(t,ew(Fx(n),Hx(n)))}),left:st.some(function(t,n){ow(t,ew(Fx(n),Ux(n)))})},$w=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=kw(t,n,e.left()),r=Iw(t,n,e.top()),i=Ww(o,r);return Uw(t,i),i},setToMin:function(t,n){var e=Ax(n),o=Mx(n);Uw(t,Ww(e,o))},setToMax:function(t,n){var e=Rx(n),o=Vx(n);Uw(t,Ww(e,o))},getValueFromEvent:function(t){return Dx(t)},setPositionFromValue:function(t,n,e,o){var r=Zx(e),i=Ow(t,o.getSpectrum(t),r.x(),o.getLeftEdge(t),o.getRightEdge(t),e),u=Vw(t,o.getSpectrum(t),r.y(),o.getTopEdge(t),o.getBottomEdge(t),e),a=bu(n.element())/2,c=lu(n.element())/2;Ni(n.element(),"left",i-a+"px"),Ni(n.element(),"top",u-c+"px")},onLeft:Xw,onRight:Yw,onUp:qw,onDown:Kw,edgeActions:Jw}),Qw=Vf({name:"Slider",configFields:[te("stepSize",1),te("onChange",Z),te("onChoose",Z),te("onInit",Z),te("onDragStart",Z),te("onDragEnd",Z),te("snapToGrid",!1),te("rounded",!0),qn("snapStart"),Ln("model",Dn("mode",{x:[te("minX",0),te("maxX",100),ce("value",function(t){return se(t.mode.minX)}),Nn("getInitialValue"),fa("manager",Aw)],y:[te("minY",0),te("maxY",100),ce("value",function(t){return se(t.mode.minY)}),Nn("getInitialValue"),fa("manager",jw)],xy:[te("minX",0),te("maxX",100),te("minY",0),te("maxY",100),ce("value",function(t){return se({x:at(t.mode.minX),y:at(t.mode.minY)})}),Nn("getInitialValue"),fa("manager",$w)]})),Bl("sliderBehaviours",[Gm,El]),ce("mouseIsDown",function(){return se(!1)})],partFields:Ex,factory:function(i,t,n,e){var o,u=function(t){return xf(t,i,"thumb")},a=function(t){return xf(t,i,"spectrum")},r=function(t){return yf(t,i,"left-edge")},c=function(t){return yf(t,i,"right-edge")},s=function(t){return yf(t,i,"top-edge")},l=function(t){return yf(t,i,"bottom-edge")},f=i.model,d=f.manager,m=function(t,n){d.setPositionFromValue(t,n,i,{getLeftEdge:r,getRightEdge:c,getTopEdge:s,getBottomEdge:l,getSpectrum:a})},g=function(t,n){f.value.set(n);var e=u(t);return m(t,e),i.onChange(t,e,n),st.some(!0)},p=function(e){var t=i.mouseIsDown.get();i.mouseIsDown.set(!1),t&&yf(e,i,"thumb").each(function(t){var n=f.value.get();i.onChoose(e,t,n)})},h=function(t,n){n.stop(),i.mouseIsDown.set(!0),i.onDragStart(t,u(t))},v=function(t,n){n.stop(),i.onDragEnd(t,u(t)),p(t)};return{uid:i.uid,dom:i.dom,components:t,behaviours:Al(i.sliderBehaviours,[Gm.config({mode:"special",focusIn:function(t){return yf(t,i,"spectrum").map(Gm.focusIn).map(at(!0))}}),El.config({store:{mode:"manual",getValue:function(t){return f.value.get()}}}),oc.config({channels:((o={})[fl()]={onReceive:p},o)})]),events:qo([$o(Bx(),function(t,n){g(t,n.event().value())}),rr(function(t,n){var e=f.getInitialValue();f.value.set(e);var o=u(t);m(t,o);var r=a(t);i.onInit(t,o,r,f.value.get())}),$o($e(),h),$o(Ze(),v),$o(no(),h),$o(ro(),v)]),apis:{resetToMin:function(t){d.setToMin(t,i)},resetToMax:function(t){d.setToMax(t,i)},changeValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),Zw=[Bl("formBehaviours",[El])],tS=function(t){return"<alloy.field."+t+">"},nS=function(o,t){return{uid:o.uid,dom:o.dom,components:t,behaviours:Al(o.formBehaviours,[El.config({store:{mode:"manual",getValue:function(t){var n=Sf(t,o);return Tt(n,function(t,r){return t().bind(function(t){var n,e,o=Lf.getCurrent(t);return n=o,e=new Error("Cannot find a current component to extract the value from for form part '"+r+"': "+Nr(t.element())),n.fold(function(){return ut.error(e)},ut.value)}).map(El.getValue)})},setValue:function(e,t){_t(t,function(n,t){yf(e,o,t).each(function(t){Lf.getCurrent(t).each(function(t){El.setValue(t,n)})})})}}})]),apis:{getField:function(t,n){return yf(t,o,n).bind(Lf.getCurrent)}}}},eS={getField:ii(function(t,n,e){return t.getField(n,e)}),sketch:function(t){var e,n=(e=[],{field:function(t,n){return e.push(t),gf("form",tS(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return rf({name:t,pname:tS(t)})});return Bf("form",Zw,i,nS,o)}},oS=Wr("valid-input"),rS=Wr("invalid-input"),iS=Wr("validating-input"),uS="colorcustom.rgb.",aS=function(d,m,g,p){var h=function(t,n,e,o,r){var i,u,a=d(uS+"range"),c=[oy.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),oy.parts().field({data:r,factory:Cy,inputAttributes:et({type:"text"},"hex"===n?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:Ka([(i=n,u=t,Gy.config({invalidClass:m("invalid"),notify:{onValidate:function(t){Uo(t,iS,{type:i})},onValid:function(t){Uo(t,oS,{type:i,value:El.getValue(t)})},onInvalid:function(t){Uo(t,rS,{type:i,value:El.getValue(t)})}},validator:{validate:function(t){var n=El.getValue(t),e=u(n)?ut.value(!0):ut.error(d("aria.input.invalid"));return Ry(e)},validateOnLoad:!1}})),uy.config({})]),onSetValue:function(t){Gy.isInvalid(t)&&Gy.run(t).get(Z)}})],s="hex"!==n?[oy.parts()["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}},v=function(t,n){var e=n.red,o=n.green,r=n.blue;El.setValue(t,{red:e,green:o,blue:r})},b=Zg({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),y=function(t,n){b.getOpt(t).each(function(t){Ni(t.element(),"background-color","#"+n.value)})};return Rf({factory:function(){var e={red:se(st.some(255)),green:se(st.some(255)),blue:se(st.some(255)),hex:se(st.some("ffffff"))},o=function(t){return e[t].get()},i=function(t,n){e[t].set(n)},r=function(t){var n=t.red,e=t.green,o=t.blue;i("red",st.some(n)),i("green",st.some(e)),i("blue",st.some(o))},n=function(t,n){var e=n.event();"hex"!==e.type()?i(e.type(),st.none()):p(t)},u=function(r,t,n){var e=parseInt(n,10);i(t,st.some(e)),o("red").bind(function(e){return o("green").bind(function(n){return o("blue").map(function(t){return Nv(e,n,t,1)})})}).each(function(t){var n,e,o=(n=r,e=Iv(t),eS.getField(n,"hex").each(function(t){eg.isFocused(t)||El.setValue(n,{hex:e.value})}),e);y(r,o)})},a=function(t,n){var e=n.event();"hex"===e.type()?function(t,n){g(t);var e=Ev(n);i("hex",st.some(n));var o=Uv(e);v(t,o),r(o),Uo(t,hx,{hex:e}),y(t,e)}(t,e.value()):u(t,e.type(),e.value())},t=function(t){return{label:d(uS+t+".label"),description:d(uS+t+".description")}},c=t("red"),s=t("green"),l=t("blue"),f=t("hex");return zt(eS.sketch(function(t){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[t.field("red",oy.sketch(h(Lv,"red",c.label,c.description,255))),t.field("green",oy.sketch(h(Lv,"green",s.label,s.description,255))),t.field("blue",oy.sketch(h(Lv,"blue",l.label,l.description,255))),t.field("hex",oy.sketch(h(Av,"hex",f.label,f.description,"ffffff"))),b.asSpec()],formBehaviours:Ka([Gy.config({invalidClass:m("form-invalid")}),$m("rgb-form-events",[$o(oS,a),$o(rS,n),$o(iS,n)])])}}),{apis:{updateHex:function(t,n){var e,o;El.setValue(t,{hex:n.value}),e=t,o=Uv(n),v(e,o),r(o),y(t,n)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}})},cS=function(t,o){var r=Qw.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),i=Qw.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}}),u=function(t,n){var e=t.width,o=t.height,r=t.getContext("2d");if(null!==r){r.fillStyle=n,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}};return Rf({factory:function(t){var n=at({x:at(0),y:at(0)}),e=Ka([Lf.config({find:st.some}),eg.config({})]);return Qw.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:n},rounded:!1,components:[r,i],onChange:function(t,n,e){Uo(t,bx,{value:e})},onInit:function(t,n,e,o){u(e.element().dom(),Gv(Xv))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(t,n,e){var o,r;o=e,r=n.components()[0].element().dom(),u(r,Gv(o))}},extraApis:{}})},sS=function(f,d){return Rf({name:"ColourPicker",configFields:[Nn("dom"),te("onValidHex",Z),te("onInvalidHex",Z)],factory:function(t){var a,v,n,e,o,r=aS(f,d,t.onValidHex,t.onInvalidHex),i=cS(0,d),b={paletteRgba:se(Xv)},u=Zg(i.sketch({})),c=Zg(r.sketch({})),s=function(t,e){u.getOpt(t).each(function(t){var n=Uv(e);b.paletteRgba.set(n),i.setRgba(t,n)})},l=function(t,n){c.getOpt(t).each(function(t){r.updateHex(t,n)})},y=function(n,e,t){rt(t,function(t){t(n,e)})};return{uid:t.uid,dom:t.dom,components:[u.asSpec(),(n=d,e=Qw.parts().spectrum({dom:{tag:"div",classes:[n("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=Qw.parts().thumb({dom:{tag:"div",classes:[n("hue-slider-thumb")],attributes:{role:"presentation"}}}),Qw.sketch({dom:{tag:"div",classes:[n("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:at({y:at(0)})},components:[e,o],sliderBehaviours:Ka([eg.config({})]),onChange:function(t,n,e){Uo(t,vx,{value:e})}})),c.asSpec()],behaviours:Ka([$m("colour-picker-events",[$o(bx,(v=[l],function(t,n){var e,o,r,i,u,a,c,s,l,f=n.event().value(),d=b.paletteRgba.get(),m=(i=r=0,u=(e=d).red/255,a=e.green/255,c=e.blue/255,s=Math.min(u,Math.min(a,c)),l=Math.max(u,Math.max(a,c)),s===l?px(0,0,100*(i=s)):(r=60*((r=u===s?3:c===s?1:5)-(u===s?a-c:c===s?u-a:c-u)/(l-s)),o=(l-s)/l,i=l,px(Math.round(r),Math.round(100*o),Math.round(100*i)))),g=px(m.hue,f.x(),100-f.y()),p=jv(g),h=Iv(p);y(t,h,v)})),$o(vx,(a=[s,l],function(t,n){var e,o,r,i=n.event().value(),u=(e=i.y(),o=px((100-e)/100*360,100,100),r=jv(o),Iv(r));y(t,u,a)}))]),Lf.config({find:function(t){return c.getOpt(t)}}),Gm.config({mode:"acyclic"})])}}})},lS=function(){return Lf.config({find:st.some})},fS=function(t){return Lf.config({find:t.getOpt})},dS=function(t){return Lf.config({find:function(n){return wr(n.element(),t).bind(function(t){return n.getSystem().getByDom(t).toOption()})}})},mS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},gS=function(t){return mS[t]},pS=tinymce.util.Tools.resolve("tinymce.Resource"),hS=fn([te("preprocess",ct),te("postprocess",ct)]),vS=function(t,n,e){return El.config(zt({store:{mode:"manual",getValue:n,setValue:e}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))},bS=function(t,n,e){return vS(t,function(t){return n(t.element())},function(t,n){return e(t.element(),n)})},yS=function(r,t){var i=Tn("RepresentingConfigs.memento processors",hS,t);return El.config({store:{mode:"manual",getValue:function(t){var n=r.get(t),e=El.getValue(n);return i.postprocess(e)},setValue:function(t,n){var e=i.preprocess(n),o=r.get(t);El.setValue(o,e)}}})},xS=vS,wS=function(t){return bS(t,Dr,Ar)},SS=function(t){return El.config({store:{mode:"memory",initialValue:t}})},kS=function(r,n){var e=function(t,n){n.stop()},o=function(t){return function(n,e){rt(t,function(t){t(n,e)})}},i=function(t,n){if(!Dh.isDisabled(t)){var e=n.event().raw();a(t,e.dataTransfer.files)}},u=function(t,n){var e=n.event().raw().target.files;a(t,e)},a=function(t,n){var e,o;El.setValue(t,(e=n,o=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i"),H(J(e),function(t){return o.test(t.name)}))),Uo(t,fy,{name:r.name})},c=Zg({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:Ka([$m("input-file-events",[er(mo()),er(ko())])])}),t=r.label.map(function(t){return ly(t,n)}),s=oy.parts().field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:Ka([SS([]),lS(),Dh.config({}),gg.config({toggleClass:"dragenter",toggleOnExecute:!1}),$m("dropzone-events",[$o("dragenter",o([e,gg.toggle])),$o("dragleave",o([e,gg.toggle])),$o("dragover",e),$o("drop",o([e,i])),$o(fo(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:n.translate("Drop an image here")}},Qg.sketch({dom:{tag:"button",innerHtml:n.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element().dom().click()},buttonBehaviours:Ka([uy.config({}),ov(n.isReadOnly),nv()])})]}]}}}});return ay(t,s,["tox-form__group--stretched"],[])},CS=Wr("alloy-fake-before-tabstop"),OS=Wr("alloy-fake-after-tabstop"),_S=function(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:Ka([eg.config({ignore:!0}),uy.config({})])}},TS=function(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[_S([CS]),t,_S([OS])],behaviours:Ka([dS(1)])}},ES=function(t,n){Uo(t,co(),{raw:{which:9,shiftKey:n}})},BS=function(t,n){var e=n.element();Oi(e,CS)?ES(t,!0):Oi(e,OS)&&ES(t,!1)},DS=function(t){return Mb(t,["."+CS,"."+OS].join(","),at(!1))},AS=!(ze().browser.isIE()||ze().browser.isEdge()),MS=function(t,n){var o,r,e=AS&&t.sandboxed,i=et(et({},t.label.map(function(t){return{title:t}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),u=(o=e,r=se(""),{getValue:function(t){return r.get()},setValue:function(t,n){if(o)Fr(t.element(),"srcdoc",n);else{Fr(t.element(),"src","javascript:''");var e=t.element().dom().contentWindow.document;e.open(),e.write(n),e.close()}r.set(n)}}),a=t.label.map(function(t){return ly(t,n)}),c=oy.parts().field({factory:{sketch:function(t){return TS({uid:t.uid,dom:{tag:"iframe",attributes:i},behaviours:Ka([uy.config({}),eg.config({}),xS(st.none(),u.getValue,u.setValue)])})}}});return ay(a,c,["tox-form__group--stretched"],[])};function FS(t,n){return VS(nt.document.createElement("canvas"),t,n)}function IS(t){var n=FS(t.width,t.height);return RS(n).drawImage(t,0,0),n}function RS(t){return t.getContext("2d")}function VS(t,n,e){return t.width=n,t.height=e,t}function PS(t){return t.naturalWidth||t.width}function HS(t){return t.naturalHeight||t.height}var zS,NS,LS,jS=window.Promise?window.Promise:(NS=(zS=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],KS(t,US(GS,this),US(XS,this))}).immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(t){nt.setTimeout(t,1)},LS=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},zS.prototype["catch"]=function(t){return this.then(null,t)},zS.prototype.then=function(e,o){var r=this;return new zS(function(t,n){WS.call(r,new qS(e,o,t,n))})},zS.all=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var c=Array.prototype.slice.call(1===t.length&&LS(t[0])?t[0]:t);return new zS(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}for(var t=0;t<c.length;t++)a(t,c[t])})},zS.resolve=function(n){return n&&"object"==typeof n&&n.constructor===zS?n:new zS(function(t){t(n)})},zS.reject=function(e){return new zS(function(t,n){n(e)})},zS.race=function(r){return new zS(function(t,n){for(var e=0,o=r;e<o.length;e++)o[e].then(t,n)})},zS);function US(t,n){return function(){return t.apply(n,arguments)}}function WS(o){var r=this;null!==this._state?NS(function(){var t=r._state?o.onFulfilled:o.onRejected;if(null!==t){var n;try{n=t(r._value)}catch(e){return void o.reject(e)}o.resolve(n)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function GS(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void KS(US(n,t),US(GS,this),US(XS,this))}this._state=!0,this._value=t,YS.call(this)}catch(e){XS.call(this,e)}}function XS(t){this._state=!1,this._value=t,YS.call(this)}function YS(){for(var t=0,n=this._deferreds;t<n.length;t++){var e=n[t];WS.call(this,e)}this._deferreds=[]}function qS(t,n,e,o){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.resolve=e,this.reject=o}function KS(t,n,e){var o=!1;try{t(function(t){o||(o=!0,n(t))},function(t){o||(o=!0,e(t))})}catch(r){if(o)return;o=!0,e(r)}}function JS(e){return new jS(function(t,n){(function p(t){var n=t.split(","),e=/data:([^;]+)/.exec(n[0]);if(!e)return st.none();for(var o=e[1],r=n[1],i=nt.atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var l=1024*s,f=Math.min(1024+l,u),d=new Array(f-l),m=l,g=0;m<f;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return st.some(new nt.Blob(c,{type:o}))})(e).fold(function(){n("uri is not base64: "+e)},t)})}function $S(t,o,r){return o=o||"image/png",nt.HTMLCanvasElement.prototype.toBlob?new jS(function(n,e){t.toBlob(function(t){t?n(t):e()},o,r)}):JS(t.toDataURL(o,r))}function QS(t){return function n(a){return new jS(function(t,n){var e=nt.URL.createObjectURL(a),o=new nt.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),t(o)}function u(){r(),n("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}(t).then(function(t){!function e(t){nt.URL.revokeObjectURL(t.src)}(t);var n=FS(PS(t),HS(t));return RS(n).drawImage(t,0,0),n})}function ZS(t,n,e){var o=n.type;function r(n,e){return t.then(function(t){return function o(t,n,e){return n=n||"image/png",t.toDataURL(n,e)}(t,n,e)})}return{getType:at(o),toBlob:function i(){return jS.resolve(n)},toDataURL:at(e),toBase64:function u(){return e.split(",")[1]},toAdjustedBlob:function a(n,e){return t.then(function(t){return $S(t,n,e)})},toAdjustedDataURL:r,toAdjustedBase64:function c(t,n){return r(t,n).then(function(t){return t.split(",")[1]})},toCanvas:function s(){return t.then(IS)}}}function tk(n){return function t(e){return new jS(function(t){var n=new nt.FileReader;n.onloadend=function(){t(n.result)},n.readAsDataURL(e)})}(n).then(function(t){return ZS(QS(n),n,t)})}function nk(n,t){return $S(n,t).then(function(t){return ZS(jS.resolve(n),t,n.toDataURL())})}function ek(t,n,e){var o="string"==typeof t?parseFloat(t):t;return e<o?o=e:o<n&&(o=n),o}function ok(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}var rk=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function ik(t,n){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=n[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=t[u+5*a]*o[a];r[u+5*i]=e}}return r}function uk(n,e){return n.toCanvas().then(function(t){return function i(t,n,e){var o=RS(t);var r=function E(t,n){for(var e,o,r,i,u=t.data,a=n[0],c=n[1],s=n[2],l=n[3],f=n[4],d=n[5],m=n[6],g=n[7],p=n[8],h=n[9],v=n[10],b=n[11],y=n[12],x=n[13],w=n[14],S=n[15],k=n[16],C=n[17],O=n[18],_=n[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*l+f,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*k+r*C+i*O+_;return t}(o.getImageData(0,0,t.width,t.height),e);return o.putImageData(r,0,0),nk(t,n)}(t,n.getType(),e)})}function ak(n,e){return n.toCanvas().then(function(t){return function u(t,n,e){var o=RS(t);var r=o.getImageData(0,0,t.width,t.height),i=o.getImageData(0,0,t.width,t.height);return i=function w(t,n,e){function o(t,n,e){return e<t?t=e:t<n&&(t=n),t}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=t.data,a=n.data,c=t.width,s=t.height,l=0;l<s;l++)for(var f=0;f<c;f++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(f+h-i,0,c-1),b=4*(o(l+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(l*c+f);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return n}(r,i,e),o.putImageData(i,0,0),nk(t,n)}(t,n.getType(),e)})}function ck(e){return function(t,n){return uk(t,e(ok(),n))}}var sk=function $F(n){return function(t){return uk(t,n)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),lk=ck(function QF(t,n){return ik(t,[1,0,0,0,n=ek(255*n,-255,255),0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])}),fk=ck(function ZF(t,n){var e;return n=ek(n,-1,1),ik(t,[(e=(n*=100)<0?127+n/100*127:127*(e=0===(e=n%1)?rk[n]:rk[Math.floor(n)]*(1-e)+rk[Math.floor(n)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),dk=function(t,n,e,o){return uk(t,function r(t,n,e,o){return ik(t,[n=ek(n,0,2),0,0,0,0,0,e=ek(e,0,2),0,0,0,0,0,o=ek(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])}(ok(),n,e,o))},mk=function tI(n){return function(t){return ak(t,n)}}([0,-1,0,-1,5,-1,0,-1,0]),gk=function nI(c){var o=function(t,n,e){var o=RS(t),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=c(i,e);var u=function a(t,n){for(var e=t.data,o=0;o<e.length;o+=4)e[o]=n[e[o]],e[o+1]=n[e[o+1]],e[o+2]=n[e[o+2]];return t}(o.getImageData(0,0,t.width,t.height),r);return o.putImageData(u,0,0),nk(t,n)};return function(n,e){return n.toCanvas().then(function(t){return o(t,n.getType(),e)})}}(function(t,n){return 255*Math.pow(t/255,1-n)});function pk(t,n,e){var o=PS(t),r=HS(t),i=n/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function s(u,a,c){return new jS(function(t){var n=PS(u),e=HS(u),o=Math.floor(n*a),r=Math.floor(e*c),i=FS(o,r);RS(i).drawImage(u,0,0,n,e,0,0,o,r),t(i)})}(t,i,u);return a?c.then(function(t){return pk(t,n,e)}):c}function hk(n,e){return n.toCanvas().then(function(t){return function a(t,n,e){var o=FS(t.width,t.height),r=RS(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||VS(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(t,0,0),nk(o,n)}(t,n.getType(),e)})}function vk(n,e){return n.toCanvas().then(function(t){return function i(t,n,e){var o=FS(t.width,t.height),r=RS(o);"v"===e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0));return nk(o,n)}(t,n.getType(),e)})}function bk(n,e,o,r,i){return n.toCanvas().then(function(t){return function a(t,n,e,o,r,i){var u=FS(r,i);return RS(u).drawImage(t,-e,-o),nk(u,n)}(t,n.getType(),e,o,r,i)})}var yk=function(t){return sk(t)},xk=function(t){return mk(t)},wk=function(t,n){return gk(t,n)},Sk=function(t,n){return lk(t,n)},kk=function(t,n){return fk(t,n)},Ck=function(t,n){return vk(t,n)},Ok=function(t,n,e){return function r(n,e,o){return n.toCanvas().then(function(t){return pk(t,e,o).then(function(t){return nk(t,n.getType())})})}(t,n,e)},_k=function(t,n){return hk(t,n)},Tk=function(t,n){return et({dom:{tag:"span",innerHtml:t,classes:["tox-icon","tox-tbtn__icon-wrap"]}},n)},Ek=function(t,n){return Tk(np(t,n),{})},Bk=function(t,n){return Tk(np(t,n),{behaviours:Ka([Jm.config({})])})},Dk=function(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:Ka([Jm.config({})])}},Ak=Wr("toolbar.button.execute"),Mk={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},Fk=Wr("update-menu-text"),Ik=Wr("update-menu-icon"),Rk=function(t,n,o){var e=se(Z),r=t.text.map(function(t){return Zg(Dk(t,n,o.providers))}),i=t.icon.map(function(t){return Zg(Bk(t,o.providers.icons))}),u=function(t,n){var e=El.getValue(t);return eg.focus(e),Uo(e,"keydown",{raw:n.event().raw()}),sx.close(e),st.some(!0)},a=t.role.fold(function(){return{}},function(t){return{role:t}}),c=t.tooltip.fold(function(){return{}},function(t){var n=o.providers.translate(t);return{title:n,"aria-label":n}});return Zg(sx.sketch(et(et({},a),{dom:{tag:"button",classes:[n,n+"--select"].concat(V(t.classes,function(t){return n+"--"+t})),attributes:et({},c)},components:lv([i.map(function(t){return t.asSpec()}),r.map(function(t){return t.asSpec()}),st.some({dom:{tag:"div",classes:[n+"__select-chevron"],innerHtml:np("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:Ka(b(t.dropdownBehaviours,[ov(function(){return t.disabled||o.providers.isReadOnly()}),nv(),lx.config({}),Jm.config({}),$m("dropdown-events",[av(t,e),cv(t,e)]),$m("menubutton-update-display-text",[$o(Fk,function(n,e){r.bind(function(t){return t.getOpt(n)}).each(function(t){Jm.set(t,[ou(o.providers.translate(e.event().text()))])})}),$o(Ik,function(n,e){i.bind(function(t){return t.getOpt(n)}).each(function(t){Jm.set(t,[Bk(e.event().icon(),o.providers.icons)])})})])])),eventOrder:zt(Mk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:Ka([Gm.config({mode:"special",onLeft:u,onRight:u})]),lazySink:o.getSink,toggleClass:n+"--active",parts:{menu:Wp(0,t.columns,t.presets)},fetch:function(){return Iy(t.fetch)}}))).asSpec()},Vk=function(t){return"separator"===t.type},Pk={type:"separator"},Hk=function(t,e){var n=N(t,function(t,n){return w(n)?""===n?t:"|"===n?0<t.length&&!Vk(t[t.length-1])?t.concat([Pk]):t:It(e,n.toLowerCase())?t.concat([e[n.toLowerCase()]]):t:t.concat([n])},[]);return 0<n.length&&Vk(n[n.length-1])&&n.pop(),n},zk=function(t,n){return It(t,"getSubmenuItems")?(o=n,r=(e=t).getSubmenuItems(),i=Nk(r,o),{item:e,menus:zt(i.menus,Jt(e.value,i.items)),expansions:zt(i.expansions,Jt(e.value,e.value))}):{item:t,menus:{},expansions:{}};var e,o,r,i},Nk=function(t,r){var n=Hk(w(t)?t.split(" "):t,r);return z(n,function(t,n){var e=function(t){if(Vk(t))return t;var n=Ft(t,"value").getOrThunk(function(){return Wr("generated-menu-item")});return zt({value:n},t)}(n),o=zk(e,r);return{menus:zt(t.menus,o.menus),items:[o.item].concat(t.items),expansions:zt(t.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},Lk=function(t,e,o,n){var r=Wr("primary-menu"),i=Nk(t,o.shared.providers.menuItems());if(0===i.items.length)return st.none();var u=Ob(r,i.items,e,o,n),a=Tt(i.menus,function(t,n){return Ob(n,t,e,o,!1)}),c=zt(a,Jt(r,u));return st.from(Hg.tieredData(r,c,i.expansions))},jk=function(e){return{isDisabled:function(){return Dh.isDisabled(e)},setDisabled:function(t){return Dh.set(e,t)},setActive:function(t){var n=e.element();t?(Si(n,"tox-tbtn--enabled"),Fr(n,"aria-pressed",!0)):(Ci(n,"tox-tbtn--enabled"),Pr(n,"aria-pressed"))},isActive:function(){return Oi(e.element(),"tox-tbtn--enabled")}}},Uk=function(t,n,e,o){return Rk({text:t.text,icon:t.icon,tooltip:t.tooltip,role:o,fetch:function(n){t.fetch(function(t){n(Lk(t,Ap.CLOSE_ON_EXECUTE,e,!1))})},onSetup:t.onSetup,getApi:jk,columns:1,presets:"normal",classes:[],dropdownBehaviours:[uy.config({})]},n,e.shared)},Wk=function(n,r,i){return function(t){t(V(n,function(t){var n,e,o=t.text.fold(function(){return{}},function(t){return{text:t}});return et(et({type:t.type,active:!1},o),{onAction:function(t){var n=!t.isActive();t.setActive(n),e.storage.set(n),i.shared.getSink().each(function(t){r().getOpt(t).each(function(t){ic(t.element()),Uo(t,gy,{name:e.name,value:e.storage.get()})})})},onSetup:(n=e=t,function(t){t.setActive(n.storage.get())})})}))}},Gk=function(t,n,e,o,r,i){void 0===e&&(e=[]);var u=n.fold(function(){return{}},function(t){return{action:t}}),a=et({buttonBehaviours:Ka([ov(function(){return t.disabled||i.isReadOnly()}),nv(),uy.config({}),$m("button press",[Jo("click"),Jo("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=zt(a,{dom:o});return zt(c,{components:r})},Xk=function(t,n,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})},i=t.icon.map(function(t){return Ek(t,e.icons)}),u=lv([i]);return Gk(t,n,o,r,u,e)},Yk=function(t,n,e,o){void 0===o&&(o=[]);var r=Xk(t,st.some(n),e,o);return Qg.sketch(r)},qk=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(t.text),u=t.icon?t.icon.map(function(t){return Ek(t,e.icons)}):st.none(),a=u.isSome()?lv([u]):[],c=u.isSome()?{}:{innerHtml:i},s=b(t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],t.borderless?["tox-button--naked"]:[],r),l=et(et({tag:"button",classes:s},c),{attributes:{title:i}});return Gk(t,n,o,l,a,e)},Kk=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=qk(t,st.some(n),e,o,r);return Qg.sketch(i)},Jk=function(n,e){return function(t){"custom"===e?Uo(t,gy,{name:n,value:{}}):"submit"===e?jo(t,py):"cancel"===e?jo(t,my):nt.console.error("Unknown button type: ",e)}},$k=function(n,t,e){if("menu"===t){var o=n,r=et(et({},n),{onSetup:function(t){return t.setDisabled(n.disabled),Z},fetch:Wk(o.items,function(){return i},e)}),i=Zg(Uk(r,"tox-tbtn",e,st.none()));return i.asSpec()}if("custom"===(c=t)||"cancel"===c||"submit"===c){var u=Jk(n.name,t),a=et(et({},n),{borderless:!1});return Kk(a,u,e.shared.providers,[])}var c;nt.console.error("Unknown footer button type: ",t)},Qk=function(t,n){var e,o,r=Jk(t.name,"custom");return e=st.none(),o=oy.parts().field(et({factory:Qg},qk(t,st.some(r),n,[SS(""),lS()]))),ay(e,o,[],[])},Zk=at([te("field1Name","field1"),te("field2Name","field2"),sa("onLockedChange"),ia(["lockClass"]),te("locked",!1),Ml("coupledFieldBehaviours",[Lf,El])]),tC=function(t,n){return rf({factory:oy,name:t,overrides:function(o){return{fieldBehaviours:Ka([$m("coupled-input-behaviour",[$o(lo(),function(e){yf(e,o,n).bind(Lf.getCurrent).each(function(n){yf(e,o,"lock").each(function(t){gg.isOn(t)&&o.onLockedChange(e,n,t)})})})])])}}})},nC=at([tC("field1","field2"),tC("field2","field1"),rf({factory:Qg,schema:[Nn("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:Ka([gg.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),eC=Vf({name:"FormCoupledInputs",configFields:Zk(),partFields:nC(),factory:function(o,t,n,e){return{uid:o.uid,dom:o.dom,components:t,behaviours:Fl(o.coupledFieldBehaviours,[Lf.config({find:st.some}),El.config({store:{mode:"manual",getValue:function(t){var n,e=Cf(t,o,["field1","field2"]);return(n={})[o.field1Name]=El.getValue(e.field1()),n[o.field2Name]=El.getValue(e.field2()),n},setValue:function(t,n){var e=Cf(t,o,["field1","field2"]);Rt(n,o.field1Name)&&El.setValue(e.field1(),n[o.field1Name]),Rt(n,o.field2Name)&&El.setValue(e.field2(),n[o.field2Name])}}})]),apis:{getField1:function(t){return yf(t,o,"field1")},getField2:function(t){return yf(t,o,"field2")},getLock:function(t){return yf(t,o,"lock")}}}},apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),oC=function(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===n)return ut.error(t);var e=parseFloat(n[1]),o=n[2];return ut.value({value:e,unit:o})},rC=function(t,n){var e={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1},o=function(t){return Object.prototype.hasOwnProperty.call(e,t)};return t.unit===n?st.some(t.value):o(t.unit)&&o(n)?e[t.unit]===e[n]?st.some(t.value):st.some(t.value/e[t.unit]*e[n]):st.none()},iC=function(t){return st.none()},uC=function(t,n){var e,o,r,i=oC(t).toOption(),u=oC(n).toOption();return o=u,r=function(t,o){return rC(t,o.unit).map(function(t){return o.value/t}).map(function(t){return n=t,e=o.unit,function(t){return rC(t,e).map(function(t){return{value:t*n,unit:e}})};var n,e}).getOr(iC)},((e=i).isSome()&&o.isSome()?st.some(r(e.getOrDie(),o.getOrDie())):st.none()).getOr(iC)},aC=function(o,n){var a=iC,r=Wr("ratio-event"),t=eC.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:np("lock",n.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:np("unlock",n.icons)}}],buttonBehaviours:Ka([Dh.config({disabled:function(){return o.disabled||n.isReadOnly()}}),nv(),uy.config({})])}),e=function(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}},i=function(e){return oy.parts().field({factory:Cy,inputClasses:["tox-textfield"],inputBehaviours:Ka([Dh.config({disabled:function(){return o.disabled||n.isReadOnly()}}),nv(),uy.config({}),$m("size-input-events",[$o(uo(),function(t,n){Uo(t,r,{isField1:e})}),$o(fo(),function(t,n){Uo(t,fy,{name:o.name})})])]),selectOnFocus:!1})},u=function(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}},c=eC.parts().field1(e([oy.parts().label(u("Width")),i(!0)])),s=eC.parts().field2(e([oy.parts().label(u("Height")),i(!1)]));return eC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,e([u("&nbsp;"),t])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,n){oC(El.getValue(t)).each(function(t){a(t).each(function(t){var n,e,o,r;El.setValue(i,(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},-1!==(r=(n=t).value.toFixed((e=n.unit)in o?o[e]:1)).indexOf(".")&&(r=r.replace(/\.?0*$/,"")),r+n.unit))})})},coupledFieldBehaviours:Ka([Dh.config({disabled:function(){return o.disabled||n.isReadOnly()},onDisabled:function(t){eC.getField1(t).bind(oy.getField).each(Dh.disable),eC.getField2(t).bind(oy.getField).each(Dh.disable),eC.getLock(t).each(Dh.disable)},onEnabled:function(t){eC.getField1(t).bind(oy.getField).each(Dh.enable),eC.getField2(t).bind(oy.getField).each(Dh.enable),eC.getLock(t).each(Dh.enable)}}),nv(),$m("size-input-events2",[$o(r,function(t,n){var e=n.event().isField1(),o=e?eC.getField1(t):eC.getField2(t),r=e?eC.getField2(t):eC.getField1(t),i=o.map(El.getValue).getOr(""),u=r.map(El.getValue).getOr("");a=uC(i,u)})])])})},cC={undo:at(Wr("undo")),redo:at(Wr("redo")),zoom:at(Wr("zoom")),back:at(Wr("back")),apply:at(Wr("apply")),swap:at(Wr("swap")),transform:at(Wr("transform")),tempTransform:at(Wr("temp-transform")),transformApply:at(Wr("transform-apply"))},sC=at("save-state"),lC=at("disable"),fC=at("enable"),dC={formActionEvent:gy,saveState:sC,disable:lC,enable:fC},mC=function(a,c){var t=function(t,n,e,o){return Zg(Kk({name:t,text:t,disabled:e,primary:o,icon:st.none(),borderless:!1},n,c))},n=function(t,n,e,o){return Zg(Yk({name:t,icon:st.some(t),tooltip:st.some(n),disabled:o,primary:!1,borderless:!1},e,c))},u=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(Dh)&&Dh.disable(n)})},s=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(Dh)&&Dh.enable(n)})},l={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},e=Z,r=function(t,n,e){Uo(t,n,e)},i=function(t){return jo(t,dC.disable())},f=function(t){return jo(t,dC.enable())},d=function(t,n){i(t),r(t,cC.transform(),{transform:n}),f(t)},o=function(t){return function(){Q.getOpt(t).each(function(t){Jm.set(t,[J])})}},m=function(t,n){i(t),r(t,cC.transformApply(),{transform:n,swap:o(t)}),f(t)},g=function(){return t("Back",function(t){return r(t,cC.back(),{swap:o(t)})},!1,!1)},p=function(){return Zg({dom:{tag:"div",classes:["tox-spacer"]},behaviours:Ka([Dh.config({})])})},h=function(){return t("Apply",function(t){return r(t,cC.apply(),{swap:o(t)})},!0,!0)},v=function(){return function(t){var n,e,o,r,i,u=a.getRect();return n=t,e=u.x,o=u.y,r=u.w,i=u.h,bk(n,e,o,r,i)}},b=[g(),p(),t("Apply",function(t){var n=v();m(t,n),a.hideCrop()},!1,!0)],y=Zb.sketch({dom:l,components:b.map(function(t){return t.asSpec()}),containerBehaviours:Ka([$m("image-tools-crop-buttons-events",[$o(dC.disable(),function(t,n){u(b,t)}),$o(dC.enable(),function(t,n){s(b,t)})])])}),x=Zg(aC({name:"size",label:st.none(),constrain:!0,disabled:!1},c)),w=[g(),p(),x,p(),t("Apply",function(a){x.getOpt(a).each(function(t){var n,e,o=El.getValue(t),r=parseInt(o.width,10),i=parseInt(o.height,10),u=(n=r,e=i,function(t){return Ok(t,n,e)});m(a,u)})},!1,!0)],S=Zb.sketch({dom:l,components:w.map(function(t){return t.asSpec()}),containerBehaviours:Ka([$m("image-tools-resize-buttons-events",[$o(dC.disable(),function(t,n){u(w,t)}),$o(dC.enable(),function(t,n){s(w,t)})])])}),k=function(n,e){return function(t){return n(t,e)}},C=k(Ck,"h"),O=k(Ck,"v"),_=k(_k,-90),T=k(_k,90),E=function(t,n){var e,o;o=n,i(e=t),r(e,cC.tempTransform(),{transform:o}),f(e)},B=[g(),p(),n("flip-horizontally","Flip horizontally",function(t){E(t,C)},!1),n("flip-vertically","Flip vertically",function(t){E(t,O)},!1),n("rotate-left","Rotate counterclockwise",function(t){E(t,_)},!1),n("rotate-right","Rotate clockwise",function(t){E(t,T)},!1),p(),h()],D=Zb.sketch({dom:l,components:B.map(function(t){return t.asSpec()}),containerBehaviours:Ka([$m("image-tools-fliprotate-buttons-events",[$o(dC.disable(),function(t,n){u(B,t)}),$o(dC.enable(),function(t,n){s(B,t)})])])}),A=function(t,n,e,o,r){var i=Qw.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=Qw.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=Qw.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return Zg(Qw.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:at({x:at(o)})},components:[i,u,a],sliderBehaviours:Ka([eg.config({})]),onChoose:n}))},M=function(t,n,e,o,r){return[g(),(i=n,A(t,function(t,n,e){var o=k(i,e.x()/100);d(t,o)},e,o,r)),h()];var i},F=function(t,n,e,o,r){var i=M(t,n,e,o,r);return Zb.sketch({dom:l,components:i.map(function(t){return t.asSpec()}),containerBehaviours:Ka([$m("image-tools-filter-panel-buttons-events",[$o(dC.disable(),function(t,n){u(i,t)}),$o(dC.enable(),function(t,n){s(i,t)})])])})},I=[g(),p(),h()],R=Zb.sketch({dom:l,components:I.map(function(t){return t.asSpec()})}),V=F("Brightness",Sk,-100,0,100),P=F("Contrast",kk,-100,0,100),H=F("Gamma",wk,-100,0,100),z=function(n,e,o){return function(t){return dk(t,n,e,o)}},N=function(t){return A(t,function(a,t,n){var e=L.getOpt(a),o=U.getOpt(a),r=j.getOpt(a);e.each(function(u){o.each(function(i){r.each(function(t){var n=El.getValue(u).x()/100,e=El.getValue(t).x()/100,o=El.getValue(i).x()/100,r=z(n,e,o);d(a,r)})})})},0,100,200)},L=N("R"),j=N("G"),U=N("B"),W=[g(),L,j,U,h()],G=Zb.sketch({dom:l,components:W.map(function(t){return t.asSpec()})}),X=function(n,e,o){return function(t){r(t,cC.swap(),{transform:e,swap:function(){Q.getOpt(t).each(function(t){Jm.set(t,[n]),o(t)})}})}},Y=st.some(xk),q=st.some(yk),K=[n("crop","Crop",X(y,st.none(),function(t){a.showCrop()}),!1),n("resize","Resize",X(S,st.none(),function(t){x.getOpt(t).each(function(t){var n=a.getMeasurements(),e=n.width,o=n.height;El.setValue(t,{width:e,height:o})})}),!1),n("orientation","Orientation",X(D,st.none(),e),!1),n("brightness","Brightness",X(V,st.none(),e),!1),n("sharpen","Sharpen",X(R,Y,e),!1),n("contrast","Contrast",X(P,st.none(),e),!1),n("color-levels","Color levels",X(G,st.none(),e),!1),n("gamma","Gamma",X(H,st.none(),e),!1),n("invert","Invert",X(R,q,e),!1)],J=Zb.sketch({dom:l,components:K.map(function(t){return t.asSpec()})}),$=Zb.sketch({dom:{tag:"div"},components:[J],containerBehaviours:Ka([Jm.config({})])}),Q=Zg($);return{memContainer:Q,getApplyButton:function(t){return Q.getOpt(t).map(function(t){var n=t.components()[0];return n.components()[n.components().length-1]})}}},gC=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),pC=tinymce.util.Tools.resolve("tinymce.geom.Rect"),hC=tinymce.util.Tools.resolve("tinymce.util.Observable"),vC=tinymce.util.Tools.resolve("tinymce.util.Tools"),bC=tinymce.util.Tools.resolve("tinymce.util.VK");function yC(t){var n,e;if(t.changedTouches)for(n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]}function xC(t,r){var i,u,a,c,l=r.document||nt.document;r=r||{};var f=l.getElementById(r.handle||t),d=function(t){if(yC(t),t.button!==u)return m(t);t.deltaX=t.screenX-a,t.deltaY=t.screenY-c,t.preventDefault(),r.drag(t)},m=function(t){yC(t),gC(l).off("mousemove touchmove",d).off("mouseup touchend",m),i.remove(),r.stop&&r.stop(t)};this.destroy=function(){gC(f).off()},gC(f).on("mousedown touchstart",function(t){var n,e=function s(t){var n=Math.max,e=t.documentElement,o=t.body,r=n(e.scrollWidth,o.scrollWidth),i=n(e.clientWidth,o.clientWidth),u=n(e.offsetWidth,o.offsetWidth),a=n(e.scrollHeight,o.scrollHeight),c=n(e.clientHeight,o.clientHeight);return{width:r<u?i:r,height:a<n(e.offsetHeight,o.offsetHeight)?c:a}}(l);yC(t),t.preventDefault(),u=t.button;var o=f;a=t.screenX,c=t.screenY,n=nt.window.getComputedStyle?nt.window.getComputedStyle(o,null).getPropertyValue("cursor"):o.runtimeStyle.cursor,i=gC("<div></div>").css({position:"absolute",top:0,left:0,width:e.width,height:e.height,zIndex:2147483647,opacity:1e-4,cursor:n}).appendTo(l.body),gC(l).on("mousemove touchmove",d).on("mouseup touchend",m),r.start(t)})}var wC=0,SC=function(s,e,l,o,r){var n,i="tox-",u="tox-crid-"+wC++,a=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],c=["top","right","bottom","left"],f=function(t,n){return{x:n.x+t.x,y:n.y+t.y,w:n.w,h:n.h}},d=function(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}};function m(t,n,e,o){var r,i,u,a,c;r=n.x,i=n.y,u=n.w,a=n.h,r+=e*t.deltaX,i+=o*t.deltaY,(u+=e*t.deltaW)<20&&(u=20),(a+=o*t.deltaH)<20&&(a=20),c=s=pC.clamp({x:r,y:i,w:u,h:a},l,"move"===t.name),c=d(l,c),v.fire("updateRect",{rect:c}),h(c)}function g(n){function t(t,n){n.h<0&&(n.h=0),n.w<0&&(n.w=0),gC("#"+u+"-"+t,o).css({left:n.x,top:n.y,width:n.w,height:n.h})}vC.each(a,function(t){gC("#"+u+"-"+t.name,o).css({left:n.w*t.xMul+n.x,top:n.h*t.yMul+n.y})}),t("top",{x:e.x,y:e.y,w:e.w,h:n.y-e.y}),t("right",{x:n.x+n.w,y:n.y,w:e.w-n.x-n.w+e.x,h:n.h}),t("bottom",{x:e.x,y:n.y+n.h,w:e.w,h:e.h-n.y-n.h+e.y}),t("left",{x:e.x,y:n.y,w:n.x-e.x,h:n.h}),t("move",n)}function p(t){g(s=t)}function h(t){p(f(l,t))}!function b(){gC('<div id="'+u+'" class="'+i+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),vC.each(c,function(t){gC("#"+u,o).append('<div id="'+u+"-"+t+'"class="'+i+'croprect-block" style="display: none" data-mce-bogus="all">')}),vC.each(a,function(t){gC("#"+u,o).append('<div id="'+u+"-"+t.name+'" class="'+i+"croprect-handle "+i+"croprect-handle-"+t.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+t.label+'" aria-grabbed="false" title="'+t.label+'">')}),n=vC.map(a,function t(n){var e;return new xC(u,{document:o.ownerDocument,handle:u+"-"+n.name,start:function(){e=s},drag:function(t){m(n,e,t.deltaX,t.deltaY)}})}),g(s),gC(o).on("focusin focusout",function(t){gC(t.target).attr("aria-grabbed","focus"===t.type?"true":"false")}),gC(o).on("keydown",function(n){var i;function t(t,n,e,o,r){t.stopPropagation(),t.preventDefault(),m(i,e,o,r)}switch(vC.each(a,function(t){if(n.target.id===u+"-"+t.name)return i=t,!1}),n.keyCode){case bC.LEFT:t(n,0,s,-10,0);break;case bC.RIGHT:t(n,0,s,10,0);break;case bC.UP:t(n,0,s,0,-10);break;case bC.DOWN:t(n,0,s,0,10);break;case bC.ENTER:case bC.SPACEBAR:n.preventDefault(),r()}})}();var v=vC.extend({toggleVisibility:function y(t){var n=vC.map(a,function(t){return"#"+u+"-"+t.name}).concat(vC.map(c,function(t){return"#"+u+"-"+t})).join(",");t?gC(n,o).show():gC(n,o).hide()},setClampRect:function x(t){l=t,g(s)},setRect:p,getInnerRect:function(){return d(l,s)},setInnerRect:h,setViewPortRect:function w(t){e=t,g(s)},destroy:function t(){vC.each(n,function(t){t.destroy()}),n=[]}},hC);return v},kC=function(n){var l=Zg({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),f=se(1),d=se(st.none()),m=se({x:0,y:0,w:1,h:1}),c=se({x:0,y:0,w:1,h:1}),s=function(t,s){g.getOpt(t).each(function(t){var e=f.get(),o=bu(t.element()),r=lu(t.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),n={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};Li(s,n),l.getOpt(t).each(function(t){Li(t.element(),n)}),d.get().each(function(t){var n=m.get();t.setRect({x:n.x*e+a,y:n.y*e+c,w:n.w*e,h:n.h*e}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})},e=function(t,n){var e,a=fe.fromTag("img");return Fr(a,"src",n),e=a.dom(),new bp(function(t){var n=function(){e.removeEventListener("load",n),t(e)};e.complete?t(e):e.addEventListener("load",n)}).then(function(){return g.getOpt(t).map(function(t){var n=ru({element:a});Jm.replaceAt(t,1,st.some(n));var e=c.get(),o={x:0,y:0,w:a.dom().naturalWidth,h:a.dom().naturalHeight};c.set(o);var r,u,i=pC.inflate(o,-20,-20);return m.set(i),e.w===o.w&&e.h===o.h||(r=t,u=a,g.getOpt(r).each(function(t){var n=bu(t.element()),e=lu(t.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(n/o,e/r);1<=i?f.set(1):f.set(i)})),s(t,a),a})})},t=Zb.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[l.asSpec(),{dom:{tag:"img",attributes:{src:n}}},{dom:{tag:"div"},behaviours:Ka([$m("image-panel-crop-events",[rr(function(t){g.getOpt(t).each(function(t){var n=t.element().dom(),e=SC({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},n,function(){});e.toggleVisibility(!1),e.on("updateRect",function(t){var n=t.rect,e=f.get(),o={x:Math.round(n.x/e),y:Math.round(n.y/e),w:Math.round(n.w/e),h:Math.round(n.h/e)};m.set(o)}),d.set(st.some(e))})})])])}],containerBehaviours:Ka([Jm.config({}),$m("image-panel-events",[rr(function(t){e(t,n)})])])}),g=Zg(t);return{memContainer:g,updateSrc:e,zoom:function(t,n){var e=f.get(),o=0<n?Math.min(2,e+.1):Math.max(.1,e-.1);f.set(o),g.getOpt(t).each(function(t){var n=t.components()[1].element();s(t,n)})},showCrop:function(){d.get().each(function(t){t.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(t){t.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var t=c.get();return{width:t.w,height:t.h}}}},CC=function(t,n,e,o,r){return Yk({name:t,icon:st.some(n),disabled:e,tooltip:st.some(t),primary:!1,borderless:!1},o,r)},OC=function(t,n){n?Dh.enable(t):Dh.disable(t)};function _C(){var e=[],o=-1;function t(){return 0<o}function n(){return-1!==o&&o<e.length-1}return{data:e,add:function r(t){var n=e.splice(++o);return e.push(t),{state:t,removed:n}},undo:function i(){if(t())return e[--o]},redo:function u(){if(n())return e[++o]},canUndo:t,canRedo:n}}var TC,EC,BC,DC,AC=function(t){var n=se(t),e=se(st.none()),r=_C();r.add(t);var i=function(t){n.set(t)},u=function(t){return{blob:t,url:nt.URL.createObjectURL(t)}},a=function(t){nt.URL.revokeObjectURL(t.url)},o=function(){e.get().each(a),e.set(st.none())},c=function(t){var n=u(t);i(n);var e,o=r.add(n).removed;return e=o,vC.each(e,a),n.url};return{getBlobState:function(){return n.get()},setBlobState:i,addBlobState:c,getTempState:function(){return e.get().fold(function(){return n.get()},function(t){return t})},updateTempState:function(t){var n=u(t);return o(),e.set(st.some(n)),n.url},addTempState:function(t){var n=u(t);return e.set(st.some(n)),n.url},applyTempState:function(n){return e.get().fold(function(){},function(t){c(t.blob),n()})},destroyTempState:o,undo:function(){var t=r.undo();return i(t),t.url},redo:function(){var t=r.redo();return i(t),t.url},getHistoryStates:function(){return{undoEnabled:r.canUndo(),redoEnabled:r.canRedo()}}}},MC=function(t,n){var e,o,r,u=AC(t.currentState),i=function(t){var n=u.getHistoryStates();p.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),Uo(t,dC.formActionEvent,{name:dC.saveState(),value:n.undoEnabled})},a=function(t){return t.toBlob()},c=function(t){Uo(t,dC.formActionEvent,{name:dC.disable(),value:{}})},s=function(t){h.getApplyButton(t).each(function(t){Dh.enable(t)}),Uo(t,dC.formActionEvent,{name:dC.enable(),value:{}})},l=function(t,n){return c(t),g.updateSrc(t,n)},f=function(n,t,e,o,r){return c(n),tk(t).then(e).then(a).then(o).then(function(t){return l(n,t).then(function(t){return i(n),r(),s(n),t})})["catch"](function(t){return nt.console.log(t),s(n),t})},d=function(t,n,e){var o=u.getBlobState().blob;f(t,o,n,function(t){return u.updateTempState(t)},e)},m=function(t){var n=u.getBlobState().url;return u.destroyTempState(),i(t),n},g=kC(t.currentState.url),p=(o=Zg(CC("Undo","undo",!0,function(t){Uo(t,cC.undo(),{direction:1})},e=n)),r=Zg(CC("Redo","redo",!0,function(t){Uo(t,cC.redo(),{direction:1})},e)),{container:Zb.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),CC("Zoom in","zoom-in",!1,function(t){Uo(t,cC.zoom(),{direction:1})},e),CC("Zoom out","zoom-out",!1,function(t){Uo(t,cC.zoom(),{direction:-1})},e)]}),updateButtonUndoStates:function(t,n,e){o.getOpt(t).each(function(t){OC(t,n)}),r.getOpt(t).each(function(t){OC(t,e)})}}),h=mC(g,n);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[h.memContainer.asSpec(),g.memContainer.asSpec(),p.container],behaviours:Ka([El.config({store:{mode:"manual",getValue:function(){return u.getBlobState()}}}),$m("image-tools-events",[$o(cC.undo(),function(n,t){var e=u.undo();l(n,e).then(function(t){s(n),i(n)})}),$o(cC.redo(),function(n,t){var e=u.redo();l(n,e).then(function(t){s(n),i(n)})}),$o(cC.zoom(),function(t,n){var e=n.event().direction();g.zoom(t,e)}),$o(cC.back(),function(t,n){var e,o;o=m(e=t),l(e,o).then(function(t){s(e)}),n.event().swap()(),g.hideCrop()}),$o(cC.apply(),function(t,n){u.applyTempState(function(){m(t),n.event().swap()()})}),$o(cC.transform(),function(t,n){return d(t,n.event().transform(),Z)}),$o(cC.tempTransform(),function(t,n){return e=t,o=n.event().transform(),r=u.getTempState().blob,void f(e,r,o,function(t){return u.addTempState(t)},Z);var e,o,r}),$o(cC.transformApply(),function(t,n){return e=t,o=n.event().transform(),r=n.event().swap(),i=u.getBlobState().blob,void f(e,i,o,function(t){var n=u.addBlobState(t);return m(e),n},r);var e,o,r,i}),$o(cC.swap(),function(n,t){var e;e=n,p.updateButtonUndoStates(e,!1,!1);var o=t.event().transform(),r=t.event().swap();o.fold(function(){r()},function(t){d(n,t,r)})})]),lS()])}},FC=Rf({name:"HtmlSelect",configFields:[Nn("options"),Bl("selectBehaviours",[eg,El]),te("selectClasses",[]),te("selectAttributes",{}),qn("data")],factory:function(e,t){var n=V(e.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=e.data.map(function(t){return Jt("initialValue",t)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:n,behaviours:Al(e.selectBehaviours,[eg.config({}),El.config({store:et({mode:"manual",getValue:function(t){return Ki(t.element())},setValue:function(t,n){L(e.options,function(t){return t.value===n}).isSome()&&Ji(t.element(),n)}},o)})])}}}),IC=function(e,n){var t=e.label.map(function(t){return ly(t,n)}),o=[Dh.config({disabled:function(){return e.disabled||n.isReadOnly()}}),nv(),Gm.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(t){return jo(t,py),st.some(!0)}}),$m("textfield-change",[$o(lo(),function(t,n){Uo(t,fy,{name:e.name})}),$o(yo(),function(t,n){Uo(t,fy,{name:e.name})})]),uy.config({})],r=e.validation.map(function(o){return Gy.config({getRoot:function(t){return br(t.element())},invalidClass:"tox-invalid",validator:{validate:function(t){var n=El.getValue(t),e=o.validator(n);return Ry(!0===e?ut.value(n):ut.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(at({}),function(t){return{placeholder:n.translate(t)}}),u=e.inputMode.fold(at({}),function(t){return{inputmode:t}}),a=et(et({},i),u),c=oy.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:Ka(it([o,r])),selectOnFocus:!1,factory:Cy}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[Dh.config({disabled:function(){return e.disabled||n.isReadOnly()},onDisabled:function(t){oy.getField(t).each(Dh.disable)},onEnabled:function(t){oy.getField(t).each(Dh.enable)}}),nv()];return ay(t,c,s,l)},RC=/* */Object.freeze({__proto__:null,events:function(t,n){var e=t.stream.streams.setup(t,n);return qo([$o(t.event,e),ir(function(){return n.cancel()})].concat(t.cancelEvent.map(function(t){return[$o(t,function(){return n.cancel()})]}).getOr([])))}}),VC=function(t){var n=se(null);return ai({readState:function(){return{timer:null!==n.get()?"set":"unset"}},setTimer:function(t){n.set(t)},cancel:function(){var t=n.get();null!==t&&t.cancel()}})},PC=/* */Object.freeze({__proto__:null,throttle:VC,init:function(t){return t.stream.streams.state(t)}}),HC=[Ln("stream",Dn("mode",{throttle:[Nn("delay"),te("stopEvent",!0),fa("streams",{setup:function(t,n){var e=t.stream,o=ap(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},state:VC})]})),te("event","input"),qn("cancelEvent"),sa("onStream")],zC=$a({fields:HC,name:"streaming",active:RC,state:PC}),NC=function(t,n,e){var o=El.getValue(e);El.setValue(n,o),jC(n)},LC=function(t,n){var e=t.element(),o=Ki(e),r=e.dom();"number"!==Ir(e,"type")&&n(r,o)},jC=function(t){LC(t,function(t,n){return t.setSelectionRange(n.length,n.length)})},UC=function(t,n,o){if(t.selectsOver){var e=El.getValue(n),r=t.getDisplayText(e),i=El.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?st.some(function(){var t,e;NC(0,n,o),t=n,e=r.length,LC(t,function(t,n){return t.setSelectionRange(e,n.length)})}):st.none()}return st.none()},WC=at("alloy.typeahead.itemexecute"),GC=at([qn("lazySink"),Nn("fetch"),te("minChars",5),te("responseTime",1e3),aa("onOpen"),te("getHotspot",st.some),te("getAnchorOverrides",at({})),te("layouts",st.none()),te("eventOrder",{}),ae("model",{},[te("getDisplayText",function(t){return t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.value}),te("selectsOver",!0),te("populateFromBrowse",!0)]),aa("onSetValue"),ca("onExecute"),aa("onItemExecute"),te("inputClasses",[]),te("inputAttributes",{}),te("inputStyles",{}),te("matchWidth",!0),te("useMinWidth",!1),te("dismissOnBlur",!0),ia(["openClass"]),qn("initialData"),Bl("typeaheadBehaviours",[eg,El,zC,Gm,gg,qy]),ce("previewing",function(){return se(!0)})].concat(wy()).concat(ux())),XC=at([uf({schema:[ra()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(n,e){o.previewing.get()?n.getSystem().getByUid(o.uid).each(function(t){UC(o.model,t,e).fold(function(){return Qf.dehighlight(n,e)},function(t){return t()})}):n.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&NC(o.model,t,e)}),o.previewing.set(!1)},onExecute:function(t,n){return t.getSystem().getByUid(o.uid).toOption().map(function(t){return Uo(t,WC(),{item:n}),!0})},onHover:function(t,n){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&NC(o.model,t,n)})}}}})]),YC=Vf({name:"Typeahead",configFields:GC(),partFields:XC(),factory:function(r,t,n,i){var e=function(t,n,e){r.previewing.set(!1);var o=qy.getCoupled(t,"sandbox");if(cl.isOpen(o))Lf.getCurrent(o).each(function(t){Qf.getHighlighted(t).fold(function(){e(t)},function(){Yo(o,t.element(),"keydown",n)})});else{Qy(r,u(t),t,o,i,function(t){Lf.getCurrent(t).each(e)},Ty.HighlightFirst).get(Z)}},o=Sy(r),u=function(o){return function(t){return t.map(function(t){var n=Mt(t.menus),e=U(n,function(t){return H(t.items,function(t){return"item"===t.type})});return El.getState(o).update(V(e,function(t){return t.data})),t})}},a=[eg.config({}),El.config({onSetValue:r.onSetValue,store:et({mode:"dataset",getDataKey:function(t){return Ki(t.element())},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,n){Ji(t.element(),r.model.getDisplayText(n))}},r.initialData.map(function(t){return Jt("initialValue",t)}).getOr({}))}),zC.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,n){var e=qy.getCoupled(t,"sandbox");if(eg.isFocused(t)&&Ki(t.element()).length>=r.minChars){var o=Lf.getCurrent(e).bind(function(t){return Qf.getHighlighted(t).map(El.getValue)});r.previewing.set(!0);Qy(r,u(t),t,e,i,function(t){Lf.getCurrent(e).each(function(t){o.fold(function(){r.model.selectsOver&&Qf.highlightFirst(t)},function(n){Qf.highlightBy(t,function(t){return El.getValue(t).value===n.value}),Qf.getHighlighted(t).orThunk(function(){return Qf.highlightFirst(t),st.none()})})})},Ty.HighlightFirst).get(Z)}},cancelEvent:_o()}),Gm.config({mode:"special",onDown:function(t,n){return e(t,n,Qf.highlightFirst),st.some(!0)},onEscape:function(t){var n=qy.getCoupled(t,"sandbox");return cl.isOpen(n)?(cl.close(n),st.some(!0)):st.none()},onUp:function(t,n){return e(t,n,Qf.highlightLast),st.some(!0)},onEnter:function(n){var t=qy.getCoupled(n,"sandbox"),e=cl.isOpen(t);if(e&&!r.previewing.get())return Lf.getCurrent(t).bind(function(t){return Qf.getHighlighted(t)}).map(function(t){return Uo(n,WC(),{item:t}),!0});var o=El.getValue(n);return jo(n,_o()),r.onExecute(t,n,o),e&&cl.close(t),st.some(!0)}}),gg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),qy.config({others:{sandbox:function(t){return rx(r,t,{onOpen:function(){return gg.on(t)},onClose:function(){return gg.off(t)}})}}}),$m("typeaheadevents",[ar(function(t){var n=Z;tx(r,u(t),t,i,n,Ty.HighlightFirst).get(Z)}),$o(WC(),function(t,n){var e=qy.getCoupled(t,"sandbox");NC(r.model,t,n.event().item()),jo(t,_o()),r.onItemExecute(t,e,n.event().item(),El.getValue(t)),cl.close(e),jC(t)})].concat(r.dismissOnBlur?[$o(bo(),function(t){var n=qy.getCoupled(t,"sandbox");ac(n.element()).isNone()&&cl.close(n)})]:[]))];return{uid:r.uid,dom:ky(zt(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:et(et({},o),Al(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),qC=function(i){return et(et({},i),{toCached:function(){return qC(i.toCached())},bindFuture:function(n){return qC(i.bind(function(t){return t.fold(function(t){return Ry(ut.error(t))},function(t){return n(t)})}))},bindResult:function(n){return qC(i.map(function(t){return t.bind(n)}))},mapResult:function(n){return qC(i.map(function(t){return t.map(n)}))},mapError:function(n){return qC(i.map(function(t){return t.mapError(n)}))},foldResult:function(n,e){return i.map(function(t){return t.fold(n,e)})},withTimeout:function(t,r){return qC(Iy(function(n){var e=!1,o=nt.setTimeout(function(){e=!0,n(ut.error(r()))},t);i.get(function(t){e||(nt.clearTimeout(o),n(t))})}))}})},KC=function(t){return qC(Iy(t))},JC=KC,$C={type:"separator"},QC=function(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:function(){}}},ZC=function(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:undefined},onAction:function(){}}},tO=function(t,n){return o=t,e=H(n,function(t){return t.type===o}),V(e,QC);var e,o},nO=function(t,n){var e=t.toLowerCase();return H(n,function(t){var n=t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.text;return Be(n.toLowerCase(),e)||Be(t.value.toLowerCase(),e)})},eO=function(u,t,a){var n=El.getValue(t),c=n.meta.text!==undefined?n.meta.text:n.value;return a.getLinkInformation().fold(function(){return[]},function(t){var n,e,o,r,i=nO(c,(n=a.getHistory(u),V(n,function(t){return ZC(t,t)})));return"file"===u?(e=[i,nO(c,tO("header",t.targets)),nO(c,it([(r=t,st.from(r.anchorTop).map(function(t){return ZC("<top>",t)}).toArray()),tO("anchor",t.targets),(o=t,st.from(o.anchorBottom).map(function(t){return ZC("<bottom>",t)}).toArray())]))],N(e,function(t,n){return 0===t.length||0===n.length?t.concat(n):t.concat($C,n)},[])):i})},oO=Wr("aria-invalid"),rO=function(r,o,i){var t,n,e,u,a,c=o.shared.providers,s=function(t){var n=El.getValue(t);i.addToHistory(n.value,r.filetype)},l=oy.parts().field({factory:YC,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":oO,type:"url"},minChars:0,responseTime:0,fetch:function(t){var n=eO(r.filetype,t,i),e=Lk(n,Ap.BUBBLE_TO_SANDBOX,o,!1);return Ry(e)},getHotspot:function(t){return h.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(Gy)&&Gy.run(t).get(Z)},typeaheadBehaviours:Ka(it([i.getValidationHandler().map(function(e){return Gy.config({getRoot:function(t){return br(t.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,n){d.getOpt(t).each(function(t){Fr(t.element(),"title",c.translate(n))})}},validator:{validate:function(t){var n=El.getValue(t);return JC(function(o){e({type:r.filetype,url:n.value},function(t){if("invalid"===t.status){var n=ut.error(t.message);o(n)}else{var e=ut.value(t.message);o(e)}})})},validateOnLoad:!1}})}).toArray(),[Dh.config({disabled:function(){return r.disabled||c.isReadOnly()}}),uy.config({}),$m("urlinput-events",it(["file"===r.filetype?[$o(lo(),function(t){Uo(t,fy,{name:r.name})})]:[],[$o(fo(),function(t){Uo(t,fy,{name:r.name}),s(t)}),$o(yo(),function(t){Uo(t,fy,{name:r.name}),s(t)})]]))]])),eventOrder:((t={})[lo()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:Wp(0,0,"normal")},onExecute:function(t,n,e){Uo(n,py,{})},onItemExecute:function(t,n,e,o){s(t),Uo(t,fy,{name:r.name})}}),f=r.label.map(function(t){return ly(t,c)}),d=Zg((n="invalid",e=st.some(oO),void 0===(u="warning")&&(u=n),void 0===a&&(a=n),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+n],innerHtml:np(u,c.icons),attributes:et({title:c.translate(a),"aria-live":"polite"},e.fold(function(){return{}},function(t){return{id:t}}))}})),m=Zg({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=Wr("browser.url.event"),h=Zg({dom:{tag:"div",classes:["tox-control-wrap"]},components:[l,m.asSpec()],behaviours:Ka([Dh.config({disabled:function(){return r.disabled||c.isReadOnly()}})])}),v=Zg(Kk({name:r.name,icon:st.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(t){return jo(t,p)},c,[],["tox-browse-url"]));return oy.sketch({dom:sy([]),components:f.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:it([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:Ka([Dh.config({disabled:function(){return r.disabled||c.isReadOnly()},onDisabled:function(t){oy.getField(t).each(Dh.disable),v.getOpt(t).each(Dh.disable)},onEnabled:function(t){oy.getField(t).each(Dh.enable),v.getOpt(t).each(Dh.enable)}}),nv(),$m("url-input-events",[$o(p,function(o){Lf.getCurrent(o).each(function(n){var t=El.getValue(n),e=et({fieldname:r.name},t);g.each(function(t){t(e).get(function(t){El.setValue(n,t),Uo(o,fy,{name:r.name})})})})})])])})},iO=function(r){return function(n,e,o){return Ft(e,"name").fold(function(){return r(e,o)},function(t){return n.field(t,r(e,o))})}},uO={bar:iO(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:V(e.items,o.interpreter)};var e,o}),collection:iO(function(t,n){return xy(t,n.shared.providers)}),alertbanner:iO(function(t,n){return e=t,o=n.shared.providers,Zb.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+e.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Qg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:np(e.icon,o.icons),attributes:{title:o.translate(e.iconTooltip)}},action:function(t){Uo(t,gy,{name:"alert-banner",value:e.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(e.text)}}]});var e,o}),input:iO(function(t,n){return e=t,o=n.shared.providers,IC({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:e.disabled,classname:"tox-textfield",validation:st.none(),maximized:e.maximized},o);var e,o}),textarea:iO(function(t,n){return e=t,o=n.shared.providers,IC({name:e.name,multiline:!0,label:e.label,inputMode:st.none(),placeholder:e.placeholder,flex:!0,disabled:e.disabled,classname:"tox-textarea",validation:st.none(),maximized:e.maximized},o);var e,o}),label:iO(function(t,n){return e=t,o=n.shared,r={dom:{tag:"label",innerHtml:o.providers.translate(e.label),classes:["tox-label"]}},i=V(e.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:Ka([lS(),Jm.config({}),wS(st.none()),Gm.config({mode:"acyclic"})])};var e,o,r,i}),iframe:(TC=function(t,n){return MS(t,n.shared.providers)},function(t,n,e){var o=zt(n,{source:"dynamic"});return iO(TC)(t,o,e)}),button:iO(function(t,n){return Qk(t,n.shared.providers)}),checkbox:iO(function(t,n){return e=t,o=n.shared.providers,r=El.config({store:{mode:"manual",getValue:function(t){return t.element().dom().checked},setValue:function(t,n){t.element().dom().checked=n}}}),i=function(t){return t.element().dom().click(),st.some(!0)},u=oy.parts().field({factory:{sketch:ct},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:Ka([lS(),Dh.config({disabled:function(){return e.disabled||o.isReadOnly()}}),uy.config({}),eg.config({}),r,Gm.config({mode:"special",onEnter:i,onSpace:i,stopSpaceKeyup:!0}),$m("checkbox-events",[$o(fo(),function(t,n){Uo(t,fy,{name:e.name})})])])}),a=oy.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:o.translate(e.label)},behaviours:Ka([lx.config({})])}),s=Zg({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[(c=function(t){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t],innerHtml:np("checked"===t?"selected":"unselected",o.icons)}}})("checked"),c("unchecked")]}),oy.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[u,s.asSpec(),a],fieldBehaviours:Ka([Dh.config({disabled:function(){return e.disabled||o.isReadOnly()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){oy.getField(t).each(Dh.disable)},onEnabled:function(t){oy.getField(t).each(Dh.enable)}}),nv()])});var e,o,r,i,u,a,c,s}),colorinput:iO(function(t,n){return gx(t,n.shared,n.colorinput)}),colorpicker:iO(function(t){var n=function(t){return"tox-"+t},e=sS(gS,n),r=Zg(e.sketch({dom:{tag:"div",classes:["tox-color-picker-container"],attributes:{role:"presentation"}},onValidHex:function(t){Uo(t,gy,{name:"hex-valid",value:!0})},onInvalidHex:function(t){Uo(t,gy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:Ka([El.config({store:{mode:"manual",getValue:function(t){var n=r.get(t);return Lf.getCurrent(n).bind(function(t){return El.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(n),o=r.get(t);Lf.getCurrent(o).fold(function(){nt.console.log("Can not find form")},function(t){El.setValue(t,{hex:st.from(e[1]).getOr("")}),eS.getField(t,"hex").each(function(t){jo(t,lo())})})}}}),lS()])}}),dropzone:iO(function(t,n){return kS(t,n.shared.providers)}),grid:iO(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+e.columns+"col"]},components:V(e.items,o.interpreter)};var e,o}),selectbox:iO(function(t,n){return e=t,o=n.shared.providers,r=V(e.items,function(t){return{text:o.translate(t.text),value:t.value}}),i=e.label.map(function(t){return ly(t,o)}),u=oy.parts().field({dom:{},selectAttributes:{size:e.size},options:r,factory:FC,selectBehaviours:Ka([Dh.config({disabled:function(){return e.disabled||o.isReadOnly()}}),uy.config({}),$m("selectbox-change",[$o(fo(),function(t,n){Uo(t,fy,{name:e.name})})])])}),a=1<e.size?st.none():st.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:np("chevron-down",o.icons)}}),c={dom:{tag:"div",classes:["tox-selectfield"]},components:it([[u],a.toArray()])},oy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:it([i.toArray(),[c]]),fieldBehaviours:Ka([Dh.config({disabled:function(){return e.disabled||o.isReadOnly()},onDisabled:function(t){oy.getField(t).each(Dh.disable)},onEnabled:function(t){oy.getField(t).each(Dh.enable)}}),nv()])});var e,o,r,i,u,a,c}),sizeinput:iO(function(t,n){return aC(t,n.shared.providers)}),urlinput:iO(function(t,n){return rO(t,n,n.urlinput)}),customeditor:iO(function(e){var o=se(st.none()),n=Zg({dom:{tag:e.tag}}),r=se(st.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:Ka([$m("editor-foo-events",[rr(function(t){n.getOpt(t).each(function(n){var t;t=e,(Object.prototype.hasOwnProperty.call(t,"init")?e.init(n.element().dom()):pS.load(e.scriptId,e.scriptUrl).then(function(t){return t(n.element().dom(),e.settings)})).then(function(n){r.get().each(function(t){n.setValue(t)}),r.set(st.none()),o.set(st.some(n))})})})]),El.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,n){o.get().fold(function(){r.set(st.some(n))},function(t){return t.setValue(n)})}}}),lS()]),components:[n.asSpec()]}}),htmlpanel:iO(function(t){return"presentation"===t.presets?Zb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):Zb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:Ka([uy.config({}),eg.config({})])})}),imagetools:iO(function(t,n){return MC(t,n.shared.providers)}),table:iO(function(t,n){return e=t,o=n.shared.providers,u=function(t){return{dom:{tag:"th",innerHtml:o.translate(t)}}},a=function(t){return{dom:{tag:"td",innerHtml:o.translate(t)}}},c=function(t){return{dom:{tag:"tr"},components:V(t,a)}},{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(i=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:V(i,u)}]}),(r=e.cells,{dom:{tag:"tbody"},components:V(r,c)})],behaviours:Ka([uy.config({}),eg.config({})])};var e,o,r,i,u,a,c}),panel:iO(function(t,n){return o=n,{dom:{tag:"div",classes:(e=t).classes},components:V(e.items,o.shared.interpreter)};var e,o})},aO={field:function(t,n){return n}},cO=function(n,t,e){var o=zt(e,{shared:{interpreter:function(t){return sO(n,t,o)}}});return sO(n,t,o)},sO=function(n,e,o){return Ft(uO,e.type).fold(function(){return nt.console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(t){return t(n,e,o)})},lO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},fO=function(t,n,e){var o=Ac(-12,12,lO),r={maxHeightFunction:_c()};return function(){return e()?{anchor:"node",root:Pi(gr(t())),node:st.from(t()),bubble:o,layouts:{onRtl:function(){return[qg]},onLtr:function(){return[Yg]}},overrides:r}:{anchor:"hotspot",hotspot:n(),bubble:o,layouts:{onRtl:function(){return[Aa]},onLtr:function(){return[Ma]}},overrides:r}}},dO=function(t,n,e){return function(){return e()?{anchor:"node",root:Pi(gr(t())),node:st.from(t()),layouts:{onRtl:function(){return[Kg]},onLtr:function(){return[Kg]}}}:{anchor:"hotspot",hotspot:n(),layouts:{onRtl:function(){return[Va]},onLtr:function(){return[Va]}}}}},mO=function(t,n,e){var o,r,i,u=qh(t),a=function(){return fe.fromDom(t.getBody())},c=function(){return fe.fromDom(t.getContentAreaContainer())},s=function(){return u||!e()};return{inlineDialog:fO(c,n,s),banner:dO(c,n,s),cursor:(r=t,function(){return{anchor:"selection",root:i(),getSelection:function(){var t=r.selection.getRng();return st.some(Gc.range(fe.fromDom(t.startContainer),t.startOffset,fe.fromDom(t.endContainer),t.endOffset))}}}),node:(o=i=a,function(t){return{anchor:"node",root:o(),node:t}})}},gO=function(t){return{colorPicker:function(t,n){lb(r)(t,n)},hasCustomColors:function(){return Zv(o)},getColors:function(){return tb(e)},getColorCols:(n=e=o=r=t,function(){return ob(n)})};var n,e,o,r},pO=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],hO=function(t){return N(t,function(t,n){if(It(n,"items")){var e=hO(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(It(n,"inline")||It(n,"block")||It(n,"selector")){var o="custom-"+n.title.toLowerCase();return{customFormats:t.customFormats.concat([{name:o,format:n}]),formats:t.formats.concat([{title:n.title,format:o,icon:n.icon}])}}return et(et({},t),{formats:t.formats.concat(n)})},{customFormats:[],formats:[]})},vO=function(i){return t=i,st.from(t.getParam("style_formats")).filter(v).map(function(t){var n,e,o,r=(n=i,e=hO(t),o=function(t){rt(t,function(t){n.formatter.has(t.name)||n.formatter.register(t.name,t.format)})},n.formatter?o(e.customFormats):n.on("init",function(){o(e.customFormats)}),e.formats);return i.getParam("style_formats_merge",!1,"boolean")?pO.concat(r):r}).getOr(pO);var t},bO=function(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return zt(t,o)},yO=function(a,t,c,s){var l=function(t){return V(t,function(t){var n,e,o,r,i=Ct(t);if(Rt(t,"items")){var u=l(t.items);return zt(zt(t,{type:"submenu"}),{getStyleItems:function(){return u}})}return Rt(t,"format")?bO(t,c,s):1===i.length&&M(i,"title")?zt(t,{type:"separator"}):(e=Wr((n=t).title),o={type:"formatter",format:e,isSelected:c(e),getStylePreview:s(e)},r=zt(n,o),a.formatter.register(e,r),r)})};return l(t)},xO=vC.trim,wO=function(n){return function(t){if(t&&1===t.nodeType){if(t.contentEditable===n)return!0;if(t.getAttribute("data-mce-contenteditable")===n)return!0}return!1}},SO=wO("true"),kO=wO("false"),CO=function(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}},OO=function(t){return t.innerText||t.textContent},_O=function(t){return(n=t)&&"A"===n.nodeName&&(n.id||n.name)!==undefined&&EO(t);var n},TO=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},EO=function(t){return function(t){for(;t=t.parentNode;){var n=t.contentEditable;if(n&&"inherit"!==n)return SO(t)}return!1}(t)&&!kO(t)},BO=function(t){return TO(t)&&EO(t)},DO=function(t){var n,e,o=(n=t).id?n.id:Wr("h");return CO("header",OO(t),"#"+o,TO(e=t)?parseInt(e.nodeName.substr(1),10):0,function(){t.id=o})},AO=function(t){var n=t.id||t.name,e=OO(t);return CO("anchor",e||"#"+n,"#"+n,0,Z)},MO=function(t){var n,e;return n="h1,h2,h3,h4,h5,h6,a:not([href])",e=t,V(os(fe.fromDom(e),n),function(t){return t.dom()})},FO=function(t){return 0<xO(t.title).length},IO=function(t){var n=MO(t);return H(V(H(n,BO),DO).concat(V(H(n,_O),AO)),FO)},RO="tinymce-url-history",VO=function(t){return w(t)&&/^https?/.test(t)},PO=function(t){return S(t)&&At(t,function(t){return!(v(n=t)&&n.length<=5&&W(n,VO));var n}).isNone()},HO=function(){var t,n=qv.getItem(RO);if(null===n)return{};try{t=JSON.parse(n)}catch(e){if(e instanceof SyntaxError)return nt.console.log("Local storage "+RO+" was not valid JSON",e),{};throw e}return PO(t)?t:(nt.console.log("Local storage "+RO+" was not valid format",t),{})},zO=function(t){var n=HO();return Object.prototype.hasOwnProperty.call(n,t)?n[t]:[]},NO=function(n,t){if(VO(n)){var e=HO(),o=Object.prototype.hasOwnProperty.call(e,t)?e[t]:[],r=H(o,function(t){return t!==n});e[t]=[n].concat(r).slice(0,5),function(t){if(!PO(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));qv.setItem(RO,JSON.stringify(t))}(e)}},LO=function(t){return!!t},jO=function(t){return Tt(vC.makeMap(t,/[, ]/),LO)},UO=function(t){return st.from(t.getParam("file_picker_callback")).filter(_)},WO=function(t,n){var e,o,r,i,u=(e=t,o=st.some(e.getParam("file_picker_types")).filter(LO),r=st.some(e.getParam("file_browser_callback_types")).filter(LO),i=o.or(r).map(jO),UO(e).fold(function(){return!1},function(t){return i.fold(function(){return!0},function(t){return 0<Ct(t).length&&t})}));return k(u)?u?UO(t):st.none():u[n]?UO(t):st.none()},GO=function(t){return st.from((e=(n=t).getParam("file_picker_validator_handler",undefined,"function"))===undefined?n.getParam("filepicker_validator_handler",undefined,"function"):e);var n,e},XO=function(n){return{getHistory:zO,addToHistory:NO,getLinkInformation:function(){return!1===(t=n).getParam("typeahead_urls")?st.none():st.some({targets:IO(t.getBody()),anchorTop:t.getParam("anchor_top","#top","string"),anchorBottom:t.getParam("anchor_bottom","#bottom","string")});var t},getValidationHandler:function(){return GO(n)},getUrlPicker:function(t){return WO(r=n,i=t).map(function(o){return function(n){return Iy(function(e){var t=et({filetype:i,fieldname:n.fieldname},st.from(n.meta).getOr({}));o.call(r,function(t,n){if(!w(t))throw new Error("Expected value to be string");if(n!==undefined&&!S(n))throw new Error("Expected meta to be a object");e({value:t,meta:n})},n.value,t)})}});var r,i}}},YO=function(t,n,e){var o,r,i=se(!1),u={isPositionedAtTop:function(){return"top"===o.get()},getDockingMode:(o=se(Xh(n)?"bottom":"top")).get,setDockingMode:o.set},a={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:hh.translate,isReadOnly:function(){return n.mode.isReadOnly()}},interpreter:function(t){return sO(aO,t,a)},anchors:mO(n,e,u.isPositionedAtTop),header:u,getSink:function(){return ut.value(t)}},urlinput:XO(n),styleselect:function(o){var r=function(t){return function(){return o.formatter.match(t)}},i=function(n){return function(){var t=o.formatter.get(n);return t!==undefined?st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(n))}):st.none()}},u=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,u):[t.format]},a=se([]),c=se([]),e=se([]),s=se([]),l=se(!1);o.on("PreInit",function(t){var n=vO(o),e=yO(o,n,r,i);a.set(e),c.set(U(e,u))}),o.on("addStyleModifications",function(t){var n=yO(o,t.items,r,i);e.set(n),l.set(t.replace),s.set(U(n,u))});return{getData:function(){var t=l.get()?[]:a.get(),n=e.get();return t.concat(n)},getFlattenedKeys:function(){var t=l.get()?[]:c.get(),n=s.get();return t.concat(n)}}}(n),colorinput:gO(n),dialog:{isDraggableModal:(r=n,function(){return r.getParam("draggable_modal",!1,"boolean")})},isContextMenuOpen:function(){return i.get()},setContextMenuState:function(t){return i.set(t)}};return a},qO=at(function(t,n){var e,o,r;e=t,o=Math.floor(n),r=vu.max(e,o,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]),Ni(e,"max-width",r+"px")}),KO="contexttoolbar-hide",JO=at([Nn("items"),ia(["itemSelector"]),Bl("tgroupBehaviours",[Gm])]),$O=at([cf({name:"items",unit:"item"})]),QO=Vf({name:"ToolbarGroup",configFields:JO(),partFields:$O(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:Al(t.tgroupBehaviours,[Gm.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),ZO=at([Nn("dom"),te("shell",!0),Bl("toolbarBehaviours",[Jm])]),t_=at([af({name:"groups",overrides:function(){return{behaviours:Ka([Jm.config({})])}}})]),n_=Vf({name:"Toolbar",configFields:ZO(),partFields:t_(),factory:function(n,t,e,o){var r=function(t){return n.shell?st.some(t):yf(t,n,"groups")},i=n.shell?{behaviours:[Jm.config({})],components:[]}:{behaviours:[],components:t};return{uid:n.uid,dom:n.dom,components:i.components,behaviours:Al(n.toolbarBehaviours,i.behaviours),apis:{setGroups:function(t,n){r(t).fold(function(){throw nt.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){Jm.set(t,n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),e_=function(t,n,e){return{within:at(t),extra:at(n),withinWidth:at(e)}},o_=function(t,n,o){var e,r=(e=function(t,n){var e=o(t);return st.some({element:at(t),start:at(n),finish:at(n+e),width:at(e)})},N(t,function(n,t){return e(t,n.len).fold(at(n),function(t){return{len:t.finish(),list:n.list.concat([t])}})},{len:0,list:[]}).list),i=H(r,function(t){return t.finish()<=n}),u=z(i,function(t,n){return t+n.width()},0),a=r.slice(i.length);return{within:at(i),extra:at(a),withinWidth:at(u)}},r_=function(t){return V(t,function(t){return t.element()})},i_=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m=(0===(r=o_(n,t,e)).extra().length?st.some(r):st.none()).getOrThunk(function(){return o_(n,t-e(o),e)}),g=m.within(),p=m.extra(),h=m.withinWidth();return 1===p.length&&p[0].width()<=e(o)?(l=p,f=h,d=r_(g.concat(l)),e_(d,[],f)):1<=p.length?(u=p,a=o,c=h,s=r_(g).concat([a]),e_(s,r_(u),c)):(i=h,e_(r_(g),[],i))},u_=function(t,n){var e=V(n,function(t){return au(t)});n_.setGroups(t,e)},a_=function(t,n,e){var o=xf(t,n,"primary"),r=qy.getCoupled(t,"overflowGroup");Ni(o.element(),"visibility","hidden");var i=n.builtGroups.get().concat([r]),u=$(i,function(n){return ac(n.element()).bind(function(t){return n.getSystem().getByDom(t).toOption()})});e([]),u_(o,i);var a=bu(o.element()),c=i_(a,n.builtGroups.get(),function(t){return bu(t.element())},r);0===c.extra().length?(Jm.remove(o,r),e([])):(u_(o,c.within()),e(c.extra())),Yi(o.element(),"visibility"),qi(o.element()),u.each(eg.focus)},c_=at([Bl("splitToolbarBehaviours",[qy]),ce("builtGroups",function(){return se([])})]),s_=at([ia(["overflowToggledClass"]),Qn("getOverflowBounds"),Nn("lazySink"),ce("overflowGroups",function(){return se([])})].concat(c_())),l_=at([rf({factory:n_,schema:ZO(),name:"primary"}),uf({schema:ZO(),name:"overflow"}),uf({name:"overflow-button"}),uf({name:"overflow-group"})]),f_=at([ia(["toggledClass"]),Nn("lazySink"),Wn("fetch"),Qn("getBounds"),Zn("fireDismissalEventInstead",[te("event",Io())]),Pc()]),d_=at([uf({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:Ka([gg.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),uf({factory:n_,schema:ZO(),name:"toolbar",overrides:function(n){return{toolbarBehaviours:Ka([Gm.config({mode:"cyclic",onEscape:function(t){return yf(t,n,"button").each(eg.focus),st.none()}})])}}})]),m_=function(t,n){var e=qy.getCoupled(t,"toolbarSandbox");cl.isOpen(e)?cl.close(e):cl.open(e,n.toolbar())},g_=function(t,n,e,o){var r=e.getBounds.map(function(t){return t()}),i=e.lazySink(t).getOrDie();Ns.positionWithinBounds(i,{anchor:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:qO()}},n,r)},p_=function(t,n,e,o,r){n_.setGroups(n,r),g_(t,n,e,o),gg.on(t)},h_=Vf({name:"FloatingToolbarButton",factory:function(u,t,a,n){return et(et({},Qg.sketch(et(et({},n.button()),{action:function(t){m_(t,n)},buttonBehaviours:Fl({dump:n.button().buttonBehaviours},[qy.config({others:{toolbarSandbox:function(t){return o=t,e=a,r=u,{dom:{tag:"div",attributes:{id:(i=Uu()).id}},behaviours:Ka([Gm.config({mode:"special",onEscape:function(t){return cl.close(t),st.some(!0)}}),cl.config({onOpen:function(t,n){r.fetch().get(function(t){p_(o,n,r,e.layouts,t),i.link(o.element()),Gm.focusIn(n)})},onClose:function(){gg.off(o),eg.focus(o),i.unlink(o.element())},isPartOf:function(t,n,e){return Gu(n,e)||Gu(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),oc.config({channels:et(et({},ml(et({isExtraPart:c},r.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),pl({doReposition:function(){cl.getState(qy.getCoupled(o,"toolbarSandbox")).each(function(t){g_(o,t,r,e.layouts)})}}))})])};var o,e,r,i}}})])}))),{apis:{setGroups:function(n,e){cl.getState(qy.getCoupled(n,"toolbarSandbox")).each(function(t){p_(n,t,u,a.layouts,e)})},reposition:function(n){cl.getState(qy.getCoupled(n,"toolbarSandbox")).each(function(t){g_(n,t,u,a.layouts)})},toggle:function(t){m_(t,n)},getToolbar:function(t){return cl.getState(qy.getCoupled(t,"toolbarSandbox"))}}})},configFields:f_(),partFields:d_(),apis:{setGroups:function(t,n,e){t.setGroups(n,e)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getToolbar:function(t,n){return t.getToolbar(n)}}}),v_=function(t){return V(t,function(t){return au(t)})},b_=function(t,e,o){a_(t,o,function(n){o.overflowGroups.set(n),e.getOpt(t).each(function(t){h_.setGroups(t,v_(n))})})},y_=Vf({name:"SplitFloatingToolbar",configFields:s_(),partFields:l_(),factory:function(e,t,n,o){var r=Zg(h_.sketch({fetch:function(){return Iy(function(t){t(v_(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[Ma,Aa]},onRtl:function(){return[Aa,Ma]},onBottomLtr:function(){return[Ia,Fa]},onBottomRtl:function(){return[Fa,Ia]}},getBounds:n.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Al(e.splitToolbarBehaviours,[qy.config({others:{overflowGroup:function(){return QO.sketch(et(et({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(t,n){e.builtGroups.set(V(n,t.getSystem().build)),b_(t,r,e)},refresh:function(t){return b_(t,r,e)},toggle:function(t){r.getOpt(t).each(function(t){h_.toggle(t)})},reposition:function(t){r.getOpt(t).each(function(t){h_.reposition(t)})},getOverflow:function(t){return r.getOpt(t).bind(function(t){return h_.getToolbar(t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getOverflow:function(t,n){return t.getOverflow(n)}}}),x_=function(n,t){return t.getAnimationRoot.fold(function(){return n.element()},function(t){return t(n)})},w_=function(t){return t.dimension.property},S_=function(t,n){return t.dimension.getDimension(n)},k_=function(t,n){var e=x_(t,n);Ti(e,[n.shrinkingClass,n.growingClass])},C_=function(t,n){Ci(t.element(),n.openClass),Si(t.element(),n.closedClass),Ni(t.element(),w_(n),"0px"),qi(t.element())},O_=function(t,n){Ci(t.element(),n.closedClass),Si(t.element(),n.openClass),Yi(t.element(),w_(n))},__=function(t,n,e,o){e.setCollapsed(),Ni(t.element(),w_(n),S_(n,t.element())),qi(t.element()),k_(t,n),C_(t,n),n.onStartShrink(t),n.onShrunk(t)},T_=function(t,n,e,o){var r=o.getOrThunk(function(){return S_(n,t.element())});e.setCollapsed(),Ni(t.element(),w_(n),r),qi(t.element());var i=x_(t,n);Ci(i,n.growingClass),Si(i,n.shrinkingClass),C_(t,n),n.onStartShrink(t)},E_=function(t,n,e){var o=S_(n,t.element());("0px"===o?__:T_)(t,n,e,st.some(o))},B_=function(t,n,e){var o=x_(t,n),r=Oi(o,n.shrinkingClass),i=S_(n,t.element());O_(t,n);var u=S_(n,t.element());(r?function(){Ni(t.element(),w_(n),i),qi(t.element())}:function(){C_(t,n)})(),Ci(o,n.shrinkingClass),Si(o,n.growingClass),O_(t,n),Ni(t.element(),w_(n),u),e.setExpanded(),n.onStartGrow(t)},D_=function(t,n,e){var o=x_(t,n);return!0===Oi(o,n.growingClass)},A_=function(t,n,e){var o=x_(t,n);return!0===Oi(o,n.shrinkingClass)},M_=/* */Object.freeze({__proto__:null,refresh:function(t,n,e){if(e.isExpanded()){Yi(t.element(),w_(n));var o=S_(n,t.element());Ni(t.element(),w_(n),o)}},grow:function(t,n,e){e.isExpanded()||B_(t,n,e)},shrink:function(t,n,e){e.isExpanded()&&E_(t,n,e)},immediateShrink:function(t,n,e){e.isExpanded()&&__(t,n,e,st.none())},hasGrown:function(t,n,e){return e.isExpanded()},hasShrunk:function(t,n,e){return e.isCollapsed()},isGrowing:D_,isShrinking:A_,isTransitioning:function(t,n,e){return!0===D_(t,n)||!0===A_(t,n)},toggleGrow:function(t,n,e){(e.isExpanded()?E_:B_)(t,n,e)},disableTransitions:k_}),F_=/* */Object.freeze({__proto__:null,exhibit:function(t,n,e){var o=n.expanded;return si(o?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:Jt(n.dimension.property,"0px")})},events:function(e,o){return qo([or(go(),function(t,n){n.event().raw().propertyName===e.dimension.property&&(k_(t,e),o.isExpanded()&&Yi(t.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(t))})])}}),I_=[Nn("closedClass"),Nn("openClass"),Nn("shrinkingClass"),Nn("growingClass"),qn("getAnimationRoot"),aa("onShrunk"),aa("onStartShrink"),aa("onGrown"),aa("onStartGrow"),te("expanded",!1),Ln("dimension",Dn("property",{width:[fa("property","width"),fa("getDimension",function(t){return bu(t)+"px"})],height:[fa("property","height"),fa("getDimension",function(t){return lu(t)+"px"})]}))],R_=$a({fields:I_,name:"sliding",active:F_,apis:M_,state:/* */Object.freeze({__proto__:null,init:function(t){var n=se(t.expanded);return ai({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:g(n.set,!1),setExpanded:g(n.set,!0),readState:function(){return"expanded: "+n.get()}})}})}),V_=at([ia(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),aa("onOpened"),aa("onClosed")].concat(c_())),P_=at([rf({factory:n_,schema:ZO(),name:"primary"}),rf({factory:n_,schema:ZO(),name:"overflow",overrides:function(n){return{toolbarBehaviours:Ka([R_.config({dimension:{property:"height"},closedClass:n.markers.closedClass,openClass:n.markers.openClass,shrinkingClass:n.markers.shrinkingClass,growingClass:n.markers.growingClass,onShrunk:function(t){yf(t,n,"overflow-button").each(function(t){gg.off(t),eg.focus(t)}),n.onClosed(t)},onGrown:function(t){Gm.focusIn(t),n.onOpened(t)},onStartGrow:function(t){yf(t,n,"overflow-button").each(gg.on)}}),Gm.config({mode:"acyclic",onEscape:function(t){return yf(t,n,"overflow-button").each(eg.focus),st.some(!0)}})])}}}),uf({name:"overflow-button",overrides:function(t){return{buttonBehaviours:Ka([gg.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),uf({name:"overflow-group"})]),H_=function(t,n){yf(t,n,"overflow").each(function(e){a_(t,n,function(t){var n=V(t,function(t){return au(t)});n_.setGroups(e,n)}),yf(t,n,"overflow-button").each(function(t){R_.hasGrown(e)&&gg.on(t)}),R_.refresh(e)})},z_=Vf({name:"SplitSlidingToolbar",configFields:V_(),partFields:P_(),factory:function(o,t,n,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:t,behaviours:Al(o.splitToolbarBehaviours,[qy.config({others:{overflowGroup:function(n){return QO.sketch(et(et({},e["overflow-group"]()),{items:[Qg.sketch(et(et({},e["overflow-button"]()),{action:function(t){jo(n,r)}}))]}))}}}),$m("toolbar-toggle-events",[$o(r,function(n){yf(n,o,"overflow").each(function(t){H_(n,o),R_.toggleGrow(t)})})])]),apis:{setGroups:function(t,n){var e;e=V(n,t.getSystem().build),o.builtGroups.set(e),H_(t,o)},refresh:function(t){return H_(t,o)},toggle:function(t){var n,e;yf(n=t,e=o,"overflow").each(function(t){H_(n,e),R_.toggleGrow(t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},toggle:function(t,n){t.toggle(n)}}}),N_=at(Wr("toolbar-height-change")),L_=function(t){var n=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:et({tag:"div",classes:["tox-toolbar__group"]},n),components:[QO.parts().items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:Ka([uy.config({}),eg.config({})])}},j_=function(t){return QO.sketch(L_(t))},U_=function(e,t){var n=rr(function(t){var n=V(e.initGroups,j_);n_.setGroups(t,n)});return Ka([iv(e.providers.isReadOnly),nv(),Gm.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),$m("toolbar-events",[n])])},W_=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":L_({title:st.none(),items:[]}),"overflow-button":Xk({name:"more",icon:st.some("more-drawer"),disabled:!1,tooltip:st.some("More..."),primary:!1,borderless:!1},st.none(),t.providers)},splitToolbarBehaviours:U_(t,n)}},G_=function(i){var t=W_(i),n=y_.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return y_.sketch(et(et({},t),{lazySink:i.getSink,getOverflowBounds:function(){var t=i.moreDrawerData.lazyHeader().element(),n=Mu(t),e=hr(t),o=Mu(e),r=Math.max(e.dom().scrollHeight,o.height);return Du(n.x+4,o.y,n.width-8,r)},parts:et(et({},t.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[n],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))},X_=function(t){var n=z_.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=z_.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=W_(t);return z_.sketch(et(et({},o),{components:[n,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([N_()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([N_()],{type:"closed"})}}))},Y_=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return n_.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===Bp.scrolling?["tox-toolbar--scrolling"]:[])},components:[n_.parts().groups({})],toolbarBehaviours:U_(t,n)})},q_=[ie("disabled",!1),$n("tooltip"),$n("icon"),$n("text"),ue("onSetup",function(){return Z})],K_=fn([jn("type"),Wn("onAction")].concat(q_)),J_=function(t){return On("toolbarbutton",K_,t)},$_=[$n("text"),$n("tooltip"),$n("icon"),Wn("fetch"),ue("onSetup",function(){return Z})],Q_=fn(b([jn("type")],$_)),Z_=function(t){return On("menubutton",Q_,t)},tT=fn([jn("type"),$n("tooltip"),$n("icon"),$n("text"),Qn("select"),Wn("fetch"),ue("onSetup",function(){return Z}),re("presets","normal",["normal","color","listpreview"]),te("columns",1),Wn("onAction"),Wn("onItemAction")]),nT=[ie("active",!1)].concat(q_),eT=fn(nT.concat([jn("type"),Wn("onAction")])),oT=function(t){return On("ToggleButton",eT,t)},rT=fn([jn("type"),Ln("items",(EC=[wn([jn("name"),Yn("items",In)]),In],{extract:function(t,n,e){for(var o=[],r=0,i=EC;r<i.length;r++){var u=i[r].extract(t,n,e);if(u.stype===a.Value)return u;o.push(u)}return nn(o)},toString:function(){return"oneOf("+V(EC,function(t){return t.toString()}).join(", ")+")"}}))].concat(q_)),iT=[ue("predicate",function(){return!1}),re("scope","node",["node","editor"]),re("position","selection",["node","selection","line"])],uT=q_.concat([te("type","contextformbutton"),te("primary",!1),Wn("onAction"),ce("original",ct)]),aT=nT.concat([te("type","contextformbutton"),te("primary",!1),Wn("onAction"),ce("original",ct)]),cT=q_.concat([te("type","contextformbutton")]),sT=nT.concat([te("type","contextformtogglebutton")]),lT=Dn("type",{contextformbutton:uT,contextformtogglebutton:aT}),fT=fn([te("type","contextform"),ue("initValue",function(){return""}),$n("label"),Yn("commands",lT),Kn("launch",Dn("type",{contextformbutton:cT,contextformtogglebutton:sT}))].concat(iT)),dT=fn([te("type","contexttoolbar"),jn("items")].concat(iT)),mT=/* */Object.freeze({__proto__:null,getState:function(t,n,e){return e}}),gT=/* */Object.freeze({__proto__:null,events:function(i,u){var r=function(o,r){i.updateState.each(function(t){var n=t(o,r);u.set(n)}),i.renderComponents.each(function(t){var n=t(r,u.get()),e=V(n,o.getSystem().build);Ws(o,e)})};return qo([$o(xo(),function(t,n){var e=n,o=i.channel;M(e.channels(),o)&&r(t,e.data())}),rr(function(n,t){i.initialData.each(function(t){r(n,t)})})])}}),pT=/* */Object.freeze({__proto__:null,init:function(){var n=se(st.none());return{readState:function(){return n.get().fold(function(){return"none"},function(t){return t})},get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(st.none())}}}}),hT=[Nn("channel"),qn("renderComponents"),qn("updateState"),qn("initialData")],vT=$a({fields:hT,name:"reflecting",active:gT,apis:mT,state:pT}),bT=at([Nn("toggleClass"),Nn("fetch"),sa("onExecute"),te("getHotspot",st.some),te("getAnchorOverrides",at({})),Pc(),sa("onItemExecute"),qn("lazySink"),Nn("dom"),aa("onOpen"),Bl("splitDropdownBehaviours",[qy,Gm,eg]),te("matchWidth",!1),te("useMinWidth",!1),te("eventOrder",{}),qn("role")].concat(ux())),yT=rf({factory:Qg,schema:[Nn("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:Ka([eg.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(n.uid).each(Wo)},buttonBehaviours:Ka([gg.config({toggleOnExecute:!1,toggleClass:n.toggleClass})])}}}),xT=rf({factory:Qg,schema:[Nn("dom")],name:"button",defaults:function(){return{buttonBehaviours:Ka([eg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(e.uid).each(function(t){e.onExecute(t,n)})}}}}),wT=at([yT,xT,af({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Nn("text")],name:"aria-descriptor"}),uf({schema:[ra()],name:"menu",defaults:function(o){return{onExecute:function(n,e){n.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,n,e)})}}}}),Jy()]),ST=Vf({name:"SplitDropdown",configFields:bT(),partFields:wT(),factory:function(o,t,n,e){var r=function(t){Lf.getCurrent(t).each(function(t){Qf.highlightFirst(t),Gm.focusIn(t)})},i=function(t){tx(o,function(t){return t},t,e,r,Ty.HighlightFirst).get(Z)},u=function(t){var n=xf(t,o,"button");return Wo(n),st.some(!0)},a=et(et({},qo([rr(function(e,t){yf(e,o,"aria-descriptor").each(function(t){var n=Wr("aria");Fr(t.element(),"id",n),Fr(e.element(),"aria-describedby",n)})})])),hg(st.some(i))),c={repositionMenus:function(t){gg.isOn(t)&&ix(t)}};return{uid:o.uid,dom:o.dom,components:t,apis:c,eventOrder:et(et({},o.eventOrder),{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:Al(o.splitDropdownBehaviours,[qy.config({others:{sandbox:function(t){var n=xf(t,o,"arrow");return rx(o,t,{onOpen:function(){gg.on(n),gg.on(t)},onClose:function(){gg.off(n),gg.off(t)}})}}}),Gm.config({mode:"special",onSpace:u,onEnter:u,onDown:function(t){return i(t),st.some(!0)}}),eg.config({}),gg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(t,n){return t.repositionMenus(n)}}}),kT=function(n){return{isDisabled:function(){return Dh.isDisabled(n)},setDisabled:function(t){return Dh.set(n,t)}}},CT=function(n){return{setActive:function(t){gg.set(n,t)},isActive:function(){return gg.isOn(n)},isDisabled:function(){return Dh.isDisabled(n)},setDisabled:function(t){return Dh.set(n,t)}}},OT=function(t,n){return t.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})},_T=Wr("focus-button"),TT=["checklist","ordered-list"],ET=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],BT=function(n,e,t,o,r,i){var u,a=function(t){return hh.isRtl()&&M(TT,t)?t+"-rtl":t},c=hh.isRtl()&&n.exists(function(t){return M(ET,t)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:OT(t,i)},components:lv([n.map(function(t){return Ek(a(t),i.icons)}),e.map(function(t){return Dk(t,"tox-tbtn",i)})]),eventOrder:((u={})[no()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:Ka([iv(i.isReadOnly),nv(),$m("common-button-display-events",[$o(no(),function(t,n){n.event().prevent(),jo(t,_T)})])].concat(o.map(function(t){return vT.config({channel:t,initialData:{icon:n,text:e},renderComponents:function(t,n){return lv([t.icon.map(function(t){return Ek(a(t),i.icons)}),t.text.map(function(t){return Dk(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}},DT=function(t,n,e){var o,r=se(Z),i=BT(t.icon,t.text,t.tooltip,st.none(),st.none(),e);return Qg.sketch({dom:i.dom,components:i.components,eventOrder:Mk,buttonBehaviours:Ka([$m("toolbar-button-events",[(o={onAction:t.onAction,getApi:n.getApi},ar(function(n,t){uv(o,n)(function(t){Uo(n,Ak,{buttonApi:t}),o.onAction(t)})})),av(n,r),cv(n,r)]),iv(function(){return t.disabled||e.isReadOnly()}),nv()].concat(n.toolbarButtonBehaviours))})},AT=function(t,n,e){return DT(t,{toolbarButtonBehaviours:[].concat(0<e.length?[$m("toolbarButtonWith",e)]:[]),getApi:kT,onSetup:t.onSetup},n)},MT=function(t,n,e){return zt(DT(t,{toolbarButtonBehaviours:[Jm.config({}),gg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[$m("toolbarToggleButtonWith",e)]:[]),getApi:CT,onSetup:t.onSetup},n))},FT=function(n,t){var e,o,r,i,u=Wr("channel-update-split-dropdown-display"),a=function(e){return{isDisabled:function(){return Dh.isDisabled(e)},setDisabled:function(t){return Dh.set(e,t)},setIconFill:function(t,n){Lu(e.element(),'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Fr(t,"fill",n)})},setIconStroke:function(t,n){Lu(e.element(),'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Fr(t,"stroke",n)})},setActive:function(n){Fr(e.element(),"aria-pressed",n),Lu(e.element(),"span").each(function(t){e.getSystem().getByDom(t).each(function(t){return gg.set(t,n)})})},isActive:function(){return Lu(e.element(),"span").exists(function(t){return e.getSystem().getByDom(t).exists(gg.isOn)})}}},c=se(Z),s={getApi:a,onSetup:n.onSetup};return ST.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:et({"aria-pressed":!1},OT(n.tooltip,t.providers))},onExecute:function(t){n.onAction(a(t))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:Ka([rv(t.providers.isReadOnly),nv(),$m("split-dropdown-events",[$o(_T,eg.focus),av(s,c),cv(s,c)]),lx.config({})]),eventOrder:((e={})[Mo()]=["alloy.base.behaviour","split-dropdown-events"],e),toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:(o=a,r=n,i=t.providers,function(n){return Iy(function(t){return r.fetch(t)}).map(function(t){return st.from(_b(zt(fb(Wr("menu-value"),t,function(t){r.onItemAction(o(n),t)},r.columns,r.presets,Ap.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),i),{movement:mb(r.columns,r.presets),menuBehaviours:uh("auto"!==r.columns?[]:[rr(function(o,t){rh(o,4,Np(r.presets)).each(function(t){var n=t.numRows,e=t.numColumns;Gm.setGridSize(o,n,e)})})])})))})}),parts:{menu:Wp(0,n.columns,n.presets)},components:[ST.parts().button(BT(n.icon,n.text,st.none(),st.some(u),st.some([gg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),ST.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:np("chevron-down",t.providers.icons)},buttonBehaviours:Ka([rv(t.providers.isReadOnly),nv()])}),ST.parts()["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})},IT=function(i,u){return $o(Ak,function(t,n){var e,o=i.get(t),r=(e=o,{hide:function(){return jo(e,Oo())},getValue:function(){return El.getValue(e)}});u.onAction(r,n.event().buttonApi())})},RT=function(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===n.type?(s=t,f=p,(d=(l=n).original).primary,m=y(d,["primary"]),g=_n(oT(et(et({},m),{type:"togglebutton",onAction:function(){}}))),MT(g,f.backstage.shared.providers,[IT(s,l)])):(o=t,i=p,(u=(r=n).original).primary,a=y(u,["primary"]),c=_n(J_(et(et({},a),{type:"button",onAction:function(){}}))),AT(c,i.backstage.shared.providers,[IT(o,r)]))},VT=function(t,n){var e,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=Zg(Cy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:Ka([Gm.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return Wo(t),!0})},onLeft:function(t,n){return n.cut(),st.none()},onRight:function(t,n){return n.cut(),st.none()}})])})),c=(e=a,o=t.commands,r=n,i=V(o,function(t){return Zg(RT(e,t,r))}),{asSpecs:function(){return V(i,function(t){return t.asSpec()})},findPrimary:function(e){return $(o,function(t,n){return t.primary?st.from(i[n]).bind(function(t){return t.getOpt(e)}).filter(x(Dh.isDisabled)):st.none()})}});return[{title:st.none(),items:[a.asSpec()]},{title:st.none(),items:c.asSpecs()}]},PT=VT,HT=function(t,n){var e,o,r,i,u,a=_u(nt.window),c=Au(fe.fromDom(t.getContentAreaContainer())),s=Hh(t)||zh(t)||Lh(t),l=(e=c,o=a,r=Math.max(o.x,e.x),i=e.right-r,u=o.width-(r-o.x),{x:r,width:Math.min(i,u)}),f=l.x,d=l.width;if(t.inline&&!s)return Du(f,a.y,d,a.height);var m=function(t,n,e,o){var r=fe.fromDom(t.getContainer()),i=Lu(r,".tox-editor-header").getOr(r),u=Au(i),a=u.y>=n.bottom,c=o&&!a;if(t.inline&&c)return{y:Math.max(u.bottom,e.y),bottom:e.bottom};if(t.inline&&!c)return{y:e.y,bottom:Math.min(u.y,e.bottom)};var s=Au(r);return c?{y:Math.max(u.bottom,e.y),bottom:Math.min(s.bottom,e.bottom)}:{y:Math.max(s.y,e.y),bottom:Math.min(u.y,e.bottom)}}(t,c,a,n.header.isPositionedAtTop()),g=m.y,p=m.bottom;return Du(f,g,d,p-g)},zT=function(n,t){var e=H(t,function(t){return t.predicate(n.dom())}),o=P(e,function(t){return"contexttoolbar"===t.type});return{contextToolbars:o.pass,contextForms:o.fail}},NT=function(t,n,e){var o=zT(t,n);if(0<o.contextForms.length)return st.some({elem:t,toolbars:[o.contextForms[0]]});var r=zT(t,e);if(0<r.contextForms.length)return st.some({elem:t,toolbars:[r.contextForms[0]]});if(0<o.contextToolbars.length||0<r.contextToolbars.length){var i=function(t){if(t.length<=1)return t;var n=function(n){return F(t,function(t){return t.position===n})},e=function(n){return H(t,function(t){return t.position===n})},o=n("selection"),r=n("node");if(o||r){if(r&&o){var i=e("node"),u=V(e("selection"),function(t){return et(et({},t),{position:"node"})});return i.concat(u)}return e(o?"selection":"node")}return e("line")}(o.contextToolbars.concat(r.contextToolbars));return st.some({elem:t,toolbars:i})}return st.none()},LT=function(t,n,i){return t(n)?st.none():Xe(n,function(t){var n=zT(t,i.inNodeScope),e=n.contextToolbars,o=n.contextForms,r=0<o.length?o:function(t){if(t.length<=1)return t;var n=function(n){return L(t,function(t){return t.position===n})};return n("selection").orThunk(function(){return n("node")}).orThunk(function(){return n("line")}).map(function(t){return t.position}).fold(function(){return[]},function(n){return H(t,function(t){return t.position===n})})}(e);return 0<r.length?st.some({elem:t,toolbars:r}):st.none()},t)},jT=function(e,r){var t={},i=[],u=[],a={},c={},o=function(n,e){var o=_n(On("ContextForm",fT,e));(t[n]=o).launch.map(function(t){a["form:"+n]=et(et({},e.launch),{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?u.push(o):i.push(o),c[n]=o},s=function(n,e){On("ContextToolbar",dT,e).each(function(t){"editor"===e.scope?u.push(t):i.push(t),c[n]=t})},n=Ct(e);return rt(n,function(t){var n=e[t];"contextform"===n.type?o(t,n):"contexttoolbar"===n.type&&s(t,n)}),{forms:t,inNodeScope:i,inEditorScope:u,lookupTable:c,formNavigators:a}},UT=Wr("forward-slide"),WT=Wr("backward-slide"),GT=Wr("change-slide-event"),XT="tox-pop--resizing",YT=function(t,n,e){var u,a,r,c,i,o=e.dataset,s="basic"===o.type?function(){return V(o.data,function(t){return bO(t,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:(u=n,a=e,r=function(t,n,e,o){var r=u.shared.providers.translate(t.title);if("separator"===t.type)return st.some({type:"separator",text:r});if("submenu"!==t.type)return st.some(et({type:"togglemenuitem",text:r,icon:t.icon,active:t.isSelected(o),disabled:e,onAction:a.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}})));var i=U(t.getStyleItems(),function(t){return c(t,n,o)});return 0===n&&i.length<=0?st.none():st.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return U(t.getStyleItems(),function(t){return c(t,n,o)})}})},c=function(t,n,e){var o="formatter"===t.type&&a.isInvalid(t);return 0===n?o?[]:r(t,n,!1,e).toArray():r(t,n,o,e).toArray()},{validateItems:i=function(t){var n=a.getCurrentValue(),e=a.shouldHide?0:1;return U(t,function(t){return c(t,e,n)})},getFetch:function(o,r){return function(t){var n=r(),e=i(n);t(Lk(e,Ap.CLOSE_ON_EXECUTE,o,!1))}}}),getStyleItems:s}},qT=function(o,t,n){var e=YT(0,t,n),r=e.items,i=e.getStyleItems;return Rk({text:n.icon.isSome()?st.none():st.some(""),icon:n.icon,tooltip:st.from(n.tooltip),role:st.none(),fetch:r.getFetch(t,i),onSetup:function(e){return n.setInitialValue.each(function(t){return t(e.getComponent())}),n.nodeChangeHandler.map(function(t){var n=t(e.getComponent());return o.on("NodeChange",n),function(){o.off("NodeChange",n)}}).getOr(Z)},getApi:function(t){return{getComponent:function(){return t}}},columns:1,presets:"normal",classes:n.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};(DC=BC=BC||{})[DC.SemiColon=0]="SemiColon",DC[DC.Space=1]="Space";var KT,JT,$T=function(t,n,e,o){var r,i,u=t.getParam(n,e,"string");return{type:"basic",data:(i=u,r=o===BC.SemiColon?i.replace(/;$/,"").split(";"):i.split(" "),V(r,function(t){var n=t,e=t,o=t.split("=");return 1<o.length&&(n=o[0],e=o[1]),{title:n,format:e}}))}},QT=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],ZT=function(e){var n=function(t){var n=L(QT,function(t){return e.formatter.match(t.format)}).fold(function(){return"left"},function(t){return t.title.toLowerCase()});Uo(t,Ik,{icon:"align-"+n})},t=st.some(function(t){return function(){return n(t)}}),o=st.some(function(t){return n(t)}),r={type:"basic",data:QT};return{tooltip:"Align",icon:st.some("align-left"),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:at(st.none()),getPreviewFor:function(t){return function(){return st.none()}},onAction:function(n){return function(){return L(QT,function(t){return t.format===n.format}).each(function(t){return e.execCommand(t.command)})}},setInitialValue:o,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}},tE=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],nE=function(t){var n=t.split(/\s*,\s*/);return V(n,function(t){return t.replace(/^['"]+|['"]+$/g,"")})},eE=function(t){var n;return 0===t.indexOf("-apple-system")&&(n=nE(t.toLowerCase()),W(tE,function(t){return-1<n.indexOf(t.toLowerCase())}))},oE=function(r){var i=function(){var e=function(t){return t?nE(t)[0]:""},t=r.queryCommandValue("FontName"),n=u.data,o=t?t.toLowerCase():"";return{matchOpt:L(n,function(t){var n=t.format;return n.toLowerCase()===o||e(n).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return eE(o)?st.from({title:"System Font",format:o}):st.none()}),font:t}},n=function(t){var n=i(),e=n.matchOpt,o=n.font,r=e.fold(function(){return o},function(t){return t.title});Uo(t,Fk,{text:r})},t=st.some(function(t){return function(){return n(t)}}),e=st.some(function(t){return n(t)}),u=$T(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",BC.SemiColon);return{tooltip:"Fonts",icon:st.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(t){return function(){return st.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},setInitialValue:e,nodeChangeHandler:t,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}},rE={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},iE=function(t,n){return/[0-9.]+px$/.test(t)?(e=72*parseInt(t,10)/96,o=n||0,r=Math.pow(10,o),Math.round(e*r)/r+"pt"):t;var e,o,r},uE=function(e){var i=function(){var o=st.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var t=function(t){var n=iE(i,t),e=Ft(rE,n).getOr("");o=L(r,function(t){return t.format===i||t.format===n||t.format===e})},n=3;o.isNone()&&0<=n;n--)t(n);return{matchOpt:o,size:i}},t=at(at(st.none())),n=function(t){var n=i(),e=n.matchOpt,o=n.size,r=e.fold(function(){return o},function(t){return t.title});Uo(t,Fk,{text:r})},o=st.some(function(t){return function(){return n(t)}}),r=st.some(function(t){return n(t)}),u=$T(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",BC.Space);return{tooltip:"Font sizes",icon:st.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getPreviewFor:t,getCurrentValue:function(){return i().matchOpt},onAction:function(t){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,t.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}},aE=function(e,t,n){var o=t();return $(n,function(n){return L(o,function(t){return e.formatter.matchNode(n,t.format)})}).orThunk(function(){return e.formatter.match("p")?st.some({title:"Paragraph",format:"p"}):st.none()})},cE=function(t){var n=t.selection.getStart(!0)||t.getBody();return t.dom.getParents(n,function(){return!0},t.getBody())},sE=function(o,r){return function(n){var e=se(st.none()),t=function(){n.setActive(o.formatter.match(r));var t=o.formatter.formatChanged(r,n.setActive).unbind;e.set(st.some(t))};return o.initialized?t():o.on("init",t),function(){return e.get().each(function(t){return t()})}}},lE=function(n){return function(t){return function(){n.undoManager.transact(function(){n.focus(),n.execCommand("mceToggleFormat",!1,t.format)})}}},fE=function(o){var e=function(t,n){var e=aE(o,function(){return r.data},t).fold(function(){return"Paragraph"},function(t){return t.title});Uo(n,Fk,{text:e})},t=st.some(function(n){return function(t){return e(t.parents,n)}}),n=st.some(function(t){var n=cE(o);e(n,t)}),r=$T(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",BC.SemiColon);return{tooltip:"Blocks",icon:st.none(),isSelectedFor:function(t){return function(){return o.formatter.match(t)}},getCurrentValue:at(st.none()),getPreviewFor:function(n){return function(){var t=o.formatter.get(n);return st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(n))})}},onAction:lE(o),setInitialValue:n,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!o.formatter.canApply(t.format)}}},dE=function(i,t){var e=function(t,n){var e=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,e):[{title:t.title,format:t.format}]},o=U(vO(i),e),r=aE(i,function(){return o},t).fold(function(){return"Paragraph"},function(t){return t.title});Uo(n,Fk,{text:r})},n=st.some(function(n){return function(t){return e(t.parents,n)}}),o=st.some(function(t){var n=cE(i);e(n,t)});return{tooltip:"Formats",icon:st.none(),isSelectedFor:function(t){return function(){return i.formatter.match(t)}},getCurrentValue:at(st.none()),getPreviewFor:function(n){return function(){var t=i.formatter.get(n);return t!==undefined?st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:i.dom.parseStyle(i.formatter.getCssText(n))}):st.none()}},onAction:lE(i),setInitialValue:o,nodeChangeHandler:n,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!i.formatter.canApply(t.format)},dataset:t}},mE=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],gE=function(r,i){return function(t,n,e){var o=r(t).mapError(function(t){return En(t)}).getOrDie();return i(o,n,e)}},pE={button:gE(J_,function(t,n){return e=t,o=n.backstage.shared.providers,AT(e,o,[]);var e,o}),togglebutton:gE(oT,function(t,n){return e=t,o=n.backstage.shared.providers,MT(e,o,[]);var e,o}),menubutton:gE(Z_,function(t,n){return Uk(t,"tox-tbtn",n.backstage,st.none())}),splitbutton:gE(function(t){return On("SplitButton",tT,t)},function(t,n){return FT(t,n.backstage.shared)}),grouptoolbarbutton:gE(function(t){return On("GroupToolbarButton",rT,t)},function(t,n,e){var o,r,i,u,a,c,s=e.ui.registry.getAll().buttons,l=((o={})[Rc]=n.backstage.shared.header.isPositionedAtTop()?Yu.TopToBottom:Yu.BottomToTop,o);switch(Wh(e)){case Bp.floating:return r=t,i=n.backstage,u=function(t){return yE(e,{buttons:s,toolbar:t,allowToolbarGroups:!1},n,st.none())},a=l,c=i.shared,h_.sketch({lazySink:c.getSink,fetch:function(){return Iy(function(t){t(V(u(r.items),j_))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:BT(r.icon,r.text,r.tooltip,st.none(),st.none(),c.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:a}}}});default:throw new Error("Toolbar groups are only supported when using floating toolbar mode")}}),styleSelectButton:function(t,n){return e=t,o=n.backstage,r=et({type:"advanced"},o.styleselect),qT(e,o,dE(e,r));var e,o,r},fontsizeSelectButton:function(t,n){return e=t,o=n.backstage,qT(e,o,uE(e));var e,o},fontSelectButton:function(t,n){return e=t,o=n.backstage,qT(e,o,oE(e));var e,o},formatButton:function(t,n){return e=t,o=n.backstage,qT(e,o,fE(e));var e,o},alignMenuButton:function(t,n){return e=t,o=n.backstage,qT(e,o,ZT(e));var e,o}},hE={styleselect:pE.styleSelectButton,fontsizeselect:pE.fontsizeSelectButton,fontselect:pE.fontSelectButton,formatselect:pE.formatButton,align:pE.alignMenuButton},vE=function(t){var n,e,o,r=t.toolbar,i=t.buttons;return!1===r?[]:r===undefined||!0===r?(e=i,o=V(mE,function(t){var n=H(t.items,function(t){return It(e,t)||It(hE,t)});return{name:t.name,items:n}}),H(o,function(t){return 0<t.items.length})):w(r)?(n=r.split("|"),V(n,function(t){return{items:t.trim().split(" ")}})):T(r,function(t){return It(t,"name")&&It(t,"items")})?r:(nt.console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])},bE=function(r,n,i,u,a,t){return Ft(n,i.toLowerCase()).orThunk(function(){return t.bind(function(t){return $(t,function(t){return Ft(n,t+i.toLowerCase())})})}).fold(function(){return Ft(hE,i.toLowerCase()).map(function(t){return t(r,a)}).orThunk(function(){return st.none()})},function(t){return"grouptoolbarbutton"!==t.type||u?(e=a,o=r,Ft(pE,(n=t).type).fold(function(){return nt.console.error("skipping button defined by",n),st.none()},function(t){return st.some(t(n,e,o))})):(nt.console.warn("Ignoring the '"+i+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),st.none());var n,e,o})},yE=function(e,o,r,i){var t=vE(o),n=V(t,function(t){var n=U(t.items,function(t){return 0===t.trim().length?[]:bE(e,o.buttons,t,o.allowToolbarGroups,r,i).toArray()});return{title:st.from(e.translate(t.name)),items:n}});return H(n,function(t){return 0<t.items.length})},xE={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},wE={maxHeightFunction:_c(),maxWidthFunction:qO()},SE={onLtr:function(){return[Ra,Va,Fa,Aa,Ia,Ma,Kg,Jg,Yg,Gg,qg,Xg]},onRtl:function(){return[Ra,Va,Ia,Ma,Fa,Aa,Kg,Jg,qg,Xg,Yg,Gg]}},kE={onLtr:function(){return[Va,Aa,Ma,Fa,Ia,Ra,Kg,Jg,Yg,Gg,qg,Xg]},onRtl:function(){return[Va,Ma,Aa,Ia,Fa,Ra,Kg,Jg,qg,Xg,Yg,Gg]}},CE=function(c,t,e,s){var o,r,l=ze().deviceType.isTouch,a=uu((o={sink:e,onEscape:function(){return c.focus(),st.some(!0)}},r=se([]),zg.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){r.set([]),zg.getContent(t).each(function(t){Yi(t.element(),"visibility")}),Ci(t.element(),XT),Yi(t.element(),"width")},inlineBehaviours:Ka([$m("context-toolbar-events",[or(go(),function(t,n){Ci(t.element(),XT),Yi(t.element(),"width")}),$o(GT,function(n,e){Yi(n.element(),"width");var t=bu(n.element());zg.setContent(n,e.event().contents()),Si(n.element(),XT);var o=bu(n.element());Ni(n.element(),"width",t+"px"),zg.getContent(n).each(function(t){e.event().focus().bind(function(t){return ic(t),ac(n.element())}).orThunk(function(){return Gm.focusIn(t),uc()})}),$g.setTimeout(function(){Ni(n.element(),"width",o+"px")},0)}),$o(UT,function(t,n){zg.getContent(t).each(function(t){r.set(r.get().concat([{bar:t,focus:uc()}]))}),Uo(t,GT,{contents:n.event().forwardContents(),focus:st.none()})}),$o(WT,function(n,t){K(r.get()).each(function(t){r.set(r.get().slice(0,r.get().length-1)),Uo(n,GT,{contents:au(t.bar),focus:t.focus})})})]),Gm.config({mode:"special",onEscape:function(n){return K(r.get()).fold(function(){return o.onEscape()},function(t){return jo(n,WT),st.some(!0)})}})]),lazySink:function(){return ut.value(o.sink)}}))),f=function(){return HT(c,s.backstage.shared)},u=function(){if(l()&&s.backstage.isContextMenuOpen())return!0;var t,n,e,o,r,i,u=(t=m.get().map(function(t){return t.getBoundingClientRect()}).getOrThunk(function(){return c.selection.getRng().getBoundingClientRect()}),n=c.inline?ku().top():Mu(fe.fromDom(c.getBody())).y,{y:t.top+n,bottom:t.bottom+n}),a=f();return e=u.y,o=u.bottom,r=a.y,i=a.bottom,!(Math.max(e,r)<=Math.min(o,i))},n=function(){zg.hide(a)},i=function(){d.get().each(function(t){var n=a.element();Yi(n,"display"),u()?Ni(n,"display","none"):Ns.positionWithinBounds(e,t,a,st.some(f()))})},d=se(st.none()),m=se(st.none()),g=se(null),p=function(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:Ka([Gm.config({mode:"acyclic"}),$m("pop-dialog-wrap-events",[rr(function(t){c.shortcuts.add("ctrl+F9","focus statusbar",function(){return Gm.focusIn(t)})}),ir(function(t){c.shortcuts.remove("ctrl+F9")})])])}},h=Lt(function(){return jT(t,function(t){var n=v([t]);Uo(a,UT,{forwardContents:p(n)})})}),v=function(t){var n=c.ui.registry.getAll().buttons,e=h(),o=et(et({},n),e.formNavigators),r=Wh(c)===Bp.scrolling?Bp.scrolling:Bp["default"],i=it(V(t,function(t){return"contexttoolbar"===t.type?yE(c,{buttons:o,toolbar:t.items,allowToolbarGroups:!1},s,st.some(["form:"])):(n=t,e=s.backstage.shared.providers,PT(n,e));var n,e}));return Y_({type:r,uid:Wr("context-toolbar"),initGroups:i,onEscape:st.none,cyclicKeying:!0,providers:s.backstage.shared.providers})};c.on("contexttoolbar-show",function(n){var t=h();Ft(t.lookupTable,n.toolbarKey).each(function(t){y([t],n.target===c?st.none():st.some(n)),zg.getContent(a).each(Gm.focusIn)})});var b=function(t,n){var e,o,r="node"===t?s.backstage.shared.anchors.node(n):s.backstage.shared.anchors.cursor();return zt(r,(e=t,o=l(),"line"===e?{bubble:Ac(12,0,xE),layouts:{onLtr:function(){return[Pa]},onRtl:function(){return[Ha]}},overrides:wE}:{bubble:Ac(0,12,xE),layouts:o?kE:SE,overrides:wE}))},y=function(t,n){if(w(),!l()||!s.backstage.isContextMenuOpen()){var e=v(t),o=n.map(fe.fromDom),r=b(t[0].position,o);d.set(st.some(r)),m.set(n);var i=a.element();Yi(i,"display"),zg.showWithinBounds(a,r,p(e),function(){return st.some(f())}),u()&&Ni(i,"display","none")}},x=function(){if(c.hasFocus()){var t,n,e,o,r,i,u=h();t=u,n=c,o=fe.fromDom(n.getBody()),r=function(t){return je(t,o)},i=fe.fromDom(n.selection.getNode()),(r(e=i)||We(o,e)?NT(i,t.inNodeScope,t.inEditorScope).orThunk(function(){return LT(r,i,t)}):st.none()).fold(function(){d.set(st.none()),zg.hide(a)},function(t){y(t.toolbars,st.some(t.elem.dom()))})}},w=function(){var t=g.get();null!==t&&($g.clearTimeout(t),g.set(null))},S=function(t){w(),g.set(t)};c.on("init",function(){c.on(KO,n),c.on("ScrollContent ScrollWindow longpress",i),c.on("click keyup focus SetContent ObjectResized ResizeEditor",function(){S($g.setEditorTimeout(c,x,0))}),c.on("focusout",function(t){$g.setEditorTimeout(c,function(){ac(e.element()).isNone()&&ac(a.element()).isNone()&&(d.set(st.none()),zg.hide(a))},0)}),c.on("SwitchMode",function(){c.mode.isReadOnly()&&(d.set(st.none()),zg.hide(a))}),c.on("NodeChange",function(t){ac(a.element()).fold(function(){S($g.setEditorTimeout(c,x,0))},function(t){})})})},OE=Tf,_E=lf,TE=at([te("shell",!1),Nn("makeItem"),te("setupItem",Z),Ml("listBehaviours",[Jm])]),EE=af({name:"items",overrides:function(){return{behaviours:Ka([Jm.config({})])}}}),BE=at([EE]),DE=Vf({name:at("CustomList")(),configFields:TE(),partFields:BE(),factory:function(s,t,n,e){var o=s.shell?{behaviours:[Jm.config({})],components:[]}:{behaviours:[],components:t},r=function(t){return s.shell?st.some(t):yf(t,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Al(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw nt.console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(n){var t=Jm.contents(n),e=c.length,o=e-t.length,r=0<o?I(o,function(){return s.makeItem()}):[],i=t.slice(e);rt(i,function(t){return Jm.remove(n,t)}),rt(r,function(t){return Jm.append(n,t)});var u=Jm.contents(n);rt(u,function(t,n){s.setupItem(a,t,c[n],n)})})}}}},apis:{setItems:function(t,n,e){t.setItems(n,e)}}}),AE=function(t){return(Gi(t,"position").is("fixed")?st.none():yr(t)).orThunk(function(){var e=fe.fromTag("span");return br(t).bind(function(t){Or(t,e);var n=yr(e);return Er(e),n})})},ME=function(t){return AE(t).map(pu).getOrThunk(function(){return mu(0,0)})},FE=Vt([{"static":[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),IE=function(t,n){var e=t.element();Si(e,n.transitionClass),Ci(e,n.fadeOutClass),Si(e,n.fadeInClass),n.onShow(t)},RE=function(t,n){var e=t.element();Si(e,n.transitionClass),Ci(e,n.fadeInClass),Si(e,n.fadeOutClass),n.onHide(t)},VE=function(t,o,r){return W(t,function(t){switch(t){case"bottom":return e=r,o.bottom<=e.bottom;case"top":return n=r,o.y>=n.y}var n,e})},PE=function(n,t){return t.getInitialPosition().map(function(t){return Du(t.bounds.x,t.bounds.y,bu(n),lu(n))})},HE=function(t,n,e){e.setInitialPosition(st.some({style:function(t){var n={},e=t.dom();if(Ei(e))for(var o=0;o<e.style.length;o++){var r=e.style.item(o);n[r]=e.style[r]}return n}(t),position:Ui(t,"position")||"static",bounds:n}))},zE=function(e,o,r){return r.getInitialPosition().bind(function(t){switch(r.setInitialPosition(st.none()),t.position){case"static":return st.some(FE["static"]());case"absolute":var n=AE(e).map(Au).getOrThunk(function(){return Au(Vi())});return st.some(FE.absolute(sc("absolute",Ft(t.style,"left").map(function(t){return o.x-n.x}),Ft(t.style,"top").map(function(t){return o.y-n.y}),Ft(t.style,"right").map(function(t){return n.right-o.right}),Ft(t.style,"bottom").map(function(t){return n.bottom-o.bottom}))));default:return st.none()}})},NE=function(t,n,e){var o,r,i,u=t.element();return Gi(u,"position").is("fixed")?(r=n,PE(o=u,i=e).filter(function(t){return VE(i.getModes(),t,r)}).bind(function(t){return zE(o,t,i)})):function(t,n,e){var o=Au(t);if(VE(e.getModes(),o,n))return st.none();HE(t,o,e);var r=Fu(),i=o.x-r.x,u=n.y-r.y,a=r.bottom-n.bottom,c=o.y<=n.y;return st.some(FE.fixed(sc("fixed",st.some(i),c?st.some(u):st.none(),st.none(),c?st.none():st.some(a))))}(u,n,e)},LE=function(n,t){rt(["left","right","top","bottom","position"],function(t){return Yi(n.element(),t)}),t.onUndocked(n)},jE=function(t,n,e){lc(t.element(),e),("fixed"===e.position()?n.onDocked:n.onUndocked)(t)},UE=function(i,t,u,a,c){void 0===c&&(c=!1),t.contextual.each(function(r){r.lazyContext(i).each(function(t){var n,e,o=(e=a,(n=t).y<e.bottom&&n.bottom>e.y);o!==u.isVisible()&&(u.setVisible(o),c&&!o?(_i(i.element(),[r.fadeOutClass]),r.onHide(i)):(o?IE:RE)(i,r))})})},WE=function(n,e,t){var o,r,i=n.element();t.setDocked(!1),o=t,r=n.element(),PE(r,o).bind(function(t){return zE(r,t,o)}).each(function(t){t.fold(function(){return LE(n,e)},function(t){return jE(n,e,t)},Z)}),t.setVisible(!0),e.contextual.each(function(t){Ti(i,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(n)}),GE(n,e,t)},GE=function(t,n,e){var o,r,i,u,a;t.getSystem().isConnected()&&(o=t,i=e,u=(r=n).lazyViewport(o),(a=i.isDocked())&&UE(o,r,i,u),NE(o,u,i).each(function(t){i.setDocked(!a),t.fold(function(){return LE(o,r)},function(t){return jE(o,r,t)},function(t){UE(o,r,i,u,!0),jE(o,r,t)})}))},XE=function(t,n,e){e.isDocked()&&WE(t,n,e)},YE=/* */Object.freeze({__proto__:null,refresh:GE,reset:XE,isDocked:function(t,n,e){return e.isDocked()},getModes:function(t,n,e){return e.getModes()},setModes:function(t,n,e,o){return e.setModes(o)}}),qE=/* */Object.freeze({__proto__:null,events:function(o,r){return qo([or(go(),function(n,e){o.contextual.each(function(t){Oi(n.element(),t.transitionClass)&&(Ti(n.element(),[t.transitionClass,t.fadeInClass]),(r.isVisible()?t.onShown:t.onHidden)(n));e.stop()})}),$o(Do(),function(t,n){GE(t,o,r)}),$o(Ao(),function(t,n){XE(t,o,r)})])}}),KE=[Zn("contextual",[jn("fadeInClass"),jn("fadeOutClass"),jn("transitionClass"),Wn("lazyContext"),aa("onShow"),aa("onShown"),aa("onHide"),aa("onHidden")]),ue("lazyViewport",Fu),ne("modes",["top","bottom"],dn(In)),aa("onDocked"),aa("onUndocked")],JE=$a({fields:KE,name:"docking",active:qE,apis:YE,state:/* */Object.freeze({__proto__:null,init:function(t){var n=se(!1),e=se(!0),o=se(st.none()),r=se(t.modes);return ai({isDocked:n.get,setDocked:n.set,getInitialPosition:o.get,setInitialPosition:o.set,isVisible:e.get,setVisible:e.set,getModes:r.get,setModes:r.set,readState:function(){return"docked:  "+n.get()+", visible: "+e.get()+", modes: "+r.get().join(",")}})}})}),$E={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},QE="tox-tinymce--toolbar-sticky-on",ZE="tox-tinymce--toolbar-sticky-off",tB=function(r){var i=r.element();br(i).each(function(t){var n,e="padding-"+JE.getModes(r)[0];if(JE.isDocked(r)){var o=bu(t);Ni(i,"width",o+"px"),Ni(t,e,fu(n=i)+(parseInt(Ui(n,"margin-top"),10)||0)+(parseInt(Ui(n,"margin-bottom"),10)||0)+"px")}else Yi(i,"width"),Yi(t,e)})},nB=function(t,n){n?(Ci(t,$E.fadeOutClass),_i(t,[$E.transitionClass,$E.fadeInClass])):(Ci(t,$E.fadeInClass),_i(t,[$E.fadeOutClass,$E.transitionClass]))},eB=function(t,n){var e=fe.fromDom(t.getContainer());n?(Si(e,QE),Ci(e,ZE)):(Si(e,ZE),Ci(e,QE))},oB=function(c,t){var n,i=se(st.none()),o=t.getSink,u=function(n){o().each(function(t){return n(t.element())})},e=function(t){c.inline||tB(t),eB(c,JE.isDocked(t)),t.getSystem().broadcastOn([ll()],{}),o().each(function(t){return t.getSystem().broadcastOn([ll()],{})})},r=c.inline?[]:[oc.config({channels:((n={})[N_()]={onReceive:tB},n)})];return b([eg.config({}),JE.config({contextual:et({lazyContext:function(t){var n,e,o=fu(t.element()),r=c.inline?c.getContentAreaContainer():c.getContainer(),i=Au(fe.fromDom(r)),u=i.height-o,a=i.y+(n=t,e="top",M(JE.getModes(n),e)?0:o);return st.some(Du(i.x,a,i.width,u))},onShow:function(){u(function(t){return nB(t,!0)})},onShown:function(r){u(function(t){return Ti(t,[$E.transitionClass,$E.fadeInClass])}),i.get().each(function(t){var n,e,o;n=r.element(),o=gr(e=t),uc(o).filter(function(t){return!je(e,t)}).filter(function(t){return je(t,fe.fromDom(o.dom().body))||We(n,t)}).each(function(){return ic(e)}),i.set(st.none())})},onHide:function(t){var n,e;i.set((n=t.element(),e=o,ac(n).orThunk(function(){return e().toOption().bind(function(t){return ac(t.element())})}))),u(function(t){return nB(t,!1)})},onHidden:function(){u(function(t){return Ti(t,[$E.transitionClass])})}},$E),modes:[t.header.getDockingMode()],onDocked:e,onUndocked:e})],r)},rB=/* */Object.freeze({__proto__:null,setup:function(t,n,o){t.inline||(n.header.isPositionedAtTop()||t.on("ResizeEditor",function(){o().each(JE.reset)}),t.on("ResizeWindow ResizeEditor",function(){o().each(tB)}),t.on("SkinLoaded",function(){o().each(function(t){JE.isDocked(t)?JE.reset(t):JE.refresh(t)})}),t.on("FullscreenStateChanged",function(){o().each(JE.reset)})),t.on("AfterScrollIntoView",function(e){o().each(function(t){JE.refresh(t);var n=t.element();zd(n)&&function(t,n){var e=gr(n),o=e.dom().defaultView.innerHeight,r=ku(e),i=fe.fromDom(t.elm),u=Mu(i),a=lu(i),c=u.y,s=c+a,l=pu(n),f=lu(n),d=l.top(),m=d+f,g=Math.abs(d-r.top())<2,p=Math.abs(m-(r.top()+o))<2;if(g&&c<m)Cu(r.left(),c-f,e);else if(p&&d<s){var h=c-o+a+f;Cu(r.left(),h,e)}}(e,n)})}),t.on("PostRender",function(){eB(t,!1)})},isDocked:function(t){return t().map(JE.isDocked).getOr(!1)},getBehaviours:oB}),iB=Z,uB=c,aB=at([]),cB=/* */Object.freeze({__proto__:null,setup:iB,isDocked:uB,getBehaviours:aB}),sB=Rf({factory:function(n,o){var t={focus:Gm.focusIn,setMenus:function(t,n){var e=V(n,function(n){var t={type:"menubutton",text:n.text,fetch:function(t){t(n.getItems())}},e=Z_(t).mapError(function(t){return En(t)}).getOrDie();return Uk(e,"tox-mbtn",o.backstage,st.some("menuitem"))});Jm.set(t,e)}};return{uid:n.uid,dom:n.dom,components:[],behaviours:Ka([Jm.config({}),$m("menubar-events",[rr(function(t){n.onSetup(t)}),$o(io(),function(e,t){Lu(e.element(),".tox-mbtn--active").each(function(n){ju(t.event().target(),".tox-mbtn").each(function(t){je(n,t)||e.getSystem().getByDom(n).each(function(n){e.getSystem().getByDom(t).each(function(t){sx.expand(t),sx.close(n),eg.focus(t)})})})})}),$o(Vo(),function(e,t){t.event().prevFocus().bind(function(t){return e.getSystem().getByDom(t).toOption()}).each(function(n){t.event().newFocus().bind(function(t){return e.getSystem().getByDom(t).toOption()}).each(function(t){sx.isOpen(n)&&(sx.expand(t),sx.close(n))})})})]),Gm.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return n.onEscape(t),st.some(!0)}}),uy.config({})]),apis:t,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Nn("dom"),Nn("uid"),Nn("onEscape"),Nn("backstage"),te("onSetup",Z)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),lB="container",fB=[Bl("slotBehaviours",[])],dB=function(t){return"<alloy.field."+t+">"},mB=function(r,t){var e,n=function(t){return kf(r)},o=function(e,o){return function(t,n){return yf(t,r,n).map(function(t){return e(t,n)}).getOr(o)}},i=function(t,n){return"true"!==Ir(t.element(),"aria-hidden")},u=o(i,!1),a=o(function(t,n){if(i(t)){var e=t.element();Ni(e,"display","none"),Fr(e,"aria-hidden","true"),Uo(t,Po(),{name:n,visible:!1})}}),c=function(n,t){rt(t,function(t){return e(n,t)})},s=o(function(t,n){if(!i(t)){var e=t.element();Yi(e,"display"),Pr(e,"aria-hidden"),Uo(t,Po(),{name:n,visible:!0})}}),l={getSlotNames:n,getSlot:function(t,n){return yf(t,r,n)},isShowing:u,hideSlot:e=a,hideAllSlots:function(t){return c(t,n())},showSlot:s};return{uid:r.uid,dom:r.dom,components:t,behaviours:Dl(r.slotBehaviours),apis:l}},gB=Tt({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},function(t){return ii(t)}),pB=et(et({},gB),{sketch:function(t){var e,n=(e=[],{slot:function(t,n){return e.push(t),gf(lB,dB(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return rf({name:t,pname:dB(t)})});return Bf(lB,fB,i,mB,o)}}),hB=fn([$n("icon"),$n("tooltip"),ue("onShow",Z),ue("onHide",Z),ue("onSetup",function(){return Z})]),vB=function(t){return{element:function(){return t.element().dom()}}},bB=function(e,o){var r=V(Ct(o),function(t){var n=o[t],e=_n(On("sidebar",hB,n));return{name:t,getApi:vB,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return V(r,function(t){var n=se(Z);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:uh([av(t,n),cv(t,n),$o(Po(),function(n,t){var e=t.event();L(r,function(t){return t.name===e.name()}).each(function(t){(e.visible()?t.onShow:t.onHide)(t.getApi(n))})})])})})},yB=function(t,e){Lf.getCurrent(t).each(function(t){return Jm.set(t,[(n=e,pB.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:bB(t,n),slotBehaviours:uh([rr(function(t){return pB.hideAllSlots(t)})])}}))]);var n})},xB=function(t){return Lf.getCurrent(t).bind(function(t){return R_.isGrowing(t)||R_.hasGrown(t)?Lf.getCurrent(t).bind(function(n){return L(pB.getSlotNames(n),function(t){return pB.isShowing(n,t)})}):st.none()})},wB=Wr("FixSizeEvent"),SB=Wr("AutoSizeEvent"),kB=function(t){var n,e,o,r=fe.fromHtml(t),i=xr(r),u=(e=(n=r).dom().attributes!==undefined?n.dom().attributes:[],N(e,function(t,n){var e;return"class"===n.name?t:et(et({},t),((e={})[n.name]=n.value,e))},{})),a=(o=r,Array.prototype.slice.call(o.dom().classList,0)),c=0===i.length?{}:{innerHtml:Dr(r)};return et({tag:cr(r),classes:a,attributes:u},c)},CB=function(t,n,e){var o=t.element();!0===n?(Jm.set(t,[{dom:{tag:"div",attributes:{"aria-label":e.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:kB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:Ka([Gm.config({mode:"special",onTab:function(){return st.some(!0)},onShiftTab:function(){return st.some(!0)}}),eg.config({})])}]),Yi(o,"display"),Pr(o,"aria-hidden")):(Jm.set(t,[]),Ni(o,"display","none"),Fr(o,"aria-hidden","true"))},OB=_E.optional({factory:sB,name:"menubar",schema:[Nn("backstage")]}),_B=_E.optional({factory:{sketch:function(t){return DE.sketch({uid:t.uid,dom:t.dom,listBehaviours:Ka([Gm.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return Y_({type:t.type,uid:Wr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return st.none()}})},setupItem:function(t,n,e,o){n_.setGroups(n,e)},shell:!0})}},name:"multiple-toolbar",schema:[Nn("dom"),Nn("onEscape")]}),TB=_E.optional({factory:{sketch:function(t){var n;return((n=t).type===Bp.sliding?X_:n.type===Bp.floating?G_:Y_)({type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),st.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes})}},name:"toolbar",schema:[Nn("dom"),Nn("onEscape"),Nn("getSink")]}),EB=_E.optional({factory:{sketch:function(t){var n=t.editor,e=t.sticky?oB:aB;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:Ka(e(n,t.sharedBackstage))}}},name:"header",schema:[Nn("dom")]}),BB=_E.optional({name:"socket",schema:[Nn("dom")]}),DB=_E.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:Ka([uy.config({}),eg.config({}),R_.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){Lf.getCurrent(t).each(pB.hideAllSlots),jo(t,SB)},onGrown:function(t){jo(t,SB)},onStartGrow:function(t){Uo(t,wB,{width:Gi(t.element(),"width").getOr("")})},onStartShrink:function(t){Uo(t,wB,{width:bu(t.element())+"px"})}}),Jm.config({}),Lf.config({find:function(t){var n=Jm.contents(t);return q(n)}})])}],behaviours:Ka([dS(0),$m("sidebar-sliding-events",[$o(wB,function(t,n){Ni(t.element(),"width",n.event().width())}),$o(SB,function(t,n){Yi(t.element(),"width")})])])}}},name:"sidebar",schema:[Nn("dom")]}),AB=_E.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:Ka([Jm.config({})]),components:[]}}},name:"throbber",schema:[Nn("dom")]}),MB=Vf({name:"OuterContainer",factory:function(e,t,n){var o={getSocket:function(t){return OE.getPart(t,e,"socket")},setSidebar:function(t,n){OE.getPart(t,e,"sidebar").each(function(t){return yB(t,n)})},toggleSidebar:function(t,o){OE.getPart(t,e,"sidebar").each(function(t){return n=t,e=o,void Lf.getCurrent(n).each(function(n){Lf.getCurrent(n).each(function(t){R_.hasGrown(n)?pB.isShowing(t,e)?R_.shrink(n):(pB.hideAllSlots(t),pB.showSlot(t,e)):(pB.hideAllSlots(t),pB.showSlot(t,e),R_.grow(n))})});var n,e})},whichSidebar:function(t){return OE.getPart(t,e,"sidebar").bind(xB).getOrNull()},getHeader:function(t){return OE.getPart(t,e,"header")},getToolbar:function(t){return OE.getPart(t,e,"toolbar")},setToolbar:function(t,n){OE.getPart(t,e,"toolbar").each(function(t){t.getApis().setGroups(t,n)})},setToolbars:function(t,n){OE.getPart(t,e,"multiple-toolbar").each(function(t){DE.setItems(t,n)})},refreshToolbar:function(t){OE.getPart(t,e,"toolbar").each(function(t){return t.getApis().refresh(t)})},getThrobber:function(t){return OE.getPart(t,e,"throbber")},focusToolbar:function(t){OE.getPart(t,e,"toolbar").orThunk(function(){return OE.getPart(t,e,"multiple-toolbar")}).each(function(t){Gm.focusIn(t)})},setMenubar:function(t,n){OE.getPart(t,e,"menubar").each(function(t){sB.setMenus(t,n)})},focusMenubar:function(t){OE.getPart(t,e,"menubar").each(function(t){sB.focus(t)})}};return{uid:e.uid,dom:e.dom,components:t,apis:o,behaviours:e.behaviours}},configFields:[Nn("dom"),Nn("behaviours")],partFields:[EB,OB,TB,_B,BB,DB,AB],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getHeader:function(t,n){return t.getHeader(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=V(e,function(t){return j_(t)});t.setToolbar(n,o)},setToolbars:function(t,n,e){var o=V(e,function(t){return V(t,j_)});t.setToolbars(n,o)},refreshToolbar:function(t,n){return t.refreshToolbar(n)},getThrobber:function(t,n){return t.getThrobber(n)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),FB={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},IB=function(t){return"string"==typeof t?t.split(" "):t},RB=function(i,u){var a=et(et({},FB),u.menus),n=0<Ct(u.menus).length,t=u.menubar===undefined||!0===u.menubar?IB("file edit view insert format tools table help"):IB(!1===u.menubar?"":u.menubar),e=H(t,function(t){return n&&u.menus.hasOwnProperty(t)&&u.menus[t].hasOwnProperty("items")||FB.hasOwnProperty(t)}),o=V(e,function(t){var n,e,o,r=a[t];return n={title:r.title,items:IB(r.items)},e=u,o=i.getParam("removed_menuitems","").split(/[ ,]/),{text:n.title,getItems:function(){return U(n.items,function(t){var n=t.toLowerCase();return 0===n.trim().length||F(o,function(t){return t===n})?[]:"separator"===n||"|"===n?[{type:"separator"}]:e.menuItems[n]?[e.menuItems[n]]:[]})}}});return H(o,function(t){return 0<t.getItems().length&&F(t.getItems(),function(t){return"separator"!==t.type})})},VB=function(t){var n=function(){t._skinLoaded=!0,t.fire("SkinLoaded")};return function(){t.initialized?n():t.on("init",n)}},PB=function(n,e){return function(){return t={message:e},n.fire("SkinLoadError",t);var t}},HB=function(t,n){var e,o=function(t){var n=t.getParam("skin"),e=t.getParam("skin_url");if(!1!==n){var o=n||"oxide";e=e?t.documentBaseURI.toAbsolute(e):Ah.baseURL+"/skins/ui/"+o}return e}(n);o&&(e=o+"/skin.min.css",n.contentCSS.push(o+(t?"/content.inline":"/content")+".min.css")),!1==(!1===n.getParam("skin"))&&e?n.ui.styleSheetLoader.load(e,VB(n),PB(n,"Skin could not be loaded")):VB(n)()},zB=g(HB,!1),NB=g(HB,!0),LB=function(e,t,o,r){var n=t.outerContainer,i=o.toolbar,u=o.buttons;if(T(i,w)){var a=i.map(function(t){var n={toolbar:t,buttons:u,allowToolbarGroups:o.allowToolbarGroups};return yE(e,n,{backstage:r},st.none())});MB.setToolbars(n,a)}else MB.setToolbar(n,yE(e,o,{backstage:r},st.none()))},jB=ph.DOM,UB=ze(),WB=UB.os.isiOS()&&UB.os.version.major<=12,GB=function(o){var e=o.getWin(),t=o.getDoc().documentElement,r=se(mu(e.innerWidth,e.innerHeight)),i=se(mu(t.offsetWidth,t.offsetHeight)),n=function(t){var n=r.get();n.left()===e.innerWidth&&n.top()===e.innerHeight||(r.set(mu(e.innerWidth,e.innerHeight)),Yv(o,t))},u=function(t){var n=o.getDoc().documentElement,e=i.get();e.left()===n.offsetWidth&&e.top()===n.offsetHeight||(i.set(mu(n.offsetWidth,n.offsetHeight)),Yv(o,t))},a=function(t){return n=t,o.fire("ScrollContent",n);var n};jB.bind(e,"resize",n),jB.bind(e,"scroll",a);var c=Db(fe.fromDom(o.getBody()),"load",u);o.on("NodeChange",u),o.on("remove",function(){c.unbind(),jB.unbind(e,"resize",n),jB.unbind(e,"scroll",a)})},XB=/* */Object.freeze({__proto__:null,render:function(e,o,t,n,r){var i=se(0);zB(e);var u,a,c,s=fe.fromDom(r.targetNode),l=(u=Mi(s),Bi(u)?u:fe.fromDom(pr(u).dom().body));a=s,c=o.mothership,$s(a,c,kr),Js(l,o.uiMothership),e.on("PostRender",function(){LB(e,o,t,n),i.set(e.getWin().innerWidth),MB.setMenubar(o.outerContainer,RB(e,t)),MB.setSidebar(o.outerContainer,t.sidebar),GB(e)});var f,d,m,g=MB.getSocket(o.outerContainer).getOrDie("Could not find expected socket element");if(!0===WB){Li(g.element(),{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var p=(f=function(){e.fire("ScrollContent")},d=20,m=null,{cancel:function(){null!==m&&(nt.clearTimeout(m),m=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null===m&&(m=nt.setTimeout(function(){f.apply(null,t),m=null},d))}});Bb(g.element(),"scroll",p.throttle)}tv(e,o),e.addCommand("ToggleSidebar",function(t,n){MB.toggleSidebar(o.outerContainer,n),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return MB.whichSidebar(o.outerContainer)});var h=Wh(e);return h!==Bp.sliding&&h!==Bp.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var t=e.getWin().innerWidth;t!==i.get()&&(MB.refreshToolbar(o.outerContainer),i.set(t))}),{iframeContainer:g.element().dom(),editorContainer:o.outerContainer.element().dom()}}}),YB=function(){return t=function(t){t.unbind()},n=se(st.none()),e=function(){n.get().each(t)},{clear:function(){e(),n.set(st.none())},isSet:function(){return n.get().isSome()},set:function(t){e(),n.set(st.some(t))}};var t,n,e},qB=function(t){return/^[0-9\.]+(|px)$/i.test(""+t)?st.some(parseInt(""+t,10)):st.none()},KB=function(t){return ot(t)?t+"px":t},JB=function(n,t,e){var o=t.filter(function(t){return n<t}),r=e.filter(function(t){return t<n});return o.or(r).getOr(n)},$B=function(t){var n,e,o,r;return(e=Mh(n=t),o=Rh(n),r=Ph(n),qB(e).map(function(t){return JB(t,o,r)})).getOr(Mh(t))},QB=function(t){var n=Fh(t),e=Ih(t),o=Vh(t);return qB(n).map(function(t){return JB(t,e,o)})},ZB=function(a,c,t,n,s){var e=t.uiMothership,l=t.outerContainer,o=ph.DOM,f=qh(a),d=Jh(a),m=Vh(a).or(QB(a)),r=n.shared.header,g=r.isPositionedAtTop,i=Wh(a),p=i===Bp.sliding||i===Bp.floating,u=se(!1),h=function(){return u.get()&&!a.removed},v=function(t){return p?t.fold(function(){return 0},function(t){return 1<t.components().length?lu(t.components()[1].element()):0}):0},b=function(){e.broadcastOn([ll()],{})},y=function(t){if(void 0===t&&(t=!1),h()){var n,e,o,r,i;if(f||(n=m.getOrThunk(function(){var t=qB(Ui(Vi(),"margin-left")).getOr(0);return bu(Vi())-pu(c).left()+t}),Ni(s.get().element(),"max-width",n+"px")),p&&MB.refreshToolbar(l),f||(e=MB.getToolbar(l),o=v(e),r=Au(c),i=g()?Math.max(r.y-lu(s.get().element())+o,0):r.bottom,Li(l.element(),{position:"absolute",top:Math.round(i)+"px",left:Math.round(r.x)+"px"})),d){var u=s.get();t?JE.reset(u):JE.refresh(u)}b()}},x=function(t){if(void 0===t&&(t=!0),!f&&d&&h()){var n=r.getDockingMode(),e=function(t){switch(Gh(a)){case jh.auto:var n=MB.getToolbar(l),e=v(n),o=lu(t.element())-e,r=Au(c);if(r.y>o)return"top";var i=hr(c),u=Math.max(i.dom().scrollHeight,lu(i));return r.bottom<u-o||Fu().bottom<r.bottom-o?"bottom":"top";case jh.bottom:return"bottom";case jh.top:default:return"top"}}(s.get());e!==n&&(function(t){var n=s.get();JE.setModes(n,[t]),r.setDockingMode(t);var e=g()?Yu.TopToBottom:Yu.BottomToTop;Fr(n.element(),Rc,e)}(e),t&&y(!0))}};return{isVisible:h,isPositionedAtTop:g,show:function(){u.set(!0),Ni(l.element(),"display","flex"),o.addClass(a.getBody(),"mce-edit-focus"),Yi(e.element(),"display"),x(!1),y()},hide:function(){u.set(!1),t.outerContainer&&(Ni(l.element(),"display","none"),o.removeClass(a.getBody(),"mce-edit-focus")),Ni(e.element(),"display","none")},update:y,updateMode:x,repositionPopups:b}},tD=function(t,n){var e=Au(t);return{pos:n?e.y:e.bottom,bounds:e}},nD=/* */Object.freeze({__proto__:null,render:function(n,e,o,r,t){var i=e.mothership,u=e.uiMothership,a=e.outerContainer,c=se(null),s=fe.fromDom(t.targetNode),l=ZB(n,s,e,r,c);NB(n);var f=function(){if(c.get())l.show();else{c.set(MB.getHeader(a).getOrDie());var t=Yh(n).getOr(Vi());Js(t,i),Js(t,u),LB(n,e,o,r),MB.setMenubar(a,RB(n,o)),l.show(),function(c,s,l){var f=se(tD(s,l.isPositionedAtTop())),n=function(t){var n=tD(s,l.isPositionedAtTop()),e=n.pos,o=n.bounds,r=f.get(),i=r.pos,u=r.bounds,a=o.height!==u.height||o.width!==u.width;f.set({pos:e,bounds:o}),a&&Yv(c,t),l.isVisible()&&(i!==e?l.update(!0):a&&(l.updateMode(),l.repositionPopups()))};c.on("activate",l.show),c.on("deactivate",l.hide),c.on("SkinLoaded ResizeWindow",function(){return l.update(!0)}),c.on("NodeChange keydown",function(t){$g.requestAnimationFrame(function(){return n(t)})}),c.on("ScrollWindow",function(){return l.updateMode()});var t=YB();t.set(Db(fe.fromDom(c.getBody()),"load",n)),c.on("remove",function(){t.clear()})}(n,s,l),n.nodeChanged()}};return n.on("focus",f),n.on("blur hide",l.hide),n.on("init",function(){n.hasFocus()&&f()}),tv(n,e),{editorContainer:a.element().dom()}}}),eD=function(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}},oD=function(t){var n,e;!function(e){vC.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t,n){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:sE(e,t.name),onAction:eD(e,t.name)})});for(var t=1;t<=6;t++){var n="h"+t;e.ui.registry.addToggleButton(n,{text:n.toUpperCase(),tooltip:"Heading "+t,onSetup:sE(e,n),onAction:eD(e,n)})}}(t),n=t,vC.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){n.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return n.execCommand(t.action)}})}),e=t,vC.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return e.execCommand(t.action)},onSetup:sE(e,t.name)})})},rD=function(t){var n;oD(t),n=t,vC.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){n.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:function(){return n.execCommand(t.action)}})}),n.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:eD(n,"code")})},iD=function(t,n,e){var o=function(){return!!n.undoManager&&n.undoManager[e]()},r=function(){t.setDisabled(n.mode.isReadOnly()||!o())};return t.setDisabled(!o()),n.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return n.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}},uD=function(t){var n,e;(n=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(t){return iD(t,n,"hasUndo")},onAction:function(){return n.execCommand("undo")}}),n.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(t){return iD(t,n,"hasRedo")},onAction:function(){return n.execCommand("redo")}}),(e=t).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(t){return iD(t,e,"hasUndo")},onAction:function(){return e.execCommand("undo")}}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(t){return iD(t,e,"hasRedo")},onAction:function(){return e.execCommand("redo")}})},aD=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p;r=YT(0,o=n,ZT(e=t)),e.ui.registry.addNestedMenuItem("align",{text:o.shared.providers.translate("Align"),getSubmenuItems:function(){return r.items.validateItems(r.getStyleItems())}}),a=YT(0,u=n,oE(i=t)),i.ui.registry.addNestedMenuItem("fontformats",{text:u.shared.providers.translate("Fonts"),getSubmenuItems:function(){return a.items.validateItems(a.getStyleItems())}}),c=t,l=et({type:"advanced"},(s=n).styleselect),f=YT(0,s,dE(c,l)),c.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return f.items.validateItems(f.getStyleItems())}}),m=YT(0,n,fE(d=t)),d.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return m.items.validateItems(m.getStyleItems())}}),p=YT(0,n,uE(g=t)),g.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return p.items.validateItems(p.getStyleItems())}})},cD=function(t,n){var e,o,r,i;!function(n){vC.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){n.ui.registry.addToggleButton(t.name,{tooltip:t.text,onAction:function(){return n.execCommand(t.cmd)},icon:t.icon,onSetup:sE(n,t.name)})});var t="alignnone",e="No alignment",o="JustifyNone",r="align-none";n.ui.registry.addButton(t,{tooltip:e,onAction:function(){return n.execCommand(o)},icon:r})}(t),rD(t),aD(t,n),uD(t),function(t){eb(t);var n=se(null),e=se(null);cb(t,"forecolor","forecolor","Text color",n),cb(t,"backcolor","hilitecolor","Background color",e),sb(t,"forecolor","forecolor","Text color"),sb(t,"backcolor","hilitecolor","Background color")}(t),(o=e=t).ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return o.execCommand("mceToggleVisualAid")}}),(r=e).ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(t){return function(n,t){n.setActive(t.hasVisual);var e=function(t){n.setActive(t.hasVisual)};return t.on("VisualAid",e),function(){return t.off("VisualAid",e)}}(t,r)},onAction:function(){r.execCommand("mceToggleVisualAid")}}),(i=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(t){return function(t,n){t.setDisabled(!n.queryCommandState("outdent"));var e=function(){t.setDisabled(!n.queryCommandState("outdent"))};return n.on("NodeChange",e),function(){return n.off("NodeChange",e)}}(t,i)},onAction:function(){return i.execCommand("outdent")}}),i.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return i.execCommand("indent")}})},sD=function(t,n){return{anchor:"makeshift",x:t,y:n}},lD=function(t){return"longpress"===t.type||0===t.type.indexOf("touch")},fD=function(t,n){var e,o,r,i=ph.DOM.getPos(t);return e=n,o=i.x,r=i.y,sD(e.x+o,e.y+r)},dD=function(t,n){return"contextmenu"===n.type||"longpress"===n.type?t.inline?function(t){if(lD(t)){var n=t.touches[0];return sD(n.pageX,n.pageY)}return sD(t.pageX,t.pageY)}(n):fD(t.getContentAreaContainer(),function(t){if(lD(t)){var n=t.touches[0];return sD(n.clientX,n.clientY)}return sD(t.clientX,t.clientY)}(n)):mD(t)},mD=function(t){return{anchor:"selection",root:fe.fromDom(t.selection.getNode())}},gD=function(t){return{anchor:"node",node:st.some(fe.fromDom(t.selection.getNode())),root:fe.fromDom(t.getBody())}},pD=function(t,n,e,o,r,i){var u,a,c=e(),s=(u=t,a=n,i?gD(u):dD(u,a));Lk(c,Ap.CLOSE_ON_EXECUTE,o,!1).map(function(t){n.preventDefault(),zg.showMenuAt(r,s,{menu:{markers:jp("normal")},data:t})})},hD={onLtr:function(){return[Va,Aa,Ma,Fa,Ia,Ra,Kg,Jg,Yg,Gg,qg,Xg]},onRtl:function(){return[Va,Ma,Aa,Ia,Fa,Ra,Kg,Jg,qg,Xg,Yg,Gg]}},vD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},bD=function(n,e,t,o,r,i,u){var a,c,s,l=(a=n,c=e,s=i?gD(a):dD(a,c),et({bubble:Ac(0,12,vD),layouts:hD,overrides:{maxWidthFunction:qO(),maxHeightFunction:_c()}},s));Lk(t,Ap.CLOSE_ON_EXECUTE,o,!0).map(function(t){e.preventDefault(),zg.showMenuWithinBounds(r,l,{menu:{markers:jp("normal"),highlightImmediately:u},data:t,type:"horizontal"},function(){return st.some(HT(n,o.shared))}),n.fire(KO)})},yD=function(n,e,o,r,i,u){var t=ze(),a=t.os.isiOS(),c=t.os.isOSX(),s=t.os.isAndroid(),l=t.deviceType.isTouch(),f=function(){var t=o();bD(n,e,t,r,i,u,!(s||a||c&&l))};if(!c&&!a||u)s&&!u&&n.selection.setCursorLocation(e.target,0),f();else{var d=function(){!function(t){var n=t.selection.getRng(),e=function(){$g.setEditorTimeout(t,function(){t.selection.setRng(n)},10),i()};t.once("touchend",e);var o=function(t){t.preventDefault(),t.stopImmediatePropagation()};t.on("mousedown",o,!0);var r=function(){return i()};t.once("longpresscancel",r);var i=function(){t.off("touchend",e),t.off("longpresscancel",r),t.off("mousedown",o)}}(n),f()};!function(t,n){var e=t.selection;if(e.isCollapsed()||n.touches.length<1)return!1;var o=n.touches[0],r=e.getRng();return us(t.getWin(),Gc.domRange(r)).exists(function(t){return t.left()<=o.clientX&&t.right()>=o.clientX&&t.top()<=o.clientY&&t.bottom()>=o.clientY})}(n,e)?(n.once("selectionchange",d),n.once("touchend",function(){return n.off("selectionchange",d)})):d()}},xD=function(t){return"string"==typeof t?t.split(/[ ,]/):t},wD=function(t){return t.getParam("contextmenu_never_use_native",!1,"boolean")},SD=function(t){return e="contextmenu",o="link linkchecker image imagetools table spellchecker configurepermanentpen",r=(n=t).ui.registry.getAll().contextMenus,st.from(n.getParam(e)).map(xD).getOrThunk(function(){return H(xD(o),function(t){return It(r,t)})});var n,e,o,r},kD=function(t){return w(t)?"|"===t:"separator"===t.type},CD={type:"separator"},OD=function(n){if(w(n))return n;switch(n.type){case"separator":return CD;case"submenu":return{type:"nestedmenuitem",text:n.text,icon:n.icon,getSubmenuItems:function(){var t=n.getSubmenuItems();return w(t)?t:V(t,OD)}};default:return{type:"menuitem",text:n.text,icon:n.icon,onAction:(t=n.onAction,function(){return t()})}}var t},_D=function(t,n){if(0===n.length)return t;var e=K(t).filter(function(t){return!kD(t)}).fold(function(){return[]},function(t){return[CD]});return t.concat(e).concat(n).concat([CD])},TD=function(c,t,o){var r=ze().deviceType.isTouch,i=uu(zg.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return c.focus()},onShow:function(){return o.setContextMenuState(!0)},onHide:function(){return o.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:Ka([$m("dismissContextMenu",[$o(Io(),function(t,n){cl.close(t),c.focus()})])])})),n=function(t){return zg.hide(i)},e=function(u){if(wD(c)&&u.preventDefault(),t=c,(!u.ctrlKey||wD(t))&&!1!==c.getParam("contextmenu")){var t,n,e,a=(n=c,"longpress"!==(e=u).type&&(2!==e.button||e.target===n.getBody()&&""===e.pointerType));(r()?yD:pD)(c,u,function(){var r,i,t,n=a?c.selection.getStart(!0):u.target,e=c.ui.registry.getAll(),o=SD(c);return r=e.contextMenus,i=n,0<(t=N(o,function(t,n){if(It(r,n)){var e=r[n].update(i);if(w(e))return _D(t,e.split(" "));if(0<e.length){var o=V(e,OD);return _D(t,o)}return t}return t.concat([n])},[])).length&&kD(t[t.length-1])&&t.pop(),t},o,i,a)}};c.on("init",function(){var t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(r()?"":" ResizeWindow");c.on(t,n),c.on("longpress contextmenu",e)})},ED=Vt([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),BD=function(n){return function(t){return t.translate(-n.left(),-n.top())}},DD=function(n){return function(t){return t.translate(n.left(),n.top())}},AD=function(e){return function(t,n){return N(e,function(t,n){return n(t)},mu(t,n))}},MD=function(t,n,e){return t.fold(AD([DD(e),BD(n)]),AD([BD(n)]),AD([]))},FD=function(t,n,e){return t.fold(AD([DD(e)]),AD([]),AD([DD(n)]))},ID=function(t,n,e){return t.fold(AD([]),AD([BD(e)]),AD([DD(n),BD(e)]))},RD=function(t,n,e){var o=t.fold(function(t,n){return{position:st.some("absolute"),left:st.some(t+"px"),top:st.some(n+"px")}},function(t,n){return{position:st.some("absolute"),left:st.some(t-e.left()+"px"),top:st.some(n-e.top()+"px")}},function(t,n){return{position:st.some("fixed"),left:st.some(t+"px"),top:st.some(n+"px")}});return et({right:st.none(),bottom:st.none()},o)},VD=function(t,i,u,a){var n=function(o,r){return function(t,n){var e=o(i,u,a);return r(t.getOr(e.left()),n.getOr(e.top()))}};return t.fold(n(ID,PD),n(FD,HD),n(MD,zD))},PD=ED.offset,HD=ED.absolute,zD=ED.fixed,ND=function(t,n){var e=Ir(t,n);return C(e)?NaN:parseInt(e,10)},LD=function(t,n,e,o){return r=n,i=t.element(),u=ND(i,r.leftAttr),a=ND(i,r.topAttr),(isNaN(u)||isNaN(a)?st.none():st.some(mu(u,a))).fold(function(){return e},function(t){return zD(t.left()+o.left(),t.top()+o.top())});var r,i,u,a},jD=function(t,n,e,o,r,i){var u,a,c,s=LD(t,n,e,o),l=(n.mustSnap?GD:XD)(t,n,s,r,i),f=MD(s,r,i);return u=n,a=f,c=t.element(),Fr(c,u.leftAttr,a.left()+"px"),Fr(c,u.topAttr,a.top()+"px"),l.fold(function(){return{coord:zD(f.left(),f.top()),extra:st.none()}},function(t){return{coord:t.output,extra:t.extra}})},UD=function(t,n){var e,o;e=n,o=t.element(),Pr(o,e.leftAttr),Pr(o,e.topAttr)},WD=function(t,l,f,d){return $(t,function(t){var n,e,o,r,i,u,a,c,s=t.sensor;return(n=l,e=s,o=t.range.left(),r=t.range.top(),a=FD(n,i=f,u=d),c=FD(e,i,u),Math.abs(a.left()-c.left())<=o&&Math.abs(a.top()-c.top())<=r)?st.some({output:VD(t.output,l,f,d),extra:t.extra}):st.none()})},GD=function(t,n,d,m,g){var e=n.getSnapPoints(t);return WD(e,d,m,g).orThunk(function(){return N(e,function(n,e){var t,o,r,i,u,a,c,s,l=e.sensor,f=(t=d,o=l,e.range.left(),e.range.top(),u=FD(t,r=m,i=g),a=FD(o,r,i),c=Math.abs(u.left()-a.left()),s=Math.abs(u.top()-a.top()),mu(c,s));return n.deltas.fold(function(){return{deltas:st.some(f),snap:st.some(e)}},function(t){return(f.left()+f.top())/2<=(t.left()+t.top())/2?{deltas:st.some(f),snap:st.some(e)}:n})},{deltas:st.none(),snap:st.none()}).snap.map(function(t){return{output:VD(t.output,d,m,g),extra:t.extra}})})},XD=function(t,n,e,o,r){var i=n.getSnapPoints(t);return WD(i,e,o,r)},YD=/* */Object.freeze({__proto__:null,snapTo:function(t,n,e,o){var r,i,u,a=n.getTarget(t.element());if(n.repositionTarget){var c=gr(t.element()),s=ku(c),l=ME(a),f=(i=s,u=l,{coord:VD((r=o).output,r.output,i,u),extra:r.extra}),d=RD(f.coord,0,l);ji(a,d)}}}),qD="data-initial-z-index",KD=function(t,n){var e;t.getSystem().addToGui(n),br((e=n).element()).filter(lr).each(function(n){Gi(n,"z-index").each(function(t){Fr(n,qD,t)}),Ni(n,"z-index",Ui(e.element(),"z-index"))})},JD=function(t){br(t.element()).filter(lr).each(function(n){Rr(n,qD).fold(function(){return Yi(n,"z-index")},function(t){return Ni(n,"z-index",t)}),Pr(n,qD)}),t.getSystem().removeFromGui(t)},$D=function(t,n,e){return t.getSystem().build(Zb.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[n]},events:e}))},QD=Zn("snaps",[Nn("getSnapPoints"),aa("onSensor"),Nn("leftAttr"),Nn("topAttr"),te("lazyViewport",Fu),te("mustSnap",!1)]),ZD=[te("useFixed",c),Nn("blockerClass"),te("getTarget",ct),te("onDrag",Z),te("repositionTarget",!0),te("onDrop",Z),ue("getBounds",Fu),QD],tA=function(n){return t=Gi(n,"left"),e=Gi(n,"top"),o=Gi(n,"position"),r=function(t,n,e){return("fixed"===e?zD:PD)(parseInt(t,10),parseInt(n,10))},(t.isSome()&&e.isSome()&&o.isSome()?st.some(r(t.getOrDie(),e.getOrDie(),o.getOrDie())):st.none()).getOrThunk(function(){var t=pu(n);return HD(t.left(),t.top())});var t,e,o,r},nA=function(e,t,i,u,a,c,n){var o,r,s,l,f,d,m,g,p,h=t.fold(function(){var t,e,o,n=(t=i,e=c.left(),o=c.top(),t.fold(function(t,n){return PD(t+e,n+o)},function(t,n){return HD(t+e,n+o)},function(t,n){return zD(t+e,n+o)})),r=MD(n,u,a);return zD(r.left(),r.top())},function(n){var t=jD(e,n,i,c,u,a);return t.extra.each(function(t){n.onSensor(e,t)}),t.coord});return o=h,r=u,s=a,f=(l=n).bounds,d=FD(o,r,s),m=yc(d.left(),f.x,f.x+f.width-l.width),g=yc(d.top(),f.y,f.y+f.height-l.height),p=HD(m,g),o.fold(function(){var t=ID(p,r,s);return PD(t.left(),t.top())},function(){return p},function(){var t=MD(p,r,s);return zD(t.left(),t.top())})},eA=function(t,n){return{bounds:t.getBounds(),height:fu(n.element()),width:yu(n.element())}},oA=function(n,e,t,o,r){var i=t.update(o,r),u=t.getStartData().getOrThunk(function(){return eA(e,n)});i.each(function(t){!function(t,n,e,o){var r=n.getTarget(t.element());if(n.repositionTarget){var i=gr(t.element()),u=ku(i),a=ME(r),c=tA(r),s=nA(t,n.snaps,c,u,a,o,e),l=RD(s,0,a);ji(r,l)}n.onDrag(t,r,o)}(n,e,u,t)})},rA=function(n,t,e,o){t.each(JD),e.snaps.each(function(t){UD(n,t)});var r=e.getTarget(n.element());o.reset(),e.onDrop(n,r)},iA=function(t){return function(n,e){var o=function(t){e.setStartData(eA(n,t))};return qo(b([$o(Do(),function(t){e.getStartData().each(function(){return o(t)})})],t(n,e,o)))}},uA=/* */Object.freeze({__proto__:null,getData:function(t){return st.from(mu(t.x(),t.y()))},getDelta:function(t,n){return mu(n.left()-t.left(),n.top()-t.top())}}),aA=function(a,c,s){return[$o(no(),function(n,t){if(0===t.event().raw().button){t.stop();var e,o=function(){return rA(n,st.some(u),a,c)},r=Fb(o,200),i={drop:o,delayDrop:r.schedule,forceDrop:o,move:function(t){r.cancel(),oA(n,a,c,uA,t)}},u=$D(n,a.blockerClass,(e=i,qo([$o(no(),e.forceDrop),$o(ro(),e.drop),$o(eo(),function(t,n){e.move(n.event())}),$o(oo(),e.delayDrop)])));s(n),KD(n,u)}})]},cA=b(ZD,[fa("dragger",{handlers:iA(aA)})]),sA=/* */Object.freeze({__proto__:null,getData:function(t){var n,e=t.raw().touches;return 1===e.length?(n=e[0],st.some(mu(n.clientX,n.clientY))):st.none()},getDelta:function(t,n){return mu(n.left()-t.left(),n.top()-t.top())}}),lA=function(u,a,c){var s=se(st.none());return[$o($e(),function(n,t){t.stop();var e,o=function(){rA(n,s.get(),u,a),s.set(st.none())},r={drop:o,delayDrop:function(){},forceDrop:o,move:function(t){oA(n,u,a,sA,t)}},i=$D(n,u.blockerClass,(e=r,qo([$o($e(),e.forceDrop),$o(Ze(),e.drop),$o(to(),e.drop),$o(Qe(),function(t,n){e.move(n.event())})])));s.set(st.some(i));c(n),KD(n,i)}),$o(Qe(),function(t,n){n.stop(),oA(t,u,a,sA,n.event())}),$o(Ze(),function(t,n){n.stop(),rA(t,s.get(),u,a),s.set(st.none())}),$o(to(),function(t){rA(t,s.get(),u,a),s.set(st.none())})]},fA=cA,dA=b(ZD,[fa("dragger",{handlers:iA(lA)})]),mA=b(ZD,[fa("dragger",{handlers:iA(function(t,n,e){return b(aA(t,n,e),lA(t,n,e))})})]),gA=Za({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,mouse:fA,touch:dA,mouseOrTouch:mA}),name:"dragging",active:{events:function(t,n){return t.dragger.handlers(t,n)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:st.from(t.extra)}}},state:/* */Object.freeze({__proto__:null,init:function(){var i=st.none(),n=st.none(),t=at({});return ai({readState:t,reset:function(){i=st.none(),n=st.none()},update:function(r,t){return r.getData(t).bind(function(t){return n=r,e=t,o=i.map(function(t){return n.getDelta(t,e)}),i=st.some(e),o;var n,e,o})},getStartData:function(){return n},setStartData:function(t){n=st.some(t)}})}}),apis:YD}),pA=function(t,r,i,u,n,e){return t.fold(function(){return gA.snap({sensor:HD(i-20,u-20),range:mu(n,e),output:HD(st.some(i),st.some(u)),extra:{td:r}})},function(t){var n=i-20,e=u-20,o=t.element().dom().getBoundingClientRect();return gA.snap({sensor:HD(n,e),range:mu(40,40),output:HD(st.some(i-o.width/2),st.some(u-o.height/2)),extra:{td:r}})})},hA=function(t,i,u){return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,n){var e,o,r=n.td;e=i.get(),o=r,e.exists(function(t){return je(t,o)})||(i.set(st.some(r)),u(r))},mustSnap:!0}},vA=function(t){return Zg(Qg.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:Ka([gA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),lx.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))},bA=function(c,e){var o=se([]),r=se([]),t=se(!1),i=se(st.none()),u=se(st.none()),n=function(t){var n=Mu(t);return pA(f.getOpt(e),t,n.x,n.y,n.width,n.height)},a=function(t){var n=Mu(t);return pA(d.getOpt(e),t,n.right,n.bottom,n.width,n.height)},s=hA(function(){return V(o.get(),function(t){return n(t)})},i,function(n){u.get().each(function(t){c.fire("TableSelectorChange",{start:n,finish:t})})}),l=hA(function(){return V(r.get(),function(t){return a(t)})},u,function(n){i.get().each(function(t){c.fire("TableSelectorChange",{start:t,finish:n})})}),f=vA(s),d=vA(l),m=uu(f.asSpec()),g=uu(d.asSpec()),p=function(t,n,e,o){var r=e(n);gA.snapTo(t,r);!function(t,n,e,o){var r=n.dom().getBoundingClientRect();Yi(t.element(),"display");var i=vr(fe.fromDom(c.getBody())).dom().innerHeight,u=e(r),a=o(r,i);(u||a)&&Ni(t.element(),"display","none")}(t,n,function(t){return t[o]<0},function(t,n){return t[o]>n})},h=function(t){return p(m,t,n,"top")},v=function(t){return p(g,t,a,"bottom")};ze().deviceType.isTouch()&&(c.on("TableSelectionChange",function(n){t.get()||(Gs(e,m),Gs(e,g),t.set(!0)),i.set(st.some(n.start)),u.set(st.some(n.finish)),n.otherCells.each(function(t){o.set(t.upOrLeftCells),r.set(t.downOrRightCells),h(n.start),v(n.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){i.get().each(h),u.get().each(v)}),c.on("TableSelectionClear",function(){t.get()&&(qs(m),qs(g),t.set(!1)),i.set(st.none()),u.set(st.none())}))};(JT=KT=KT||{})[JT.None=0]="None",JT[JT.Both=1]="Both",JT[JT.Vertical=2]="Vertical";var yA,xA=function(t,n,e){var o,r,i,u,a,c,s=fe.fromDom(t.getContainer()),l=(o=t,r=n,i=e,u=lu(s),a=bu(s),(c={}).height=JB(u+r.top(),Rh(o),Ph(o)),i===KT.Both&&(c.width=JB(a+r.left(),Ih(o),Vh(o))),c);_t(l,function(t,n){return Ni(s,n,KB(t))}),t.fire("ResizeEditor")},wA=function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1},SA=function(i,u,a){u.delimiter||(u.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:Ka([Gm.config({mode:"flow",selector:"div[role=button]"}),Dh.config({disabled:a.isReadOnly}),nv(),uy.config({}),Jm.config({}),$m("elementPathEvents",[rr(function(r,t){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return Gm.focusIn(r)}),i.on("NodeChange",function(t){var n,o,e=function(t){for(var n=[],e=t.length;0<e--;){var o=t[e];if(1===o.nodeType&&!wA(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||n.push({name:r.name,element:o}),r.isPropagationStopped())break}}return n}(t.parents);0<e.length?Jm.set(r,(n=V(e||[],function(n,t){return Qg.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:n.name},action:function(t){i.focus(),i.selection.select(n.element),i.nodeChanged()},buttonBehaviours:Ka([ov(a.isReadOnly),nv()])})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+u.delimiter+" "}},N(n.slice(1),function(t,n){var e=t;return e.push(o),e.push(n),e},[n[0]]))):Jm.set(r,[])})})])]),components:[]}},kA=function(i,u){var t,n,e,o,r,a;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(r=function(){var t,o,r,n,e=[];return i.getParam("elementpath",!0,"boolean")&&e.push(SA(i,{},u)),Be(i.getParam("plugins","","string"),"wordcount")&&e.push((t=i,o=u,r=function(t,n,e){return Jm.set(t,[ou(o.translate(["{0} "+e,n[e]]))])},Qg.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:Ka([ov(o.isReadOnly),nv(),uy.config({}),Jm.config({}),El.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),$m("wordcount-events",[ar(function(t){var n=El.getValue(t),e="words"===n.mode?"characters":"words";El.setValue(t,{mode:e,count:n.count}),r(t,n.count,e)}),rr(function(e){t.on("wordCountUpdate",function(t){var n=El.getValue(e).mode;El.setValue(e,{mode:n,count:t.wordCount}),r(e,t.wordCount,n)})})])]),eventOrder:{"alloy.execute":["disabling","alloy.base.behaviour","wordcount-events"]}}))),i.getParam("branding",!0,"boolean")&&e.push({dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+(n=hh.translate(["Powered by {0}","Tiny"]))+'">'+n+"</a>"}}),0<e.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:e}]:[]}(),n=!Be((t=i).getParam("plugins","","string"),"autoresize"),(a=!1===(e=t.getParam("resize",n))?KT.None:"both"===e?KT.Both:KT.Vertical)!==KT.None&&r.push((o=a,{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:u.translate("Resize"),"aria-hidden":"true"},innerHtml:np("resize-handle",u.icons)},behaviours:Ka([gA.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,n,e){xA(i,e,o)},blockerClass:"tox-blocker"})])})),r)}},CA=function(x){var t,n=x.inline,w=n?nD:XB,S=Jh(x)?rB:cB,e=st.none(),o=ze(),r=o.browser.isIE()?["tox-platform-ie"]:[],i=o.deviceType.isTouch()?["tox-platform-touch"]:[],u=Xh(x),a=hh.isRtl()?{attributes:{dir:"rtl"}}:{},c={attributes:((t={})[Rc]=u?Yu.BottomToTop:Yu.TopToBottom,t)},k=function(){return e.bind(MB.getHeader)},C=uu({dom:et({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(r).concat(i)},a),behaviours:Ka([Ns.config({useFixed:function(){return S.isDocked(k)}})])}),O=function(){return ut.value(C)},s=Zg({dom:{tag:"div",classes:["tox-anchorbar"]}}),_=function(){return e.bind(function(t){return MB.getThrobber(t)}).getOrDie("Could not find throbber element")},T=YO(C,x,function(){return e.bind(function(t){return s.getOpt(t)}).getOrDie("Could not find a anchor bar element")}),l=MB.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:T,onEscape:function(){x.focus()}}),E=Wh(x),f=MB.parts().toolbar(et({dom:{tag:"div",classes:["tox-toolbar"]},getSink:O,providers:T.shared.providers,onEscape:function(){x.focus()},type:E,lazyToolbar:function(){return e.bind(function(t){return MB.getToolbar(t)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return k().getOrDie("Could not find header element")}},c)),d=MB.parts()["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:T.shared.providers,onEscape:function(){},type:E}),m=MB.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),g=MB.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),p=MB.parts().throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:T}),h=x.getParam("statusbar",!0,"boolean")&&!n?st.some(kA(x,T.shared.providers)):st.none(),v={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[m,g]},b=Lh(x),y=zh(x),B=Hh(x),D=MB.parts().header({dom:et({tag:"div",classes:["tox-editor-header"]},c),components:it([B?[l]:[],b?[d]:y?[f]:[],qh(x)?[]:[s.asSpec()]]),sticky:Jh(x),editor:x,sharedBackstage:T.shared}),A=it([u?[]:[D],n?[]:[v],u?[D]:[]]),M=it([[{dom:{tag:"div",classes:["tox-editor-container"]},components:A}],n?[]:h.toArray(),[p]]),F=Kh(x),I=et(et({role:"application"},hh.isRtl()?{dir:"rtl"}:{}),F?{"aria-hidden":"true"}:{}),R=uu(MB.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(n?["tox-tinymce-inline"]:[]).concat(u?["tox-tinymce--toolbar-bottom"]:[]).concat(i).concat(r),styles:et({visibility:"hidden"},F?{opacity:"0",border:"0"}:{}),attributes:I},components:M,behaviours:Ka([Gm.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])}));e=st.some(R),x.shortcuts.add("alt+F9","focus menubar",function(){MB.focusMenubar(R)}),x.shortcuts.add("alt+F10","focus toolbar",function(){MB.focusToolbar(R)});var V,P,H,z,N,L,j,U,W,G,X,Y,q,K,J,$,Q=ty(R),Z=ty(C);V=x,P=Q,H=Z,z=function(n,e){rt([P,H],function(t){t.broadcastEvent(n,e)})},N=function(n,e){rt([P,H],function(t){t.broadcastOn([n],e)})},L=function(t){return N(sl(),{target:t.target()})},j=Bb(fe.fromDom(nt.document),"touchstart",L),U=Bb(fe.fromDom(nt.document),"touchmove",function(t){return z(Eo(),t)}),W=Bb(fe.fromDom(nt.document),"touchend",function(t){return z(Bo(),t)}),G=Bb(fe.fromDom(nt.document),"mousedown",L),X=Bb(fe.fromDom(nt.document),"mouseup",function(t){0===t.raw().button&&N(fl(),{target:t.target()})}),Y=function(t){return N(sl(),{target:fe.fromDom(t.target)})},q=function(t){0===t.button&&N(fl(),{target:fe.fromDom(t.target)})},K=function(t){return z(Do(),Ab(t))},J=function(t){N(ll(),{}),z(Ao(),Ab(t))},$=function(){return N(ll(),{})},V.on("PostRender",function(){V.on("click",Y),V.on("tap",Y),V.on("mouseup",q),V.on("ScrollWindow",K),V.on("ResizeWindow",J),V.on("ResizeEditor",$)}),V.on("remove",function(){V.off("click",Y),V.off("tap",Y),V.off("mouseup",q),V.off("ScrollWindow",K),V.off("ResizeWindow",J),V.off("ResizeEditor",$),G.unbind(),j.unbind(),U.unbind(),W.unbind(),X.unbind()}),V.on("detach",function(){Qs(P),Qs(H),P.destroy(),H.destroy()});var tt=function(){var t,n=KB($B(x)),e=KB(QB(t=x).getOr(Fh(t)));return x.inline||(Xi("div","width",e)&&Ni(R.element(),"width",e),Xi("div","height",n)?Ni(R.element(),"height",n):Ni(R.element(),"height","200px")),n};return{mothership:Q,uiMothership:Z,backstage:T,renderUI:function(){var o,r,e,n,i,u,a,c;S.setup(x,T.shared,k),cD(x,T),TD(x,O,T),r=(o=x).ui.registry.getAll().sidebars,rt(Ct(r),function(n){var t=r[n],e=function(){return st.from(o.queryCommandValue("ToggleSidebar")).is(n)};o.ui.registry.addToggleButton(n,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,n),t.setActive(e())},onSetup:function(t){var n=function(){return t.setActive(e())};return o.on("ToggleSidebar",n),function(){o.off("ToggleSidebar",n)}}})}),e=x,n=_,i=T.shared,u=se(!1),a=se(st.none()),c=function(t){t!==u.get()&&(CB(n(),t,i.providers),u.set(t))},e.on("ProgressState",function(t){if(a.get().each($g.clearTimeout),ot(t.time)){var n=$g.setEditorTimeout(e,function(){return c(t.state)},t.time);a.set(st.some(n))}else c(t.state),a.set(st.none())}),Tt(x.getParam("toolbar_groups",{},"object"),function(t,n){x.ui.registry.addGroupToolbarButton(n,t)});var t,s=x.ui.registry.getAll(),l=s.buttons,f=s.menuItems,d=s.contextToolbars,m=s.sidebars,g=Nh(x),p={menuItems:f,menus:(t=x.getParam("menu"))?Tt(t,function(t){return et(et({},t),{items:t.items})}):{},menubar:x.getParam("menubar"),toolbar:g.getOrThunk(function(){return x.getParam("toolbar",!0)}),allowToolbarGroups:E===Bp.floating,buttons:l,sidebar:m};CE(x,d,C,{backstage:T}),bA(x,C);var h=x.getElement(),v=tt(),b={mothership:Q,uiMothership:Z,outerContainer:R},y={targetNode:h,height:v};return w.render(x,b,p,T,y)},getUi:function(){return{channels:{broadcastAll:Z.broadcast,broadcastOn:Z.broadcastOn,register:function(){}}}}}},OA=at([Nn("lazySink"),qn("dragBlockClass"),ue("getBounds",Fu),te("useTabstopAt",at(!0)),te("eventOrder",{}),Bl("modalBehaviours",[Gm]),ca("onExecute"),la("onEscape")]),_A={sketch:ct},TA=at([af({name:"draghandle",overrides:function(t,n){return{behaviours:Ka([gA.config({mode:"mouse",getTarget:function(t){return Nu(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(n,null,2)).message),getBounds:t.getDragBounds})])}}}),rf({schema:[Nn("dom")],name:"title"}),rf({factory:_A,schema:[Nn("dom")],name:"close"}),rf({factory:_A,schema:[Nn("dom")],name:"body"}),af({factory:_A,schema:[Nn("dom")],name:"footer"}),uf({factory:{sketch:function(t,n){return et(et({},t),{dom:n.dom,components:n.components})}},schema:[te("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),te("components",[])],name:"blocker"})]),EA=Vf({name:"ModalDialog",configFields:OA(),partFields:TA(),factory:function(a,t,n,o){var r=Wr("alloy.dialog.busy"),c=Wr("alloy.dialog.idle"),s=Ka([Gm.config({mode:"special",onTab:function(){return st.some(!0)},onShiftTab:function(){return st.some(!0)}}),eg.config({})]),e=Wr("modal-events"),i=et(et({},a.eventOrder),{"alloy.system.attached":[e].concat(a.eventOrder["alloy.system.attached"]||[])});return{uid:a.uid,dom:a.dom,components:t,apis:{show:function(i){var t=a.lazySink(i).getOrDie(),u=se(st.none()),n=o.blocker(),e=t.getSystem().build(et(et({},n),{components:n.components.concat([au(i)]),behaviours:Ka([eg.config({}),$m("dialog-blocker-events",[or(uo(),function(){Gm.focusIn(i)}),$o(c,function(t,n){Vr(i.element(),"aria-busy")&&(Pr(i.element(),"aria-busy"),u.get().each(function(t){return Jm.remove(i,t)}))}),$o(r,function(t,n){Fr(i.element(),"aria-busy","true");var e=n.event().getBusySpec();u.get().each(function(t){Jm.remove(i,t)});var o=e(i,s),r=t.getSystem().build(o);u.set(st.some(r)),Jm.append(i,au(r)),r.hasConfigured(Gm)&&Gm.focusIn(r)})])])}));Gs(t,e),Gm.focusIn(i)},hide:function(n){br(n.element()).each(function(t){n.getSystem().getByDom(t).each(function(t){qs(t)})})},getBody:function(t){return xf(t,a,"body")},getFooter:function(t){return xf(t,a,"footer")},setIdle:function(t){jo(t,c)},setBusy:function(t,n){Uo(t,r,{getBusySpec:n})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Al(a.modalBehaviours,[Jm.config({}),Gm.config({mode:"cyclic",onEnter:a.onExecute,onEscape:a.onEscape,useTabstopAt:a.useTabstopAt}),$m(e,[rr(function(t){var n,e,o,r,i,u;n=t.element(),e=xf(t,a,"title").element(),o=st.from(Ir(n,"id")).fold(function(){var t=Wr("dialog-label");return Fr(e,"id",t),t},ct),Fr(n,"aria-labelledby",o),r=t.element(),i=xf(t,a,"body").element(),u=st.from(Ir(r,"id")).fold(function(){var t=Wr("dialog-describe");return Fr(i,"id",t),t},ct),Fr(r,"aria-describedby",u)})])])}},apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),BA=fn([jn("type"),jn("name")].concat(Qp)),DA=Rn,AA=[vn("name","name",Xt(function(){return Wr("button-name")}),In),$n("icon"),re("align","end",["start","end"]),ie("primary",!1),ie("disabled",!1)],MA=b(AA,[jn("text")]),FA=b([Un("type",["submit","cancel","custom"])],MA),IA=b([Un("type",["menu"]),$n("text"),$n("tooltip"),$n("icon"),Yn("items",BA)],AA),RA=Dn("type",{submit:FA,cancel:FA,custom:FA,menu:IA}),VA=[jn("type"),jn("text"),Un("level",["info","warn","error","success"]),jn("icon"),te("url","")],PA=fn(VA),HA=[jn("type"),jn("text"),ie("disabled",!1),ie("primary",!1),vn("name","name",Xt(function(){return Wr("button-name")}),In),$n("icon"),ie("borderless",!1)],zA=fn(HA),NA=[jn("type"),jn("name"),jn("label"),ie("disabled",!1)],LA=fn(NA),jA=Rn,UA=[jn("type"),jn("name")],WA=UA.concat([$n("label")]),GA=fn(WA),XA=In,YA=fn(WA),qA=In,KA=fn(WA),JA=dn(xn),$A=function(t){return[jn("type"),Ln("columns",Fn),t]},QA=WA.concat([ie("sandboxed",!0)]),ZA=fn(QA),tM=In,nM=WA.concat([$n("inputMode"),$n("placeholder"),ie("maximized",!1),ie("disabled",!1)]),eM=fn(nM),oM=In,rM=WA.concat([Xn("items",[jn("text"),jn("value")]),ee("size",1),ie("disabled",!1)]),iM=fn(rM),uM=In,aM=WA.concat([ie("constrain",!0),ie("disabled",!1)]),cM=fn(aM),sM=fn([jn("width"),jn("height")]),lM=WA.concat([$n("placeholder"),ie("maximized",!1),ie("disabled",!1)]),fM=fn(lM),dM=In,mM=WA.concat([re("filetype","file",["image","media","file"]),te("disabled",!1)]),gM=fn(mM),pM=fn([jn("value"),te("meta",{})]),hM=UA.concat([oe("tag","textarea"),jn("scriptId"),jn("scriptUrl"),(yA=undefined,ne("settings",yA,Hn))]),vM=UA.concat([oe("tag","textarea"),Wn("init")]),bM=kn(function(t){return On("customeditor.old",ln(vM),t).orThunk(function(){return On("customeditor.new",ln(hM),t)})}),yM=In,xM=[jn("type"),jn("html"),re("presets","presentation",["presentation","document"])],wM=fn(xM),SM=WA.concat([Ln("currentState",fn([Nn("blob"),jn("url")]))]),kM=fn(SM),CM=WA.concat([te("columns","auto")]),OM=fn(CM),_M=wn([jn("value"),jn("text"),jn("icon")]),TM=[jn("type"),Yn("header",In),Yn("cells",dn(In))],EM=fn(TM),BM=function(n){return vn("items","items",Wt(),dn(kn(function(t){return On("Checking item of "+n,DM,t).fold(function(t){return ut.error(En(t))},function(t){return ut.value(t)})})))},DM=Sn(function(){return Bn("type",{alertbanner:PA,bar:fn((n=BM("bar"),[jn("type"),n])),button:zA,checkbox:LA,colorinput:GA,colorpicker:YA,dropzone:KA,grid:fn($A(BM("grid"))),iframe:ZA,input:eM,selectbox:iM,sizeinput:cM,textarea:fM,urlinput:gM,customeditor:bM,htmlpanel:wM,imagetools:kM,collection:OM,label:fn((t=BM("label"),[jn("type"),jn("label"),t])),table:EM,panel:MM});var t,n}),AM=[jn("type"),te("classes",[]),Yn("items",DM)],MM=fn(AM),FM=[vn("name","name",Xt(function(){return Wr("tab-name")}),In),jn("title"),Yn("items",DM)],IM=[jn("type"),Xn("tabs",FM)],RM=fn(IM),VM=MA,PM=RA,HM=fn([jn("title"),Ln("body",Bn("type",{panel:MM,tabpanel:RM})),oe("size","normal"),Yn("buttons",PM),te("initialData",{}),ue("onAction",Z),ue("onChange",Z),ue("onSubmit",Z),ue("onClose",Z),ue("onCancel",Z),te("onTabChange",Z)]),zM=function(t){return S(t)?[t].concat(U(Mt(t),zM)):v(t)?U(t,zM):[]},NM=function(t){return w(t.type)&&w(t.name)},LM={checkbox:jA,colorinput:XA,colorpicker:qA,dropzone:JA,input:oM,iframe:tM,sizeinput:sM,selectbox:uM,size:sM,textarea:dM,urlinput:pM,customeditor:yM,collection:_M,togglemenuitem:DA},jM=function(t){var n=H(zM(t),NM),e=U(n,function(n){return t=n,st.from(LM[t.type]).fold(function(){return[]},function(t){return[Ln(n.name,t)]});var t});return fn(e)},UM=fn(b([Un("type",["cancel","custom"])],VM)),WM=fn([jn("title"),jn("url"),Jn("height"),Jn("width"),Kn("buttons",dn(UM)),ue("onAction",Z),ue("onCancel",Z),ue("onClose",Z),ue("onMessage",Z)]),GM=function(t){return{internalDialog:_n(On("dialog",HM,t)),dataValidator:jM(t),initialData:t.initialData}},XM={open:function(t,n){var e=GM(n);return t(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(t,n){return t(_n(On("dialog",WM,n)))},redial:function(t){return GM(t)}},YM=function(t){var e=[],o={};return _t(t,function(t,n){t.fold(function(){e.push(n)},function(t){o[n]=t})}),0<e.length?ut.error(e):ut.value(o)},qM=Rf({name:"TabButton",configFields:[te("uid",undefined),Nn("value"),vn("dom","dom",Yt(function(){return{attributes:{role:"tab",id:Wr("aria"),"aria-selected":"false"}}}),An()),qn("action"),te("domModification",{}),Bl("tabButtonBehaviours",[eg,Gm,El]),Nn("view")],factory:function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:hg(t.action),behaviours:Al(t.tabButtonBehaviours,[eg.config({}),Gm.config({mode:"execution",useSpace:!0,useEnter:!0}),El.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),KM=at([Nn("tabs"),Nn("dom"),te("clickToDismiss",!1),Bl("tabbarBehaviours",[Qf,Gm]),ia(["tabClass","selectedClass"])]),JM=cf({factory:qM,name:"tabs",unit:"tab",overrides:function(o){var r=function(t,n){Qf.dehighlight(t,n),Uo(t,zo(),{tabbar:t,button:n})},i=function(t,n){Qf.highlight(t,n),Uo(t,Ho(),{tabbar:t,button:n})};return{action:function(t){var n=t.getSystem().getByUid(o.uid).getOrDie(),e=Qf.isHighlighted(n,t);(e&&o.clickToDismiss?r:e?Z:i)(n,t)},domModification:{classes:[o.markers.tabClass]}}}}),$M=at([JM]),QM=Vf({name:"Tabbar",configFields:KM(),partFields:$M(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Al(t.tabbarBehaviours,[Qf.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){Fr(n.element(),"aria-selected","true")},onDehighlight:function(t,n){Fr(n.element(),"aria-selected","false")}}),Gm.config({mode:"flow",getInitial:function(t){return Qf.getHighlighted(t).map(function(t){return t.element()})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),ZM=Rf({name:"Tabview",configFields:[Bl("tabviewBehaviours",[Jm])],factory:function(t,n){return{uid:t.uid,dom:t.dom,behaviours:Al(t.tabviewBehaviours,[Jm.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),tF=at([te("selectFirst",!0),aa("onChangeTab"),aa("onDismissTab"),te("tabs",[]),Bl("tabSectionBehaviours",[])]),nF=rf({factory:QM,schema:[Nn("dom"),Gn("markers",[Nn("tabClass"),Nn("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),eF=rf({factory:ZM,name:"tabview"}),oF=at([nF,eF]),rF=Vf({name:"TabSection",configFields:tF(),partFields:oF(),factory:function(i,t,n,e){var o=function(t,n){yf(t,i,"tabbar").each(function(t){n(t).each(Wo)})};return{uid:i.uid,dom:i.dom,components:t,behaviours:Dl(i.tabSectionBehaviours),events:qo(it([i.selectFirst?[rr(function(t,n){o(t,Qf.getFirst)})]:[],[$o(Ho(),function(t,n){var o,r,e=n.event().button();o=e,r=El.getValue(o),yf(o,i,"tabview").each(function(e){L(i.tabs,function(t){return t.value===r}).each(function(t){var n=t.view();Rr(o.element(),"id").each(function(t){Fr(e.element(),"aria-labelledby",t)}),Jm.set(e,n),i.onChangeTab(e,o,n)})})}),$o(zo(),function(t,n){var e=n.event().button();i.onDismissTab(t,e)})]])),apis:{getViewItems:function(t){return yf(t,i,"tabview").map(function(t){return Jm.contents(t)}).getOr([])},showTab:function(t,e){o(t,function(n){var t=Qf.getCandidates(n);return L(t,function(t){return El.getValue(t)===e}).filter(function(t){return!Qf.isHighlighted(n,t)})})}}}},apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),iF=function(t,n){Ni(t,"height",n+"px"),ze().browser.isIE()?Yi(t,"flex-basis"):Ni(t,"flex-basis",n+"px")},uF=function(t,o,r){Nu(t,'[role="dialog"]').each(function(e){Lu(e,'[role="tablist"]').each(function(n){r.get().map(function(t){return Ni(o,"height","0"),Ni(o,"flex-basis","0"),Math.min(t,function(t,n,e){var o,r=hr(t).dom(),i=Nu(t,".tox-dialog-wrap").getOr(t);o="fixed"===Ui(i,"position")?Math.max(r.clientHeight,nt.window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight);var u=lu(n),a=n.dom().offsetLeft>=e.dom().offsetLeft+bu(e)?Math.max(lu(e),u):u,c=parseInt(Ui(t,"margin-top"),10)||0,s=parseInt(Ui(t,"margin-bottom"),10)||0;return o-(lu(t)+c+s-a)}(e,o,n))}).each(function(t){iF(o,t)})})})},aF=function(t){return Lu(t,'[role="tabpanel"]')},cF=function(a){var c;return{smartTabHeight:(c=se(st.none()),{extraEvents:[rr(function(t){var e=t.element();aF(e).each(function(u){var n;Ni(u,"visibility","hidden"),t.getSystem().getByDom(u).toOption().each(function(t){var o,r,i,n=(r=u,i=t,V(o=a,function(t,n){Jm.set(i,o[n].view());var e=r.dom().getBoundingClientRect();return Jm.set(i,[]),e.height})),e=q(Y(n,function(t,n){return n<t?-1:t<n?1:0}));c.set(e)}),uF(e,u,c),Yi(u,"visibility"),n=t,q(a).each(function(t){return rF.showTab(n,t.value)}),$g.requestAnimationFrame(function(){uF(e,u,c)})})}),$o(Ao(),function(t){var n=t.element();aF(n).each(function(t){uF(n,t,c)})}),$o(yy,function(t,n){var r=t.element();aF(r).each(function(n){var t=uc();Ni(n,"visibility","hidden");var e=Gi(n,"height").map(function(t){return parseInt(t,10)});Yi(n,"height"),Yi(n,"flex-basis");var o=n.dom().getBoundingClientRect().height;e.forall(function(t){return t<o})?(c.set(st.from(o)),uF(r,n,c)):e.each(function(t){iF(n,t)}),Yi(n,"visibility"),t.each(ic)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}},sF="send-data-to-section",lF="send-data-to-view",fF=Wr("update-dialog"),dF=Wr("update-title"),mF=Wr("update-body"),gF=Wr("update-footer"),pF=Wr("body-send-message"),hF=function(t,n,d,e){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:et(et({},n.map(function(t){return{id:t}}).getOr({})),e?{"aria-live":"polite"}:{})},components:[],behaviours:Ka([dS(0),vT.config({channel:mF,updateState:function(t,n){return st.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},renderComponents:function(t){switch(t.body.type){case"tabpanel":return[(r=t.body,i=d,u=se({}),a=function(t){var n=El.getValue(t),e=YM(n).getOr({}),o=u.get(),r=zt(o,e);u.set(r)},c=function(t){var n=u.get();El.setValue(t,n)},s=se(null),l=V(r.tabs,function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:i.shared.providers.translate(t.title)},view:function(){return[eS.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"]},components:V(t.items,function(t){return cO(n,t,i)}),formBehaviours:Ka([Gm.config({mode:"acyclic",useTabstopAt:x(DS)}),$m("TabView.form.events",[rr(c),ir(a)]),oc.config({channels:$t([{key:sF,value:{onReceive:a}},{key:lF,value:{onReceive:c}}])})])}})]}}}),f=cF(l).smartTabHeight,rF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=El.getValue(n);Uo(t,by,{name:o,oldName:s.get()}),s.set(o)},tabs:l,components:[rF.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[QM.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:Ka([uy.config({})])}),rF.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:f.selectFirst,tabSectionBehaviours:Ka([$m("tabpanel",f.extraEvents),Gm.config({mode:"acyclic"}),Lf.config({find:function(t){return q(rF.getViewItems(t))}}),El.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([sF],{}),u.get()},setValue:function(t,n){u.set(n),t.getSystem().broadcastOn([lF],{})}}})])}))];default:return[(e=t.body,o=d,{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[(n=Zg(eS.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:V(e.items,function(t){return cO(n,t,o)})}}))).asSpec()]}],behaviours:Ka([Gm.config({mode:"acyclic",useTabstopAt:x(DS)}),fS(n),yS(n,{postprocess:function(t){return YM(t).fold(function(t){return nt.console.error(t),{}},function(t){return t})}})])})]}var e,o,n,r,i,u,a,c,s,l,f},initialData:t})])}},vF=mv.deviceType.isTouch(),bF=function(t,n){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,n]}},yF=function(t,n){return EA.parts().close(Qg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:Ka([uy.config({})])}))},xF=function(){return EA.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})},wF=function(t,n){return EA.parts().body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:kB("<p>"+n.translate(t)+"</p>")}]}]})},SF=function(t){return EA.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})},kF=function(t,n){return[Zb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),Zb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]},CF=function(n){var t,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return EA.sketch({lazySink:n.lazySink,onEscape:function(t){return n.onEscape(t),st.some(!0)},useTabstopAt:function(t){return!DS(t)},dom:{tag:"div",classes:[e].concat(n.extraClasses),styles:et({position:"relative"},n.extraStyles)},components:b([n.header,n.body],n.footer.toArray()),parts:{blocker:{dom:kB('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:vF?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:Ka(b([eg.config({}),$m("dialog-events",n.dialogEvents.concat([or(uo(),function(t,n){Gm.focusIn(t)})])),$m("scroll-lock",[rr(function(){Si(Vi(),i)}),ir(function(){Ci(Vi(),i)})])],n.extraBehaviours)),eventOrder:et(((t={})[wo()]=["dialog-events"],t[Mo()]=["scroll-lock","dialog-events","alloy.base.behaviour"],t[Fo()]=["alloy.base.behaviour","dialog-events","scroll-lock"],t),n.eventOrder)})},OF=function(t){return Qg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(t){jo(t,my)}})},_F=function(t,n,e){var o=function(t){return[ou(e.translate(t.title))]};return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:et({},n.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:Ka([vT.config({channel:dF,renderComponents:o})])}},TF=function(){return{dom:kB('<div class="tox-dialog__draghandle"></div>')}},EF=function(t,n){return e={title:n.shared.providers.translate(t),draggable:n.dialog.isDraggableModal()},o=n.shared.providers,r=EA.parts().title(_F(e,st.none(),o)),i=EA.parts().draghandle(TF()),u=EA.parts().close(OF(o)),a=[r].concat(e.draggable?[i]:[]).concat([u]),Zb.sketch({dom:kB('<div class="tox-dialog__header"></div>'),components:a});var e,o,r,i,u,a},BF=function(t,n){return{onClose:function(){return n.closeWindow()},onBlock:function(e){EA.setBusy(t(),function(t,n){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:kB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){EA.setIdle(t())}}},DF=function(t,n,e,o){var r;return uu(CF(et(et({},t),{lazySink:o.shared.getSink,extraBehaviours:b([vT.config({channel:fF,updateState:function(t,n){return st.some(n)},initialData:n}),SS({})],t.extraBehaviours),onEscape:function(t){jo(t,my)},dialogEvents:e,eventOrder:((r={})[xo()]=["reflecting","receiving"],r[Mo()]=["scroll-lock","reflecting","messages","dialog-events","alloy.base.behaviour"],r[Fo()]=["alloy.base.behaviour","dialog-events","messages","reflecting","scroll-lock"],r)})))},AF=function(t){return V(t,function(t){return"menu"===t.type?(e=V((n=t).items,function(t){var n=se(!1);return et(et({},t),{storage:n})}),et(et({},n),{items:e})):t;var n,e})},MF=function(t){return N(t,function(t,n){return"menu"!==n.type?t:N(n.items,function(t,n){return t[n.name]=n.storage,t},t)},{})},FF=function(t,e){return[nr(uo(),BS),t(dy,function(t,n){e.onClose(),n.onClose()}),t(my,function(t,n,e,o){n.onCancel(t),jo(o,dy)}),$o(vy,function(t,n){return e.onUnblock()}),$o(hy,function(t,n){return e.onBlock(n.event())})]},IF=function(i,t){var n=function(t,r){return $o(t,function(e,o){u(e,function(t,n){r(i(),t,o.event(),e)})})},u=function(n,e){vT.getState(n).get().each(function(t){e(t,n)})};return b(FF(n,t),[n(gy,function(t,n,e){n.onAction(t,{name:e.name()})})])},RF=function(i,t,a){var n=function(t,r){return $o(t,function(e,o){u(e,function(t,n){r(i(),t,o.event(),e)})})},u=function(n,e){vT.getState(n).get().each(function(t){e(t.internalDialog,n)})};return b(FF(n,t),[n(py,function(t,n){return n.onSubmit(t)}),n(fy,function(t,n,e){n.onChange(t,{name:e.name()})}),n(gy,function(t,n,e,o){var r=function(){return Gm.focusIn(o)},i=function(t){return Vr(t,"disabled")||Rr(t,"aria-disabled").exists(function(t){return"true"===t})},u=uc();n.onAction(t,{name:e.name(),value:e.value()}),uc().fold(r,function(n){i(n)||u.exists(function(t){return We(n,t)&&i(t)})?r():a().toOption().filter(function(t){return!We(t.element(),n)}).each(r)})}),n(by,function(t,n,e){n.onTabChange(t,{newTabName:e.name(),oldTabName:e.oldName()})}),ir(function(t){var n=i();El.setValue(t,n.getData())})])},VF=function(t,n){var e=n.map(function(t){return t.footerButtons}).getOr([]),o=P(e,function(t){return"start"===t.align}),r=function(t,n){return Zb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:V(n,function(t){return t.memento.asSpec()})})};return[r("start",o.pass),r("end",o.fail)]},PF=function(t,i){return{dom:kB('<div class="tox-dialog__footer"></div>'),components:[],behaviours:Ka([vT.config({channel:gF,initialData:t,updateState:function(t,n){var r=V(n.buttons,function(t){var n,e,o=Zg((e=i,$k(n=t,n.type,e)));return{name:t.name,align:t.align,memento:o}});return st.some({lookupByName:function(t,n){return e=t,o=n,L(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(e)});var e,o},footerButtons:r})},renderComponents:VF})])}},HF=function(t,n){return EA.parts().footer(PF(t,n))},zF=function(n,e){if(n.getRoot().getSystem().isConnected()){var o=Lf.getCurrent(n.getFormWrapper()).getOr(n.getFormWrapper());return eS.getField(o,e).fold(function(){var t=n.getFooter();return vT.getState(t).get().bind(function(t){return t.lookupByName(o,e)})},function(t){return st.some(t)})}return st.none()},NF=function(c,o,s){var t=function(t){var n=c.getRoot();n.getSystem().isConnected()&&t(n)},l={getData:function(){var t=c.getRoot(),n=t.getSystem().isConnected()?c.getFormWrapper():t,e=El.getValue(n),o=Tt(s,function(t){return t.get()});return et(et({},e),o)},setData:function(a){t(function(t){var n,e,o=l.getData(),r=et(et({},o),a),i=(n=r,e=c.getRoot(),vT.getState(e).get().map(function(t){return _n(On("data",t.dataValidator,n))}).getOr(n)),u=c.getFormWrapper();El.setValue(u,i),_t(s,function(t,n){It(r,n)&&t.set(r[n])})})},disable:function(t){zF(c,t).each(Dh.disable)},enable:function(t){zF(c,t).each(Dh.enable)},focus:function(t){zF(c,t).each(eg.focus)},block:function(n){if(!w(n))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){Uo(t,hy,{message:n})})},unblock:function(){t(function(t){jo(t,vy)})},showTab:function(e){t(function(t){var n=c.getBody();vT.getState(n).get().exists(function(t){return t.isTabPanel()})&&Lf.getCurrent(n).each(function(t){rF.showTab(t,e)})})},redial:function(e){t(function(t){var n=o(e);t.getSystem().broadcastOn([fF],n),t.getSystem().broadcastOn([dF],n.internalDialog),t.getSystem().broadcastOn([mF],n.internalDialog),t.getSystem().broadcastOn([gF],n.internalDialog),l.setData(n.initialData)})},close:function(){t(function(t){jo(t,dy)})}};return l},LF=function(t,n,e){var o,r,i,u=EF(t.internalDialog.title,e),a=(o={body:t.internalDialog.body},r=e,i=hF(o,st.none(),r,!1),EA.parts().body(i)),c=AF(t.internalDialog.buttons),s=MF(c),l=HF({buttons:c},e),f=RF(function(){return p},BF(function(){return g},n),e.shared.getSink),d="normal"!==t.internalDialog.size?"large"===t.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],m={header:u,body:a,footer:st.some(l),extraClasses:d,extraBehaviours:[],extraStyles:{}},g=DF(m,t,f,e),p=NF({getRoot:function(){return g},getBody:function(){return EA.getBody(g)},getFooter:function(){return EA.getFooter(g)},getFormWrapper:function(){var t=EA.getBody(g);return Lf.getCurrent(t).getOr(t)}},n.redial,s);return{dialog:g,instanceApi:p}},jF=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m=Wr("dialog-label"),g=Wr("dialog-content"),p=Zg((u={title:t.internalDialog.title,draggable:!0},a=m,c=e.shared.providers,Zb.sketch({dom:kB('<div class="tox-dialog__header"></div>'),components:[_F(u,st.some(a),c),TF(),OF(c)],containerBehaviours:Ka([gA.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return ju(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),h=Zg((s={body:t.internalDialog.body},l=g,f=e,d=o,hF(s,st.some(l),f,d))),v=AF(t.internalDialog.buttons),b=MF(v),y=Zg(PF({buttons:v},e)),x=RF(function(){return S},{onBlock:function(){},onUnblock:function(){},onClose:function(){return n.closeWindow()}},e.shared.getSink),w=uu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:((r={role:"dialog"})["aria-labelledby"]=m,r["aria-describedby"]=""+g,r)},eventOrder:((i={})[xo()]=[vT.name(),oc.name()],i[wo()]=["execute-on-form"],i[Mo()]=["reflecting","execute-on-form"],i),behaviours:Ka([Gm.config({mode:"cyclic",onEscape:function(t){return jo(t,dy),st.some(!0)},useTabstopAt:function(t){return!DS(t)&&("button"!==cr(t)||"disabled"!==Ir(t,"disabled"))}}),vT.config({channel:fF,updateState:function(t,n){return st.some(n)},initialData:t}),eg.config({}),$m("execute-on-form",x.concat([or(uo(),function(t,n){Gm.focusIn(t)})])),SS({})]),components:[p.asSpec(),h.asSpec(),y.asSpec()]}),S=NF({getRoot:function(){return w},getFooter:function(){return y.get(w)},getBody:function(){return h.get(w)},getFormWrapper:function(){var t=h.get(w);return Lf.getCurrent(t).getOr(t)}},n.redial,b);return{dialog:w,instanceApi:S}},UF=tinymce.util.Tools.resolve("tinymce.util.URI"),WF=["insertContent","setContent","execCommand","close","block","unblock"],GF=function(t){return S(t)&&-1!==WF.indexOf(t.mceAction)},XF=function(o,t,r,n){var e,i,u,a,c=EF(o.title,n),s=(i={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[TS({dom:{tag:"iframe",attributes:{src:o.url}},behaviours:Ka([uy.config({}),eg.config({})])})]}],behaviours:Ka([Gm.config({mode:"acyclic",useTabstopAt:x(DS)})])},EA.parts().body(i)),l=o.buttons.bind(function(t){return 0===t.length?st.none():st.some(HF({buttons:t},n))}),f=IF(function(){return y},BF(function(){return b},t)),d=et(et({},o.height.fold(function(){return{}},function(t){return{height:t+"px","max-height":t+"px"}})),o.width.fold(function(){return{}},function(t){return{width:t+"px","max-width":t+"px"}})),m=o.width.isNone()&&o.height.isNone()?["tox-dialog--width-lg"]:[],g=new UF(o.url,{base_uri:new UF(nt.window.location.href)}),p=g.protocol+"://"+g.host+(g.port?":"+g.port:""),h=se(st.none()),v=[$m("messages",[rr(function(){var t=Bb(fe.fromDom(nt.window),"message",function(t){if(g.isSameOrigin(new UF(t.raw().origin))){var n=t.raw().data;GF(n)?function(t,n,e){switch(e.mceAction){case"insertContent":t.insertContent(e.content);break;case"setContent":t.setContent(e.content);break;case"execCommand":var o=!!k(e.ui)&&e.ui;t.execCommand(e.cmd,o,e.value);break;case"close":n.close();break;case"block":n.block(e.message);break;case"unblock":n.unblock()}}(r,y,n):!GF(e=n)&&S(e)&&It(e,"mceAction")&&o.onMessage(y,n)}var e});h.set(st.some(t))}),ir(function(){h.get().each(function(t){return t.unbind()})})]),oc.config({channels:((e={})[pF]={onReceive:function(t,n){Lu(t.element(),"iframe").each(function(t){t.dom().contentWindow.postMessage(n,p)})}},e)})],b=DF({header:c,body:s,footer:l,extraClasses:m,extraBehaviours:v,extraStyles:d},o,f,n),y=(a=function(t){u.getSystem().isConnected()&&t(u)},{block:function(n){if(!w(n))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");a(function(t){Uo(t,hy,{message:n})})},unblock:function(){a(function(t){jo(t,vy)})},close:function(){a(function(t){jo(t,dy)})},sendMessage:function(n){a(function(t){t.getSystem().broadcastOn([pF],n)})}});return{dialog:u=b,instanceApi:y}},YF=function(t){var c,s,l,f,p=t.backstage,h=t.editor,v=Jh(h),e=(s=(c=t).backstage.shared,{open:function(t,n){var e=function(){EA.hide(u),n()},o=Zg($k({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:st.none()},"cancel",c.backstage)),r=xF(),i=yF(e,s.providers),u=uu(CF({lazySink:function(){return s.getSink()},header:bF(r,i),body:wF(t,s.providers),footer:st.some(SF(kF([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[$o(my,e)],eventOrder:{}}));EA.show(u);var a=o.get(u);eg.focus(a)}}),o=(f=(l=t).backstage.shared,{open:function(t,n){var e=function(t){EA.hide(a),n(t)},o=Zg($k({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:st.none()},"submit",l.backstage)),r=$k({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:st.none()},"cancel",l.backstage),i=xF(),u=yF(function(){return e(!1)},f.providers),a=uu(CF({lazySink:function(){return f.getSink()},header:bF(i,u),body:wF(t,f.providers),footer:st.some(SF(kF([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[$o(my,function(){return e(!1)}),$o(py,function(){return e(!0)})],eventOrder:{}}));EA.show(a);var c=o.get(a);eg.focus(c)}}),r=function(t,e){return XM.openUrl(function(t){var n=XF(t,{closeWindow:function(){EA.hide(n.dialog),e(n.instanceApi)}},h,p);return EA.show(n.dialog),n.instanceApi},t)},i=function(t,i){return XM.open(function(t,n,e){var o=n,r=LF({dataValidator:e,initialData:o,internalDialog:t},{redial:XM.redial,closeWindow:function(){EA.hide(r.dialog),i(r.instanceApi)}},p);return EA.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},t)},u=function(t,d,m,g){return XM.open(function(t,n,e){var o,r,i,u=_n(On("data",e,n)),a=(o=se(st.none()),{clear:function(){o.set(st.none())},set:function(t){o.set(st.some(t))},isSet:function(){return o.get().isSome()},on:function(t){o.get().each(t)}}),c=p.shared.header.isPositionedAtTop(),s=function(){return a.on(function(t){zg.reposition(t),JE.refresh(t)})},l=jF({dataValidator:e,initialData:u,internalDialog:t},{redial:XM.redial,closeWindow:function(){a.on(zg.hide),h.off("ResizeEditor",s),a.clear(),m(l.instanceApi)}},p,g),f=uu(zg.sketch(et(et({lazySink:p.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},c?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:Ka(b([$m("window-manager-inline-events",[$o(Io(),function(t,n){jo(l.dialog,my)})])],(r=h,i=c,v&&i?[]:[JE.config({contextual:{lazyContext:function(){return st.some(Au(fe.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]))),isExtraPart:function(t,n){return Mb(e=n,".tox-alert-dialog")||Mb(e,".tox-confirm-dialog");var e}})));return a.set(f),zg.showWithin(f,d,au(l.dialog),st.some(Vi())),v&&c||(JE.refresh(f),h.on("ResizeEditor",s)),l.instanceApi.setData(u),Gm.focusIn(l.dialog),l.instanceApi},t)};return{open:function(t,n,e){return n!==undefined&&"toolbar"===n.inline?u(t,p.shared.anchors.inlineDialog(),e,n.ariaAttrs):n!==undefined&&"cursor"===n.inline?u(t,p.shared.anchors.cursor(),e,n.ariaAttrs):i(t,e)},openUrl:function(t,n){return r(t,n)},alert:function(t,n){e.open(t,function(){n()})},close:function(t){t.close()},confirm:function(t,n){o.open(t,function(t){n(t)})}}};!function eI(){t.add("silver",function(t){var n=CA(t),e=n.uiMothership,o=n.backstage,r=n.renderUI,i=n.getUi;Tb(t,o.shared);var u=YF({editor:t,backstage:o});return{renderUI:r,getWindowManagerImpl:at(u),getNotificationManagerImpl:function(){return rp(0,{backstage:o},e)},ui:i()}})}()}(window);