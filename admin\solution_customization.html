<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>方案定制管理</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- 添加管理后台标识 -->
    <meta name="app-type" content="admin-panel" />
    <meta name="client-type" content="admin" />
    <link rel="stylesheet" href="css/bootstrap.min.css" />
    <link rel="stylesheet" href="css/bootstrap-responsive.min.css" />
    <link rel="stylesheet" href="css/uniform.css" />
    <link rel="stylesheet" href="css/select2.css" />
    <link rel="stylesheet" href="css/matrix-style.css" />
    <link rel="stylesheet" href="css/matrix-media.css" />
    <link rel="stylesheet" href="font-awesome/css/font-awesome.css" />
    <link rel="stylesheet" href="css/jquery.gritter.css" />
    <style>
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        #content {
            flex: 1;
            margin-left: 0 !important;
            border-left: 1px solid #ddd;
            background: #eee;
        }
        
        /* 修复模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: #000;
            opacity: 0.5;
        }
        
        /* 修复模态框居中问题 */
        .modal {
            width: 560px;
            margin-left: -280px;
            top: 50%;
            margin-top: -250px;
        }
        
        /* 确保通知显示在最上层 */
        #gritter-notice-wrapper {
            z-index: 9999999;
        }
        
        .gritter-item-wrapper {
            z-index: 9999999;
        }
    </style>
</head>
<body>

<!-- 先加载head.js，确保菜单正确显示 -->
<script src="js/jquery.min.js"></script>
<script src="js/jquery.cookie.js"></script>
<script src="js/jquery.i18n.js"></script>
<script src="js/language.js"></script>
<script src="js/head.js"></script>

<!-- 添加登录验证脚本 -->
<script>
    // 登录验证
    document.addEventListener('DOMContentLoaded', () => {
        // 检查是否已登录
        if (sessionStorage.getItem('adminLoggedIn') !== 'true') {
            // 未登录，跳转到登录页面
            window.location.href = '/login/login.html';
        }
    });
</script>

<!-- 包装容器 -->
<div id="wrapper">
    <!--main-container-part-->
    <div id="content">
        <!--breadcrumbs-->
        <div id="content-header">
            <div id="breadcrumb">
                <a href="admin_home.html" title="返回首页" class="tip-bottom"><i class="icon-home"></i>首页</a>
                <a href="javascript:void(0);" class="current" onclick="return false;">方案定制管理</a>
            </div>
        </div>
        <!--End-breadcrumbs-->

        <!--Action boxes-->
        <div class="container-fluid">
            <div class="widget-box">
                <div class="widget-content">
                    <!-- 数据列表 -->
                    <div class="widget-box" style="margin-top: 0;">
                        <div class="widget-content nopadding">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">ID</th>
                                        <th width="8%">姓名</th>
                                        <th width="10%">联系电话</th>
                                        <th width="15%">邮箱</th>
                                        <th width="12%">公司</th>
                                        <th width="10%">定制类型</th>
                                        <th width="25%" style="min-width: 300px;">需求描述</th>
                                        <th width="10%">提交时间</th>
                                        <th width="5%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="data_list">
                                    <!-- 数据将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div class="fg-toolbar ui-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix">
                        <div id="page" class="dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_full_numbers">
                            <!-- 分页将通过JavaScript动态填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end-main-container-part-->
</div>

<!-- 删除确认模态框 -->
<div id="deleteCustomizationAlert" class="modal hide">
    <div class="modal-header">
        <button data-dismiss="modal" class="close" type="button">×</button>
        <h3>确认删除</h3>
    </div>
    <div class="modal-body">
        <p>确定要删除以下方案定制记录吗？</p>
        <input type="hidden" id="del_id" />
        <p>ID: <span id="del_id_span"></span></p>
        <p>姓名: <span id="del_name"></span></p>
        <p>公司: <span id="del_company"></span></p>
        <p>需求描述: <span id="del_requirements"></span></p>
    </div>
    <div class="modal-footer">
        <a class="btn btn-primary" href="javascript:del_customization()">确定</a>
        <a data-dismiss="modal" class="btn" href="#">取消</a>
    </div>
</div>

<!--Footer-part-->
<script src="js/footer.js"></script>
<!--end-Footer-part-->

<!-- 其他脚本 -->
<script src="js/excanvas.min.js"></script>
<script src="js/jquery.ui.custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.peity.min.js"></script>
<script src="js/fullcalendar.min.js"></script>
<script src="js/matrix.js"></script>
<script src="js/jquery.gritter.min.js"></script>
<script src="js/matrix.interface.js"></script>
<script src="js/matrix.chat.js"></script>
<script src="js/jquery.validate.js"></script>
<script src="js/matrix.form_validation.js"></script>
<script src="js/jquery.wizard.js"></script>
<script src="js/jquery.uniform.js"></script>
<script src="js/matrix.popover.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/page.js"></script>
<script src="js/solution_customization.js"></script>

<script type="text/javascript">
    // This function is called from the pop-up menus to transfer to
    // a different page. Ignore if the value returned is a null string:
    function goPage(newURL) {
        // if url is empty, skip the menu dividers and reset the menu selection to default
        if (newURL != "") {
            // if url is "-", it is this page -- reset the menu:
            if (newURL == "-") {
                resetMenu();
            }
            // else, send page to designated URL
            else {
                document.location.href = newURL;
            }
        }
    }

    // resets the menu selection upon entry to this page:
    function resetMenu() {
        document.gomenu.selector.selectedIndex = 2;
    }

    $(function () {
        // 确保在页面加载完成后清理模态框背景
        setTimeout(function() {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            console.log("页面加载完成，清理模态框背景");
        }, 1000);
    });
</script>

<script>
    // 页面加载后自动高亮菜单
    $(function () {
        reset_menu();
    });
</script>
</body>
</html> 