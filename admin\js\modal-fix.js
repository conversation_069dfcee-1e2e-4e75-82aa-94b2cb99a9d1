/**
 * Bootstrap 2.x 模态框修复
 * 解决模态框背景无法点击关闭、多个模态框叠加、灰色遮罩无法清除等问题
 * 优化版本：使用被动事件监听器，避免性能警告
 */

$(document).ready(function() {
    // 初始化时清理可能存在的模态框背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');

    // 修复Bootstrap 2.x模态框问题
    if (typeof $.fn.modal !== 'undefined') {
        // 保存原始modal方法
        var originalModal = $.fn.modal;

        // 重写modal方法
        $.fn.modal = function(option) {
            if (option === 'hide') {
                // 隐藏模态框时清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');

                // 调用原始方法
                return originalModal.apply(this, arguments);
            }
            else if (option === 'show' || typeof option === 'object') {
                // 显示模态框前清理背景
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');

                // 调用原始方法
                return originalModal.apply(this, arguments);
            }

            // 其他情况调用原始方法
            return originalModal.apply(this, arguments);
        };
    }

    // 监听模态框关闭事件，确保背景被移除
    $(document).on('hidden', '.modal', function() {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });

    // 监听点击事件，修复灰色遮罩无法点击的问题
    $(document).on('click', '.modal-backdrop', function() {
        $('.modal').modal('hide');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });

    // 监听ESC键，确保模态框可以关闭
    $(document).keyup(function(e) {
        if (e.keyCode === 27) { // ESC键
            $('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        }
    });

    // 添加被动事件监听器支持，避免触摸事件性能警告
    function addPassiveEventListeners() {
        // 检测是否支持被动事件监听器
        var supportsPassive = false;
        try {
            var opts = Object.defineProperty({}, 'passive', {
                get: function() {
                    supportsPassive = true;
                }
            });
            window.addEventListener("testPassive", null, opts);
            window.removeEventListener("testPassive", null, opts);
        } catch (e) {}

        // 如果支持被动事件监听器，为触摸事件添加被动选项
        if (supportsPassive) {
            // 为模态框元素添加被动触摸事件监听器
            $('.modal').each(function() {
                var element = this;

                // 移除可能存在的旧监听器
                element.removeEventListener('touchstart', function() {});
                element.removeEventListener('touchmove', function() {});

                // 添加被动监听器
                element.addEventListener('touchstart', function(e) {
                    // 被动监听器，不阻止默认行为
                }, { passive: true });

                element.addEventListener('touchmove', function(e) {
                    // 被动监听器，不阻止默认行为
                }, { passive: true });
            });
        }
    }

    // 页面加载完成后添加被动事件监听器
    setTimeout(addPassiveEventListeners, 100);

    // 初始化数据列表
    setTimeout(function() {
        if (typeof nav_list === 'function') {
            nav_list(1, 10);
        }
    }, 1000);
});