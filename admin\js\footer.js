/**
 * Footer 组件 - 使用现代 DOM 操作替代 document.write()
 * 避免性能警告，提升页面加载速度
 */

function insertFooter() {
    // 创建 footer HTML 结构
    const footerHTML = `
        <div class='row-fluid'>
            <div id='footer' class='span12'>
                Copyright © 2020.
                <a href='http://www.beiqicloud.com/' target='_blank' class='i18n' name='foot.cmp'></a>
                All rights reserved.
            </div>
        </div>
    `;

    // 创建临时容器
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = footerHTML;

    // 将 footer 插入到页面底部
    const footerElement = tempDiv.firstElementChild;
    if (footerElement) {
        // 尝试插入到 body 末尾
        document.body.appendChild(footerElement);
    }
}

// 页面加载完成后插入 footer
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', insertFooter);
} else {
    insertFooter();
}