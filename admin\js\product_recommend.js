// 产品推荐管理相关JavaScript

// 全局变量
var current_page = 1;
var page_size = 10;
var total_pages = 0;
var i18nLanguage = 'zh_CN'; // 默认语言

// 获取产品推荐列表
function product_recommend_list(page, page_size, lang) {
    if (page) {
        current_page = page;
    }
    if (page_size) {
        this.page_size = page_size;
    }

    // 构建筛选条件
    var filters = {};
    if (typeof lang === 'undefined') {
        // 兼容老调用方式，尝试从下拉框获取
        lang = $('#lang_filter').val() || '';
    }
    if (lang !== '' && lang !== undefined && lang !== null) {
        filters['lang'] = Number(lang); // 强制转为数字
    }
    
    // 发送请求获取产品推荐列表
    $.ajax({
        type: "post",
        url: "/apis/get_product_recommend/",
        async: false,
        headers: {
            'X-Client-Type': 'admin',
            'X-Admin-Request': 'true'
        },
        data: {
            page: current_page,
            page_size: this.page_size,
            filters: JSON.stringify(filters)
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 更新数据列表
                update_data_list(data.data_list);
                
                // 更新分页
                total_pages = data.total_pages;
                update_pagination(current_page, total_pages);
            } else {
                show_gitter('错误提示', data.msg || '获取数据失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '获取数据失败，请检查网络连接', 2);
        }
    });
}

// 更新数据列表
function update_data_list(data_list) {
    var html = '';
    
    if (!data_list || data_list.length === 0) {
        html = '<tr><td colspan="11" class="text-center">暂无数据</td></tr>';
    } else {
        // 移除前端排序，使用后端SQL排序结果（按lang ASC, sort_order ASC, id ASC）
        // data_list.sort(function(a, b) {
        //     return (a.sort_order || 0) - (b.sort_order || 0);
        // });
        
        for (var i = 0; i < data_list.length; i++) {
            var item = data_list[i];
            
            // 格式化时间
            var updateTime = item.update_time ? formatDateTime(new Date(item.update_time)) : '未更新';
            
            // 显示状态
            var showStatus = item.show ? 
                '<span class="label label-success toggle-show-status" data-id="' + item.id + '" data-show="1" style="cursor:pointer" title="点击切换显示状态">显示</span>' : 
                '<span class="label label-important toggle-show-status" data-id="' + item.id + '" data-show="0" style="cursor:pointer" title="点击切换显示状态">隐藏</span>';
            
            // 网站类型和背景色
            var langType = '';
            var tr_bg_color = '';
            if (item.lang === 0) {
                langType = '<span class="label label-success">中文</span>';
                tr_bg_color = '#d2d2f7'; // 中文背景色
            } else if (item.lang === 1) {
                langType = '<span class="label label-info">英文</span>';
                tr_bg_color = 'none'; // 英文背景色
            } else {
                langType = '<span class="label">未知</span>';
                tr_bg_color = 'none';
            }
            
            // 构建行HTML，确保每个单元格都应用背景色
            html += '<tr>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + item.id + '</td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + langType + '</td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">\n';
            html += '    <img src="' + item.icon + '" class="product-icon" alt="' + item.title + '">\n';
            html += '  </td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + item.title + '</td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + (item.description || '').substring(0, 30) + (item.description && item.description.length > 30 ? '...' : '') + '</td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + (item.url || '') + '</td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + showStatus + '</td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + (item.sort_order || 0) + '</td>\n';
            html += '  <td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + updateTime + '</td>\n';
            html += '  <td class="taskOptions" style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">\n';
            html += '    <a href="javascript:void(0);" onclick="show_edit_modal(' + item.id + ')" class="btn btn-primary btn-mini"><i class="icon icon-pencil"></i> 编辑</a>\n';
            html += '    <a href="javascript:void(0);" onclick="show_delete_modal(' + item.id + ')" class="btn btn-danger btn-mini"><i class="icon icon-trash"></i> 删除</a>\n';
            html += '  </td>\n';
            html += '</tr>';
        }
    }
    
    $('#data_list').html(html);
}

// 更新分页
function update_pagination(current_page, total_pages) {
    var html = '';
    
    // 获取当前筛选条件
    var lang = $('#lang_filter').val() || '';
    
    // 首页和上一页
    html += '<span>';
    if (current_page > 1) {
        html += '<a class="first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default" onclick="product_recommend_list(1, 10, \'' + lang + '\')">首页</a>';
        html += '<a class="previous fg-button ui-button ui-state-default" onclick="product_recommend_list(' + (current_page - 1) + ', 10, \'' + lang + '\')">上一页</a>';
    } else {
        html += '<a class="first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default ui-state-disabled">首页</a>';
        html += '<a class="previous fg-button ui-button ui-state-default ui-state-disabled">上一页</a>';
    }
    html += '</span>';
    
    // 页码
    html += '<span>';
    var start_page = Math.max(1, current_page - 2);
    var end_page = Math.min(total_pages, start_page + 4);
    
    for (var i = start_page; i <= end_page; i++) {
        if (i === current_page) {
            html += '<a class="fg-button ui-button ui-state-default ui-state-disabled">' + i + '</a>';
        } else {
            html += '<a class="fg-button ui-button ui-state-default" onclick="product_recommend_list(' + i + ', 10, \'' + lang + '\')">' + i + '</a>';
        }
    }
    html += '</span>';
    
    // 下一页和末页
    html += '<span>';
    if (current_page < total_pages) {
        html += '<a class="next fg-button ui-button ui-state-default" onclick="product_recommend_list(' + (current_page + 1) + ', 10, \'' + lang + '\')">下一页</a>';
        html += '<a class="last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default" onclick="product_recommend_list(' + total_pages + ', 10, \'' + lang + '\')">末页</a>';
    } else {
        html += '<a class="next fg-button ui-button ui-state-default ui-state-disabled">下一页</a>';
        html += '<a class="last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default ui-state-disabled">末页</a>';
    }
    html += '</span>';
    
    $('#page').html(html);
}

// 显示编辑模态框
function show_edit_modal(id) {
    // 清空表单
    $('#edit_pid').val('');
    $('#edit_title').val('');
    $('#edit_description').val('');
    $('#edit_icon').attr('src', '');
    $('#edit_url').val('');
    $('#edit_sort_order').val('0');  // 设置默认值为0
    $('#edit_show').val('True');
    
    // 重置所有选项的selected属性
    $("#edit_show option").removeAttr("selected");
    $("#edit_lang option").removeAttr("selected");
    
    // 获取产品推荐详情
    $.ajax({
        type: "post",
        url: "/apis/get_product_recommend/",
        async: false,
        headers: {
            'X-Client-Type': 'admin',
            'X-Admin-Request': 'true'
        },
        data: {
            filters: JSON.stringify({id: id})
        },
        success: function (data) {
            if (data.status === 'ok' && data.data_list && data.data_list.length > 0) {
                var item = data.data_list[0];
                
                // 填充表单
                $('#edit_pid').val(item.id);
                $('#edit_title').val(item.title);
                $('#edit_description').val(item.description);
                $('#edit_icon').attr('src', item.icon);
                $('#edit_url').val(item.url);
                $('#edit_sort_order').val(item.sort_order !== undefined && item.sort_order !== null ? item.sort_order : 0);
                
                // 设置显示状态选项（兼容多种类型的show值）
                var isShow = (item.show === true || item.show === 1 || item.show === "1");
                
                if (isShow) {
                    $("#edit_show option[value='True']").prop("selected", true);
                } else {
                    $("#edit_show option[value='False']").prop("selected", true);
                }
                
                // 设置语言选项
                if (item.lang !== undefined) {
                    $("#edit_lang option").removeAttr("selected");
                    $("#edit_lang option[value='" + item.lang + "']").prop("selected", true);
                } else {
                    $("#edit_lang option[value='0']").prop("selected", true);
                }
                
                // 显示模态框
                $('#editProductAlert').modal('show');
            } else {
                show_gitter('错误提示', data.msg || '获取数据失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '获取数据失败，请检查网络连接', 2);
        }
    });
}

// 显示删除模态框
function show_delete_modal(id) {
    // 清空表单
    $('#del_id').val('');
    $('#del_id_span').text('');
    $('#del_title').text('');
    $('#del_icon').attr('src', '');
    
    // 获取产品推荐详情
    $.ajax({
        type: "post",
        url: "/apis/get_product_recommend/",
        async: false,
        headers: {
            'X-Client-Type': 'admin',
            'X-Admin-Request': 'true'
        },
        data: {
            filters: JSON.stringify({id: id})
        },
        success: function (data) {
            if (data.status === 'ok' && data.data_list && data.data_list.length > 0) {
                var item = data.data_list[0];
                
                // 填充表单
                $('#del_id').val(item.id);
                $('#del_id_span').text(item.id);
                $('#del_title').text(item.title);
                $('#del_icon').attr('src', item.icon);
                
                // 显示模态框
                $('#deleteProductAlert').modal('show');
            } else {
                show_gitter('错误提示', data.msg || '获取数据失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '获取数据失败，请检查网络连接', 2);
        }
    });
}

// 编辑产品推荐
function edit_product() {
    var id = $('#edit_pid').val();
    var title = $('#edit_title').val().trim();
    var description = $('#edit_description').val().trim();
    var url = $('#edit_url').val().trim();
    var sort_order = $('#edit_sort_order').val().trim();
    var show = $('#edit_show').val();
    var lang = $('#edit_lang').val();
    
    // 验证表单
    if (!id) {
        show_gitter('错误提示', '缺少ID参数', 2);
        return;
    }
    
    if (!title) {
        show_gitter('错误提示', '标题不能为空', 2);
        return;
    }
    
    if (!check_is_num(sort_order)) {
        show_gitter('错误提示', '显示顺序必须为整数', 2);
        return;
    }
    
    // 确保sort_order是整数
    sort_order = parseInt(sort_order, 10) || 0;
    
    // 确保lang是整数
    lang = parseInt(lang, 10) || 0;
    
    // 如果上传了新图标，先上传图标
    var icon = $('#edit_icon').attr('src');
    var icon_file = document.getElementById('edit_new_icon').files[0];
    
    if (icon_file) {
        var formData = new FormData();
        formData.append('file', icon_file);
        
        $.ajax({
            type: "post",
            url: "/apis/upload_pic/",
            async: false,
            processData: false,
            contentType: false,
            data: formData,
            success: function (data) {
                if (data.status === 'ok') {
                    icon = data.path;
                } else {
                    show_gitter('错误提示', data.msg || '上传图标失败', 2);
                    return;
                }
            },
            error: function(xhr, status, error) {
                show_gitter('错误提示', '上传图标失败，请检查网络连接', 2);
                return;
            }
        });
    }
    
    // 构造请求数据
    var requestData = {
        title: title,
        description: description,
        icon: icon,
        url: url,
        sort_order: sort_order,
        show: show,
        lang: lang,
        filters: JSON.stringify({id: id})
    };
    
    // 更新产品推荐
    $.ajax({
        type: "post",
        url: "/apis/update_product_recommend/",
        async: false,
        data: requestData,
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#editProductAlert').modal('hide');
                
                // 刷新数据列表
                product_recommend_list(current_page);
                
                // 显示成功提示
                show_gitter('提示信息', '更新成功', 1);
            } else {
                show_gitter('错误提示', data.msg || '更新失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '更新失败，请检查网络连接', 2);
        }
    });
}

// 添加产品推荐
function add_product() {
    var title = $('#add_title').val().trim();
    var description = $('#add_description').val().trim();
    var url = $('#add_url').val().trim();
    var sort_order = $('#add_sort_order').val().trim();
    var show = $('#add_show').val();
    var lang = $('#add_lang').val();
    
    // 验证表单
    if (!title) {
        show_gitter('错误提示', '标题不能为空', 2);
        return;
    }
    
    if (!check_is_num(sort_order)) {
        show_gitter('错误提示', '显示顺序必须为整数', 2);
        return;
    }
    
    // 确保sort_order是整数
    sort_order = parseInt(sort_order, 10) || 0;
    
    // 确保lang是整数
    lang = parseInt(lang, 10) || 0;
    
    // 验证是否上传了图标
    var icon_file = document.getElementById('add_new_icon').files[0];
    if (!icon_file) {
        show_gitter('错误提示', '请上传图标', 2);
        return;
    }
    
    // 上传图标
    var formData = new FormData();
    formData.append('file', icon_file);
    
    $.ajax({
        type: "post",
        url: "/apis/upload_pic/",
        async: false,
        processData: false,
        contentType: false,
        data: formData,
        success: function (data) {
            if (data.status === 'ok') {
                var icon = data.path;
                
                var requestData = {
                    title: title,
                    description: description,
                    icon: icon,
                    url: url,
                    sort_order: sort_order,
                    show: show,
                    lang: lang
                };
                
                // 创建产品推荐
                $.ajax({
                    type: "post",
                    url: "/apis/create_product_recommend/",
                    async: false,
                    data: requestData,
                    success: function (data) {
                        if (data.status === 'ok') {
                            // 关闭模态框
                            $('#addProductAlert').modal('hide');
                            
                            // 刷新数据列表
                            product_recommend_list(1);
                            
                            // 显示成功提示
                            show_gitter('提示信息', '添加成功', 1);
                        } else {
                            show_gitter('错误提示', data.msg || '添加失败', 2);
                        }
                    },
                    error: function(xhr, status, error) {
                        show_gitter('错误提示', '添加失败，请检查网络连接', 2);
                    }
                });
            } else {
                show_gitter('错误提示', data.msg || '上传图标失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '上传图标失败，请检查网络连接', 2);
        }
    });
}

// 删除产品推荐
function del_product() {
    var id = $('#del_id').val();
    
    $.ajax({
        type: "post",
        url: "/apis/delete_product_recommend/",
        async: false,
        data: {
            filters: JSON.stringify({id: id})
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#deleteProductAlert').modal('hide');
                
                // 刷新数据列表
                product_recommend_list(1);
                
                // 显示成功提示
                show_gitter('提示信息', '删除成功', 1);
            } else {
                show_gitter('错误提示', data.msg || '删除失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '删除失败，请检查网络连接', 2);
        }
    });
}

// 重新生成首页
function make_index_html() {
    $.ajax({
        type: "post",
        url: "/apis/make_index_html/",
        async: false,
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#makeIndexHtml').modal('hide');
                
                // 显示成功提示
                show_gitter('提示信息', '首页生成成功', 1);
            } else {
                show_gitter('错误提示', data.msg || '首页生成失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '生成首页失败，请检查网络连接', 2);
        }
    });
}

// 显示通知
function show_gitter(title, message, type) {
    var class_name = '';
    if (type === 1) {
        class_name = 'gritter-success';
    } else if (type === 2) {
        class_name = 'gritter-error';
    } else {
        class_name = 'gritter-info';
    }
    
    $.gritter.add({
        title: title,
        text: message,
        class_name: class_name
    });
}

// 验证是否为整数
function check_is_num(str) {
    if (str === '' || str === null) {
        return false;
    }
    return /^\d+$/.test(str);
}

// 格式化日期时间
function formatDateTime(date) {
    // 将UTC时间转换为北京时间（UTC+8）
    var beijingDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);
    
    var year = beijingDate.getFullYear();
    var month = padZero(beijingDate.getMonth() + 1);
    var day = padZero(beijingDate.getDate());
    var hours = padZero(beijingDate.getHours());
    var minutes = padZero(beijingDate.getMinutes());
    var seconds = padZero(beijingDate.getSeconds());
    
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}

// 补零
function padZero(num) {
    return num < 10 ? '0' + num : num;
}

// 图片上传预览
$(document).ready(function() {
    // 编辑模态框图片预览
    $('#edit_new_icon').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#edit_icon').attr('src', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 添加模态框图片预览
    $('#add_new_icon').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#add_icon').attr('src', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 绑定显示状态切换事件
    $(document).on('click', '.toggle-show-status', function() {
        var id = $(this).data('id');
        var show = $(this).data('show');
        update_show_status(id, show);
    });
});

// 更新产品显示状态
function update_show_status(id, show) {
    // 先获取当前产品的完整信息，包括lang字段
    $.ajax({
        type: "post",
        url: "/apis/get_product_recommend/",
        async: false,
        headers: {
            'X-Client-Type': 'admin',
            'X-Admin-Request': 'true'
        },
        data: {
            filters: JSON.stringify({id: id})
        },
        success: function (data) {
            if (data.status === 'ok' && data.data_list && data.data_list.length > 0) {
                var item = data.data_list[0];
                var lang = item.lang !== undefined ? item.lang : 0;
                
                // 构造请求参数
                var newShow = show === 1 ? 0 : 1; // 切换状态
                var filters = JSON.stringify({id: id});
                
                $.ajax({
                    type: "post",
                    url: "/apis/update_product_recommend_status/",
                    async: true,
                    data: {
                        filters: filters,
                        show: newShow,
                        lang: lang
                    },
                    success: function(data) {
                        if (data.status === 'ok') {
                            // 刷新列表
                            product_recommend_list(current_page);
                            
                            // 显示成功提示
                            show_gitter('提示信息', '状态更新成功', 1);
                        } else {
                            show_gitter('错误提示', data.msg || '更新状态失败', 2);
                        }
                    },
                    error: function(xhr, status, error) {
                        show_gitter('错误提示', '更新状态失败，请检查网络连接', 2);
                    }
                });
            } else {
                show_gitter('错误提示', '获取产品信息失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误提示', '获取产品信息失败，请检查网络连接', 2);
        }
    });
}

// 添加一个函数来修复所有产品推荐的lang字段
function fix_product_lang() {
    // 获取所有产品推荐数据
    $.ajax({
        type: "post",
        url: "/apis/get_product_recommend/",
        async: false,
        headers: {
            'X-Client-Type': 'admin'
        },
        data: {page: 1, page_size: 100},
        success: function (data) {
            if (data.status === 'ok') {
                var data_list = data.data_list;
                
                // 记录需要修复的记录数量
                var fixCount = 0;
                
                // 检查每条记录
                $.each(data_list, function(index, item) {
                    // 检查lang字段是否为数字
                    if (typeof item.lang !== 'number') {
                        
                        // 确定正确的lang值
                        var langValue = 0; // 默认为中文
                        if (item.lang === 1 || item.lang === "1") {
                            langValue = 1; // 英文
                        }
                        
                        // 更新记录
                        updateRecordLang(item.id, langValue);
                        fixCount++;
                    }
                });
                
                if (fixCount > 0) {
                    show_gitter('修复完成', '已修复' + fixCount + '条产品系列记录的lang字段', 1);
                    // 刷新列表
                    setTimeout(function() {
                        product_recommend_list();
                    }, 1000);
                } else {
                    show_gitter('检查完成', '所有产品系列记录的lang字段正常', 1);
                }
            } else {
                show_gitter('错误', '获取产品系列数据失败', 2);
            }
        },
        error: function(xhr, status, error) {
            show_gitter('错误', '请求失败，请检查网络连接', 2);
        }
    });
}

// 更新单条记录的lang字段
function updateRecordLang(id, langValue) {
    $.ajax({
        type: "post",
        url: "/apis/update_product_recommend/",
        async: false,
        data: {
            filters: JSON.stringify({'id': id}),
            lang: langValue
        },
        success: function(data) {
            if (data.status === 'ok') {
            } else {
            }
        },
        error: function(xhr, status, error) {
        }
    });
} 