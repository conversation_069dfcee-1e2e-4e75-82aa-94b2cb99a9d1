<!DOCTYPE html>
<html lang="en">
<head>
    <title class="i18n" name="login.title"></title><meta charset="UTF-8" />
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <!-- 添加管理后台标识 -->
    <meta name="app-type" content="admin-panel" />
    <meta name="client-type" content="admin" />
    <link rel="stylesheet" href="css/bootstrap.min.css"/>
    <link rel="stylesheet" href="css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="css/colorpicker.css"/>
    <link rel="stylesheet" href="css/uniform.css"/>
    <link rel="stylesheet" href="css/fullcalendar.css"/>

    <link rel="stylesheet" href="css/matrix-style.css"/>
    <link rel="stylesheet" href="css/matrix-media.css"/>
    <link rel="stylesheet" href="css/bootstrap-wysihtml5.css"/>
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="css/jquery.gritter.css"/>
    <!-- 先加载jQuery -->
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.cookie.js"></script>
    <script src="js/jquery.i18n.js"></script>
    <script src="js/language.js"></script>
    <style>
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        #content {
            flex: 1;
            margin-left: 0 !important;
            border-left: 1px solid #ddd;
            background: #eee;
        }
        
        /* 修复模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: #000;
            opacity: 0.5;
        }
        
        /* 修复模态框居中问题 */
        .modal {
            width: 560px;
            margin-left: -280px;
            top: 50%;
            margin-top: -250px;
        }
        
        /* 确保通知显示在最上层 */
        #gritter-notice-wrapper {
            z-index: 9999999;
        }
        
        .gritter-item-wrapper {
            z-index: 9999999;
        }

        /* 合作伙伴图片预览 */
        .partner-img-preview {
            max-height: 80px;
            max-width: 200px;
            margin: 5px 0;
        }
    </style>
</head>
<body>

<!-- 先加载head.js，确保菜单正确显示 -->
<script src="js/head.js"></script>

<!-- 添加登录验证脚本 -->
<script>
    document.addEventListener('DOMContentLoaded', () => {
        if (sessionStorage.getItem('adminLoggedIn') !== 'true') {
            window.location.href = '/login/login.html';
        }
    });
</script>

<!-- 包装容器 -->
<div id="wrapper">
    <!--main-container-part-->
    <div id="content">
        <!--breadcrumbs-->
        <div id="content-header">
            <div id="breadcrumb">
                <a href="admin_home.html" title="返回首页" class="tip-bottom"><i class="icon-home"></i>首页</a>
                <a href="javascript:void(0);" class="current" onclick="return false;">合作伙伴管理</a></div>
        </div>
        <!--End-breadcrumbs-->

        <!--Action boxes-->
        <div class="container-fluid">
            <div class="widget-box">
                <div class="widget-content">
                    <div style="margin-bottom: 10px;">
                        <a class="btn btn-primary" href="#addPartnerAlert" data-toggle="modal"><i class="icon icon-plus i18n" name="common.add"> </i>新增合作伙伴</a>
                        <a class="btn btn-danger" href="javascript:logout()" title="退出登录"><i class="icon icon-off">退出登录 </i></a>
                    </div>

                    <!--  data list -->
                    <div class="widget-box" style="margin-top: 0;">
                        <div class="widget-content nopadding">
                            <table class="table table-bordered table-striped">
                                <thead>
                                <tr>
                                    <th>唯一ID</th>
                                    <th>图片</th>
                                    <th>链接地址</th>
                                    <th>是否显示</th>
                                    <th>显示顺序</th>
                                    <th>最后操作时间</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody id="data_list">
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- page -->
                    <div class="fg-toolbar ui-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix">
                        <div id="page"
                             class="dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_full_numbers"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑合作伙伴模态框 -->
        <div id="editPartnerAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold">编辑合作伙伴</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <form action="#" method="get" class="form-horizontal">
                            <div class="control-group">
                                <label class="control-label">是否显示</label>
                                <div class="controls">
                                    <select class="span8" id="edit_show">
                                        <option value="True">显示</option>
                                        <option value="False">不显示</option>
                                    </select>
                                </div>
                            </div>
                            <input type="hidden" id="edit_pid">
                            <div class="control-group">
                                <label class="control-label">图片</label>
                                <div class="controls">
                                    <img class="thumbnail partner-img-preview" id="edit_pic">
                                    <input type="file" id="edit_new_pic" accept="image/*"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接地址</label>
                                <div class="controls">
                                    <input type="text" class="span8" id="edit_url"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">显示顺序</label>
                                <div class="controls">
                                    <input type="number" class="span8" id="edit_pic_idx" value="0"/>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-primary" href="javascript:edit_partner()">保存</a>
                <a data-dismiss="modal" class="btn" href="#">取消</a>
            </div>
        </div>

        <!-- 添加合作伙伴模态框 -->
        <div id="addPartnerAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold">新增合作伙伴</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <form action="#" method="get" class="form-horizontal">
                            <div class="control-group">
                                <label class="control-label">是否显示</label>
                                <div class="controls">
                                    <select class="span8" id="add_show">
                                        <option value="True">显示</option>
                                        <option value="False">不显示</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">图片</label>
                                <div class="controls">
                                    <input type="file" id="add_pic" accept="image/*" required/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接地址</label>
                                <div class="controls">
                                    <input type="text" class="span8" id="add_url"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">显示顺序</label>
                                <div class="controls">
                                    <input type="number" class="span8" id="add_pic_idx" value="0"/>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="btn btn-primary" href="javascript:add_partner()">保存</a>
                <a data-dismiss="modal" class="btn" href="#">取消</a>
            </div>
        </div>

        <!-- 删除合作伙伴确认模态框 -->
        <div id="deletePartnerAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 18px;font-weight:bold">合作伙伴删除确认</span>
            </div>
            <div class="modal-body">
                <p style="font-size: 18px">
                    <i class="icon icon-warning-sign" style="font-size: 30px;color: red"></i>
                    <span style="margin-left: 20px">是否确认删除唯一ID为</span>
                    <span id="del_id_span" style="color: #1d00ff;font-weight: bold;font-size: 18px;"></span>
                    <span>的合作伙伴？</span>
                    <hr/>
                    <img id='del_pic' class="thumbnail partner-img-preview">
                    <input id="del_id" style="display: none">
                </p>
            </div>
            <div class="modal-footer">
                <a class="btn btn-danger" href="javascript:del_partner()">删除</a>
                <a data-dismiss="modal" class="btn" href="#">取消</a>
            </div>
        </div>
    </div>
</div>

<!--Footer-part-->
<script src="js/footer.js"></script>
<!--end-Footer-part-->

<!-- 其他脚本 -->
<script src="js/excanvas.min.js"></script>
<script src="js/jquery.ui.custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.peity.min.js"></script>
<script src="js/matrix.js"></script>
<script src="js/jquery.gritter.min.js"></script>
<script src="js/matrix.interface.js"></script>
<script src="js/jquery.validate.js"></script>
<script src="js/jquery.uniform.js"></script>
<script src="js/matrix.popover.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/page.js"></script>

<!-- 添加额外的修复脚本 -->
<script>
    // 修复模态框问题
    $(document).ready(function() {
        // 初始化时清理可能存在的模态框背景
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        
        // 修复Bootstrap 2.x模态框问题
        if (typeof $.fn.modal !== 'undefined') {
            var originalModal = $.fn.modal;
            $.fn.modal = function(option) {
                if (option === 'hide' || option === 'show' || typeof option === 'object') {
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                }
                return originalModal.apply(this, arguments);
            };
        }
        
        // 监听模态框事件
        $(document).on('hidden', '.modal', function() {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        $(document).on('click', '.modal-backdrop', function() {
            $('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        $(document).keyup(function(e) {
            if (e.keyCode === 27) {
                $('.modal').modal('hide');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        });

        // 加载合作伙伴列表
        partner_list(1, 10);
        
        // 自动高亮菜单
        if (typeof reset_menu === 'function') {
            reset_menu();
        }
    });

    // 加载合作伙伴列表
    function partner_list(page = 1, page_size = 10) {
        $.post('/apis/get_partner_pic/', {
            page: page,
            page_size: page_size
        }, function(res) {
            if (res.status === 'ok') {
                let html = '';
                res.data_list.forEach(item => {
                    // 改进显示状态的显示效果，使用标签样式并支持点击切换
                    const showStatus = item.show ? 
                        '<span class="badge badge-success toggle-show-status" data-id="'+item.id+'" data-show="1" style="cursor:pointer" title="点击切换显示状态">显示</span>' : 
                        '<span class="badge badge-important toggle-show-status" data-id="'+item.id+'" data-show="0" style="cursor:pointer" title="点击切换显示状态">不显示</span>';
                    
                    html += `
                        <tr>
                            <td>${item.id}</td>
                            <td><img src="${item.pic}" class="partner-img-preview"></td>
                            <td>${item.url || '-'}</td>
                            <td>${showStatus}</td>
                            <td>${item.pic_idx}</td>
                            <td>${formatDate(item.update_time || item.created_at)}</td>
                            <td>
                                <button class="btn btn-info btn-mini" onclick="show_edit_modal(${item.id})">
                                    <i class="icon-edit"></i> 编辑
                                </button>
                                <button class="btn btn-danger btn-mini" onclick="show_delete_modal(${item.id})">
                                    <i class="icon-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `;
                });
                $('#data_list').html(html);
                
                // 绑定显示状态切换事件
                $('.toggle-show-status').on('click', function() {
                    const id = $(this).data('id');
                    const currentShow = $(this).data('show');
                    toggle_show_status(id, currentShow === 0);
                });

                // 更新分页
                $("#page").paging({
                    pageNo: page,
                    totalPage: Math.ceil(res.total_data / page_size),
                    totalSize: res.total_data,
                    callback: function(num) {
                        partner_list(num, page_size);
                    }
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: res.msg || '加载数据失败',
                    class_name: 'gritter-error'
                });
            }
        });
    }

    // 显示编辑模态框
    function show_edit_modal(id) {
        $.post('/apis/get_partner_pic/', {
            filters: JSON.stringify({id: id})
        }, function(res) {
            if (res.status === 'ok' && res.data_list.length > 0) {
                const item = res.data_list[0];
                $('#edit_pid').val(item.id);
                $('#edit_show').val(item.show ? 'True' : 'False');
                $('#edit_pic').attr('src', item.pic);
                $('#edit_url').val(item.url);
                $('#edit_pic_idx').val(item.pic_idx);
                $('#editPartnerAlert').modal('show');
            }
        });
    }

    // 编辑合作伙伴
    function edit_partner() {
        const formData = new FormData();
        formData.append('filters', JSON.stringify({id: $('#edit_pid').val()}));
        formData.append('show', $('#edit_show').val());
        formData.append('url', $('#edit_url').val());
        formData.append('pic_idx', $('#edit_pic_idx').val());

        if ($('#edit_new_pic')[0].files[0]) {
            formData.append('file', $('#edit_new_pic')[0].files[0]);
        }

        $.ajax({
            url: '/apis/update_partner_pic/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                if (res.status === 'ok') {
                    $('#editPartnerAlert').modal('hide');
                    partner_list();
                    $.gritter.add({
                        title: '成功',
                        text: '更新成功',
                        class_name: 'gritter-success'
                    });
                } else {
                    $.gritter.add({
                        title: '错误',
                        text: res.msg || '更新失败',
                        class_name: 'gritter-error'
                    });
                }
            }
        });
    }

    // 添加合作伙伴
    function add_partner() {
        if (!$('#add_pic')[0].files[0]) {
            $.gritter.add({
                title: '错误',
                text: '请选择图片',
                class_name: 'gritter-error'
            });
            return;
        }

        const formData = new FormData();
        formData.append('file', $('#add_pic')[0].files[0]);
        formData.append('show', $('#add_show').val());
        formData.append('url', $('#add_url').val());
        formData.append('pic_idx', $('#add_pic_idx').val());

        $.ajax({
            url: '/apis/create_partner_pic/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                if (res.status === 'ok') {
                    $('#addPartnerAlert').modal('hide');
                    partner_list();
                    $.gritter.add({
                        title: '成功',
                        text: '添加成功',
                        class_name: 'gritter-success'
                    });
                } else {
                    $.gritter.add({
                        title: '错误',
                        text: res.msg || '添加失败',
                        class_name: 'gritter-error'
                    });
                }
            }
        });
    }

    // 显示删除确认框
    function show_delete_modal(id) {
        $.post('/apis/get_partner_pic/', {
            filters: JSON.stringify({id: id})
        }, function(res) {
            if (res.status === 'ok' && res.data_list.length > 0) {
                const item = res.data_list[0];
                $('#del_id').val(item.id);
                $('#del_id_span').text(item.id);
                $('#del_pic').attr('src', item.pic);
                $('#deletePartnerAlert').modal('show');
            }
        });
    }

    // 删除合作伙伴
    function del_partner() {
        const id = $('#del_id').val();
        $.post('/apis/delete_partner_pic/', {
            filters: JSON.stringify({id: id})
        }, function(res) {
            if (res.status === 'ok') {
                $('#deletePartnerAlert').modal('hide');
                partner_list();
                $.gritter.add({
                    title: '成功',
                    text: '删除成功',
                    class_name: 'gritter-success'
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: res.msg || '删除失败',
                    class_name: 'gritter-error'
                });
            }
        });
    }

    // 切换合作伙伴显示状态
    function toggle_show_status(id, newShow) {
        // 先获取当前项的数据，保留原有的排序值
        $.post('/apis/get_partner_pic/', {
            filters: JSON.stringify({id: id})
        }, function(res) {
            if (res.status === 'ok' && res.data_list.length > 0) {
                const item = res.data_list[0];
                const picIdx = item.pic_idx || 0; // 获取当前的排序值
                
                // 确保newShow是布尔值转成的数字: true -> 1, false -> 0
                const showValue = newShow ? 1 : 0;
                
                // 更新显示状态，同时保留原有排序值
                $.ajax({
                    url: '/apis/update_partner_pic/',
                    type: 'POST',
                    data: {
                        filters: JSON.stringify({id: id}),
                        show: showValue,  // 使用数字值1或0
                        pic_idx: picIdx   // 保留原有的排序值
                    },
                    success: function(res) {
                        if (res.status === 'ok') {
                            // 显示成功提示
                            $.gritter.add({
                                title: '成功',
                                text: '显示状态更新成功',
                                class_name: 'gritter-success'
                            });
                            
                            // 刷新列表
                            partner_list();
                        } else {
                            $.gritter.add({
                                title: '错误',
                                text: res.msg || '显示状态更新失败',
                                class_name: 'gritter-error'
                            });
                        }
                    },
                    error: function() {
                        $.gritter.add({
                            title: '错误',
                            text: '网络错误，显示状态更新失败',
                            class_name: 'gritter-error'
                        });
                    }
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: '获取合作伙伴数据失败',
                    class_name: 'gritter-error'
                });
            }
        });
    }

    // 分页回调函数已在paging配置中定义，不再需要单独的函数
    
    // 格式化日期显示
    function formatDate(dateStr) {
        if (!dateStr) return '-';
        
        try {
            // 解析日期字符串
            const date = new Date(dateStr);
            
            // 检查日期是否有效
            if (isNaN(date.getTime())) return dateStr;
            
            // 检查年份是否合理（避免显示2025年这种未来时间）
            const currentYear = new Date().getFullYear();
            if (date.getFullYear() > currentYear) {
                // 如果年份是未来的，可能是时区问题，尝试修正
                date.setFullYear(currentYear);
            }
            
            // 格式化日期
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        } catch (e) {
            console.error('日期格式化错误:', e);
            return dateStr || '-';
        }
    }
</script>
</body>
</html> 