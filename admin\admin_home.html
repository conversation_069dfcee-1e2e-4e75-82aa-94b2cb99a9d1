<!DOCTYPE html>
<html lang="en">
<head>
    <title class="i18n" name="login.title"></title><meta charset="UTF-8" />
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <!-- 添加管理后台标识 -->
    <meta name="app-type" content="admin-panel" />
    <meta name="client-type" content="admin" />
    <link rel="stylesheet" href="css/bootstrap.min.css"/>
    <link rel="stylesheet" href="css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="css/colorpicker.css"/>
    <!--<link rel="stylesheet" href="css/datepicker.css"/>-->
    <link rel="stylesheet" href="css/uniform.css"/>
    <!--<link rel="stylesheet" href="css/select2.css"/>-->
    <link rel="stylesheet" href="css/fullcalendar.css"/>

    <link rel="stylesheet" href="css/matrix-style.css"/>
    <link rel="stylesheet" href="css/matrix-media.css"/>
    <link rel="stylesheet" href="css/bootstrap-wysihtml5.css"/>
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="css/jquery.gritter.css"/>
    <!--<link rel="stylesheet" href="css/page.css"/>-->
    <!-- 先加载jQuery -->
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.cookie.js"></script>
    <script src="js/jquery.i18n.js"></script>
    <script src="js/language.js"></script>
    <style>
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        #content {
            flex: 1;
            margin-left: 0 !important;
            border-left: 1px solid #ddd;
            background: #eee;
        }
        
        /* 修复模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: #000;
            opacity: 0.5;
        }
        
        /* 修复模态框居中问题 */
        .modal {
            width: 560px;
            margin-left: -280px;
            top: 50%;
            margin-top: -250px;
        }
        
        /* 确保通知显示在最上层 */
        #gritter-notice-wrapper {
            z-index: 9999999;
        }
        
        .gritter-item-wrapper {
            z-index: 9999999;
        }
    </style>
</head>
<body>

<!-- 先加载head.js，确保菜单正确显示 -->
<script src="js/head.js"></script>

<!-- 添加登录验证脚本 -->
<script>
    // 登录验证
    document.addEventListener('DOMContentLoaded', () => {
        // 检查是否已登录
        if (sessionStorage.getItem('adminLoggedIn') !== 'true') {
            // 未登录，跳转到登录页面
            window.location.href = '/login/login.html';
        }
    });

    // 注意：退出登录功能已移至head.js中的logout函数
</script>

<!-- 包装容器 -->
<div id="wrapper">
    <!--main-container-part-->
    <div id="content">
        <!--breadcrumbs-->
        <div id="content-header">
            <div id="breadcrumb">
                <!--<a href="index.html" title="Go to Home" class="tip-bottom"><i class="icon-home"></i>Home</a>-->
                <a href="javascript:void(0);" class="current" onclick="return false;" id="page_title">首页轮播图片编辑</a></div>
        </div>
        <!--End-breadcrumbs-->

        <!--Action boxes-->
        <div class="container-fluid">
            <div class="widget-box">
                <div class="widget-content">
                    <div style="margin-bottom: 10px; display: flex; align-items: center;">
                        <select id="lang_filter" class="form-control" style="width: 160px; display: inline-block; margin-right: 8px;">
                            <option value="">中/英文网站</option>
                            <option value="0">中文网站</option>
                            <option value="1">英文网站</option>
                        </select>
                        <a class="btn btn-primary" href="#addAlert" data-toggle="modal"><i class="icon icon-plus i18n" name="common.add"> </i>新增</a>
                        <a class="btn btn-danger" href="javascript:logout()" title="退出登录" style="margin-left: 5px;"><i class="icon icon-off">退出登录 </i></a>
                    </div>

                    <!--  data list -->
                    <div class="widget-box" style="margin-top: 0;">
                        <div class="widget-content nopadding">
                            <table class="table table-bordered table-striped">
                                <thead>
                                <tr>
                                    <th>唯一ID</th>
                                    <th>网站类型</th>
                                    <th>图片</th>
                                    <th>标题</th>
                                    <th>描述</th>
                                    <th>链接地址</th>
                                    <th>是否显示</th>
                                    <th>显示顺序</th>
                                    <th>最后操作时间</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody id="data_list">

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- page -->
                    <div class="fg-toolbar ui-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix">
                        <div id="page"
                             class="dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_full_numbers"></div>
                    </div>
                </div>
            </div>
        </div>
        <div id="editAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold" id="edit_modal_title">编辑首页图片</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <!--<div class="widget-box">-->
                        <form action="#" method="get" class="form-horizontal">
                            <div class="control-group">
                                <label class="control-label i18n" name="product_cls.show"></label>
                                <div class="controls">
                                    <select class="span8" id="edit_show">
                                        <option value="True" class="i18n" name="common.show"></option>
                                        <option value="False" class="i18n" name="common.unshow"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group" id="edit_lang_group">
                                <label class="control-label">网站类型</label>
                                <div class="controls">
                                    <select class="span8" id="edit_lang">
                                        <option value="0">中文网站</option>
                                        <option value="1">英文网站</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">顺序</label>
                                <input type="text" style="display: none" id="edit_pid">
                                <div class="controls" >
                                    <input type="text" class="span8"   selectattr="placeholder"  id="edit_px" />
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label" >*小图(1920*720)</label>
                                <div class="controls" >
                                    <img class="thumbnail" style="max-height: 80px;display: inline" id="edit_pic">
                                    <input type="file"   selectattr="placeholder"  id="edit_new_pic" accept="image/png,image/jpeg"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接地址</label>
                                <div class="controls" >
                                    <input type="text" class="span8"   selectattr="placeholder"  id="edit_url"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">标题</label>
                                <div class="controls" >
                                    <input type="text" class="span8"   selectattr="placeholder"  id="edit_title"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">描述</label>
                                <div class="controls" >
                                    <textarea class="span8" rows="3" id="edit_description"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <a class="btn btn-primary i18n" name="common.save" href="javascript:edit_item()"></a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#"></a>
            </div>
        </div>

        <div id="addAlert" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 16px;font-weight:bold" id="add_modal_title">新增首页图片</span>
            </div>
            <div class="modal-body">
                <div class="row-fluid">
                    <div class="span12">
                        <!--<div class="widget-box">-->
                        <form action="#" method="get" class="form-horizontal">

                            <div class="control-group">
                                <label class="control-label i18n" name="product_cls.show"></label>
                                <div class="controls">
                                    <select class="span8" id="add_show">
                                        <option value="True" class="i18n" name="common.show"></option>
                                        <option value="False" class="i18n" name="common.unshow"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group" id="add_lang_group">
                                <label class="control-label">网站类型</label>
                                <div class="controls">
                                    <select class="span8" id="add_lang">
                                        <option value="0">中文网站</option>
                                        <option value="1">英文网站</option>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label i18n" name="product_cls.show_idx"></label>
                                <div class="controls" >
                                    <input type="text" class="span8"  selectattr="placeholder" value="1" id="add_show_idx"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label" >*小图(1920*720)</label>
                                <div class="controls" >
                                    <img class="thumbnail" style="max-height: 60px;display: inline" id="add_pic">
                                    <input type="file"   selectattr="placeholder"  id="add_new_pic" accept="image/png,image/jpeg"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">链接地址</label>
                                <div class="controls" >
                                    <input type="text" class="span8"  selectattr="placeholder" value="" id="hurl"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">标题</label>
                                <div class="controls" >
                                    <input type="text" class="span8"  selectattr="placeholder" value="" id="add_title"/>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label">描述</label>
                                <div class="controls" >
                                    <textarea class="span8" rows="3" id="add_description"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <a class="btn btn-primary i18n" name="common.save" href="javascript:add_one()"></a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#"></a>
            </div>
        </div>

        <div id="myDelete" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 18px;font-weight:bold">首页图片删除确认</span>
            </div>
            <div class="modal-body">
                <p style="font-size: 18px">
                    <i class="icon icon-warning-sign " style="font-size: 30px;color: red"></i>
                    <span style="margin-left: 20px">是否确认删除唯一ID为</span>
                    <span id="del_id_span" style="color: #1d00ff;font-weight: bold;font-size: 18px;"></span>
                    <span >的下列首页图片？</span>
                    <hr/>
                    <img id='del_img' class="thumbnail" style="max-height: 240px;display: inline" src="">
                    <input id="del_id" style="display: none">

                </p>
            </div>
            <div class="modal-footer">
                <a class="btn btn-danger i18n" name="common.del" href="javascript:del_item()"></a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#"></a>
            </div>
        </div>

        <div id="makeClsJson" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 18px;font-weight:bold" >中文首页重新生成确认</span>
            </div>
            <div class="modal-body">
                <p>
                    <i class="icon icon-warning-sign " style="font-size: 30px;color: red"></i>
                    <span style="margin-left: 18px;font-size: 18px;" >是否确认重新生成中文公司首页？</span>

                </p>
            </div>
            <div class="modal-footer">
                <a class="btn btn-danger" href="javascript:make_index_html()">确认</a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#"></a>
            </div>
        </div>
        <div id="makeClsJson_en" class="modal hide">
            <div class="modal-header">
                <button data-dismiss="modal" class="close" type="button">×</button>
                <span style="font-size: 18px;font-weight:bold" >英文首页重新生成确认</span>
            </div>
            <div class="modal-body">
                <p>
                    <i class="icon icon-warning-sign " style="font-size: 30px;color: red"></i>
                    <span style="margin-left: 18px;font-size: 18px;" >是否确认重新生成英文公司首页？</span>

                </p>
            </div>
            <div class="modal-footer">
                <a class="btn btn-danger" href="javascript:make_index_html_en()">确认</a>
                <a data-dismiss="modal" class="btn i18n" name="common.cancel" href="#"></a>
            </div>
        </div>
    </div>
    <!--end-main-container-part-->
</div>

<!--Footer-part-->
<script src="js/footer.js"></script>
<!--end-Footer-part-->

<!-- 其他脚本 -->
<script src="js/excanvas.min.js"></script>
<script src="js/jquery.ui.custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.peity.min.js"></script>
<script src="js/fullcalendar.min.js"></script>
<script src="js/matrix.js"></script>
<script src="js/jquery.gritter.min.js"></script>
<script src="js/matrix.interface.js"></script>
<script src="js/matrix.chat.js"></script>
<script src="js/jquery.validate.js"></script>
<script src="js/matrix.form_validation.js"></script>
<script src="js/jquery.wizard.js"></script>
<script src="js/jquery.uniform.js"></script>
<script src="js/matrix.popover.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/page.js"></script>
<script src="js/home.js"></script>

<!-- 添加额外的修复脚本 -->
<script>
    // 修复模态框问题
    $(document).ready(function() {
        // 初始化时清理可能存在的模态框背景
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        
        // 显示最后一次操作时间
        function updateLastOperationTime() {
            var operationRecords = localStorage.getItem('operationRecords');
            if (!operationRecords) {
                $('#last-operation-time').text('最后操作时间: 暂无记录');
                return;
            }
            
            var records = JSON.parse(operationRecords);
            var lastTime = 0;
            var lastTimeStr = '';
            
            // 查找最近的操作时间
            for (var id in records) {
                if (records[id].time > lastTime) {
                    lastTime = records[id].time;
                    lastTimeStr = records[id].timeStr;
                }
            }
            
            if (lastTimeStr) {
                $('#last-operation-time').text('最后操作时间: ' + lastTimeStr);
            } else {
                $('#last-operation-time').text('最后操作时间: 暂无记录');
            }
        }
        
        // 初始化显示
        updateLastOperationTime();
        
        // 每分钟更新一次
        setInterval(updateLastOperationTime, 60000);
        
        // 修复Bootstrap 2.x模态框问题
        if (typeof $.fn.modal !== 'undefined') {
            // 保存原始modal方法
            var originalModal = $.fn.modal;
            
            // 重写modal方法
            $.fn.modal = function(option) {
                console.log("调用模态框方法:", option);
                
                if (option === 'hide') {
                    // 隐藏模态框时清理背景
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // 调用原始方法
                    return originalModal.apply(this, arguments);
                }
                else if (option === 'show' || typeof option === 'object') {
                    // 显示模态框前清理背景
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // 调用原始方法
                    return originalModal.apply(this, arguments);
                }
                
                // 其他情况调用原始方法
                return originalModal.apply(this, arguments);
            };
        }
        
        // 监听模态框关闭事件，确保背景被移除
        $(document).on('hidden', '.modal', function() {
            console.log("模态框关闭，清理背景");
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        // 监听点击事件，修复灰色遮罩无法点击的问题
        $(document).on('click', '.modal-backdrop', function() {
            console.log("点击了模态框背景，关闭所有模态框");
            $('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        
        // 监听ESC键，确保模态框可以关闭
        $(document).keyup(function(e) {
            if (e.keyCode === 27) { // ESC键
                console.log("按下ESC键，关闭所有模态框");
                $('.modal').modal('hide');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        });
        
        // 监听模态框按钮点击事件
        $('.modal .btn').on('click', function() {
            console.log("点击了模态框按钮:", $(this).text());
        });
    });
</script>

<script type="text/javascript">
    // This function is called from the pop-up menus to transfer to
    // a different page. Ignore if the value returned is a null string:
    function goPage(newURL) {
        // if url is empty, skip the menu dividers and reset the menu selection to default
        if (newURL != "") {
            // if url is "-", it is this page -- reset the menu:
            if (newURL == "-") {
                resetMenu();
            }
            // else, send page to designated URL
            else {
                document.location.href = newURL;
            }
        }
    }

    // resets the menu selection upon entry to this page:
    function resetMenu() {
        document.gomenu.selector.selectedIndex = 2;
    }

    $(function () {
        reset_menu();
        
        // 确保在页面加载完成后清理模态框背景
        setTimeout(function() {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            
            // 从URL参数中获取lang值
            var urlParams = new URLSearchParams(window.location.search);
            var lang = urlParams.get('lang');
            
            // 如果URL中有lang参数，则使用该参数；否则默认显示所有
            if (lang !== null) {
                $('#lang_filter').val(lang);
                home_pic_lst(1, 15, lang);
                
                // 根据语言类型更新页面标题
                if (lang === '0') {
                    document.title = "中文网站轮播图管理";
                    $('#page_title').text('中文网站轮播图管理');
                } else if (lang === '1') {
                    document.title = "英文网站轮播图管理";
                    $('#page_title').text('英文网站轮播图管理');
                }
            } else {
                home_pic_lst();
            }
            
            // 监听语言筛选器变化
            $('#lang_filter').on('change', function() {
                var selectedLang = $(this).val();
                // 更新URL参数，方便刷新页面时保持筛选状态
                var url = new URL(window.location.href);
                if (selectedLang !== '') {
                    url.searchParams.set('lang', selectedLang);
                } else {
                    url.searchParams.delete('lang');
                }
                window.history.replaceState({}, '', url);
                
                // 重新加载数据
                home_pic_lst(1, 15, selectedLang);
            });
        }, 500);
    });
</script>

<!-- 添加同步时间记录的函数 -->
<script>
    // 添加日期时间格式化函数
    function formatDateTime(timestamp) {
        if (!timestamp) return '未记录';
        
        try {
            // 尝试将时间戳转换为Date对象
            var date;
            if (typeof timestamp === 'number') {
                date = new Date(timestamp);
            } else if (typeof timestamp === 'string') {
                // 尝试解析字符串格式的时间戳
                if (timestamp.indexOf('T') > -1) {
                    // ISO格式: 2023-01-01T12:00:00.000Z
                    date = new Date(timestamp);
                } else if (!isNaN(Number(timestamp))) {
                    // 数字字符串: "1609459200000"
                    date = new Date(Number(timestamp));
                } else {
                    // 其他格式的日期字符串
                    date = new Date(timestamp);
                }
            } else {
                return '格式错误';
            }
            
            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                return '日期无效';
            }
            
            // 格式化日期和时间
            var year = date.getFullYear();
            var month = padZero(date.getMonth() + 1);
            var day = padZero(date.getDate());
            var hours = padZero(date.getHours());
            var minutes = padZero(date.getMinutes());
            var seconds = padZero(date.getSeconds());
            
            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
        } catch (e) {
            console.error('日期格式化错误:', e, timestamp);
            return '格式错误';
        }
    }

    // 补零函数
    function padZero(num) {
        return num < 10 ? '0' + num : num;
    }
</script>

<!-- 添加页面加载完成后的初始化脚本 -->
<script>
    // 确保页面和所有脚本完全加载后再初始化数据
    $(window).on('load', function() {
        console.log("页面完全加载完成，准备初始化数据...");
        setTimeout(function() {
            try {
                if (typeof home_pic_lst === 'function') {
                    console.log("调用home_pic_lst函数...");
                    // 明确传递参数，避免参数不一致问题
                    home_pic_lst(1, 15);
                } else {
                    console.error("home_pic_lst函数仍未定义");
                }
            } catch (e) {
                console.error("初始化数据失败:", e);
            }
        }, 500);
    });
</script>

<script>
    // 页面加载后自动高亮菜单
    $(function () {
        reset_menu();
    });
</script>

<script>
    // 监听筛选下拉框变化，筛选轮播图数据
    $(function() {
        $('#lang_filter').on('change', function() {
            var lang = $(this).val();
            if (typeof home_pic_lst === 'function') {
                // 正确传递参数：页码、每页数量、语言
                home_pic_lst(1, 15, lang);
            }
        });
    });
</script>

</body>
</html>
