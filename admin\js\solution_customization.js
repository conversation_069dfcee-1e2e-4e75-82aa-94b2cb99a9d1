/**
 * 方案定制管理JavaScript
 */

// 全局变量
var currentPage = 1;
var pageSize = 10;
var totalPage = 0;

/**
 * 加载方案定制列表
 * @param {number} page 页码
 * @param {number} size 每页条数
 */
function load_customization_list(page, size) {
    currentPage = page || 1;
    pageSize = size || 10;
    
    // 构建请求参数
    var params = {
        page: currentPage,
        size: pageSize
    };
    
    // 发送请求获取数据
    $.ajax({
        type: "get",
        url: "/apis/business_customization/list",
        data: params,
        success: function (data) {
            if (data.status === 'ok') {
                render_customization_list(data.data);
                totalPage = Math.ceil(data.total / pageSize);
                page_ctrl(currentPage, totalPage);
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '加载方案定制列表失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，加载方案定制列表失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 渲染方案定制列表
 * @param {Array} data 方案定制数据
 */
function render_customization_list(data) {
    var html = '';
    
    if (!data || data.length === 0) {
        html = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
    } else {
        $.each(data, function (index, item) {
            // 处理需求描述的显示，不再截断文本
            var requirementsText = item.requirements || '';
            
            html += '<tr>' +
                '<td>' + (item.id || '') + '</td>' +
                '<td>' + (item.name || '') + '</td>' +
                '<td>' + (item.phone || '') + '</td>' +
                '<td>' + (item.email || '') + '</td>' +
                '<td>' + (item.company || '') + '</td>' +
                '<td>' + (item.custom_type || '') + '</td>' +
                '<td style="white-space: pre-wrap; word-break: break-word;">' + requirementsText + '</td>' +
                '<td>' + formatDateTime(item.submit_time) + '</td>' +
                '<td>' +
                '<button class="btn btn-danger btn-mini" onclick="show_delete_modal(' + item.id + ')">删除</button>' +
                '</td>' +
                '</tr>';
        });
    }
    
    $("#data_list").html(html);
}

/**
 * 显示删除确认模态框
 * @param {number} id 记录ID
 */
function show_delete_modal(id) {
    // 发送请求获取记录详情
    $.ajax({
        type: "get",
        url: "/apis/business_customization/detail",
        data: {
            id: id
        },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                
                // 填充确认信息
                $('#del_id').val(item.id);
                $('#del_id_span').text(item.id);
                $('#del_name').text(item.name);
                $('#del_company').text(item.company);
                $('#del_requirements').text(item.requirements);
                
                // 显示模态框
                $('#deleteCustomizationAlert').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '获取记录详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，获取记录详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 删除方案定制记录
 */
function del_customization() {
    var id = $('#del_id').val();
    
    // 发送删除请求
    $.ajax({
        type: "post",
        url: "/apis/business_customization/delete",
        data: {
            id: id
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#deleteCustomizationAlert').modal('hide');
                
                // 显示成功消息
                $.gritter.add({
                    title: '成功',
                    text: '删除记录成功',
                    sticky: false,
                    time: 3000
                });
                
                // 重新加载数据
                load_customization_list(currentPage, pageSize);
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '删除记录失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，删除记录失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 分页控制
 * @param {number} currentPage 当前页码
 * @param {number} totalPage 总页数
 */
function page_ctrl(currentPage, totalPage) {
    var html = "";
    
    // 如果总页数小于等于1，不显示分页
    if (totalPage <= 1) {
        $("#page").html(html);
        return;
    }
    
    // 上一页按钮
    if (currentPage > 1) {
        html += '<a class="first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default" onclick="load_customization_list(' + (currentPage - 1) + ', ' + pageSize + ')">上一页</a>';
    } else {
        html += '<a class="first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default ui-state-disabled">上一页</a>';
    }
    
    // 页码按钮
    var startPage = Math.max(1, currentPage - 2);
    var endPage = Math.min(totalPage, startPage + 4);
    
    if (startPage > 1) {
        html += '<a class="fg-button ui-button ui-state-default" onclick="load_customization_list(1, ' + pageSize + ')">1</a>';
        if (startPage > 2) {
            html += '<span class="fg-button ui-button ui-state-default">...</span>';
        }
    }
    
    for (var i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            html += '<a class="fg-button ui-button ui-state-default ui-state-disabled">' + i + '</a>';
        } else {
            html += '<a class="fg-button ui-button ui-state-default" onclick="load_customization_list(' + i + ', ' + pageSize + ')">' + i + '</a>';
        }
    }
    
    if (endPage < totalPage) {
        if (endPage < totalPage - 1) {
            html += '<span class="fg-button ui-button ui-state-default">...</span>';
        }
        html += '<a class="fg-button ui-button ui-state-default" onclick="load_customization_list(' + totalPage + ', ' + pageSize + ')">' + totalPage + '</a>';
    }
    
    // 下一页按钮
    if (currentPage < totalPage) {
        html += '<a class="last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default" onclick="load_customization_list(' + (currentPage + 1) + ', ' + pageSize + ')">下一页</a>';
    } else {
        html += '<a class="last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default ui-state-disabled">下一页</a>';
    }
    
    $("#page").html(html);
}

/**
 * 格式化日期时间
 * @param {string} datetime 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(datetime) {
    if (!datetime) {
        return '';
    }
    
    try {
        var date = new Date(datetime);
        
        if (isNaN(date.getTime())) {
            // 尝试解析其他格式
            if (typeof datetime === 'string') {
                // 尝试处理MySQL格式的日期时间
                var parts = datetime.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/);
                if (parts) {
                    date = new Date(parts[1], parts[2] - 1, parts[3], parts[4], parts[5], parts[6]);
                }
            }
            
            if (isNaN(date.getTime())) {
                return datetime; // 无法解析，返回原始值
            }
        }
        
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        var seconds = ('0' + date.getSeconds()).slice(-2);
        
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    } catch (e) {
        console.error('格式化日期时间错误:', e);
        return datetime; // 发生错误，返回原始值
    }
}

// 页面加载完成后执行
$(document).ready(function() {
    // 加载方案定制列表
    load_customization_list(1, 10);
}); 