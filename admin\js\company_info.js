/**
 * 公司详情管理JS
 * 实现公司简介、公司Logo、办公地址、联系方式、产品方案、关注我们等内容的管理
 */

// 全局变量
var currentPage = 1;
var pageSize = 10;
var totalPage = 0;
var currentLang = '';
var currentInfoType = '';

/**
 * 加载公司详情列表
 * @param {number} page 页码
 * @param {number} size 每页条数
 * @param {string} lang 语言筛选（可选）
 * @param {string} infoType 信息类型筛选（可选）
 */
function company_info_list(page, size, lang, infoType) {
    currentPage = page || 1;
    pageSize = size || 10;
    currentLang = lang || '';
    currentInfoType = infoType || '';
    
    // 构建请求参数
    var params = {
        page: currentPage,
        size: pageSize
    };
    
    // 添加筛选条件
    if (currentLang !== '') {
        params.lang = currentLang;
    }
    if (currentInfoType !== '') {
        params.info_type = currentInfoType;
    }
    
    // 发送请求获取数据
    $.ajax({
        type: "get",
        url: "/apis/company_info/list",
        data: params,
        success: function (data) {
            if (data.status === 'ok') {
                // 渲染数据列表
                render_company_info_list(data.data);
                
                // 更新分页
                totalPage = Math.ceil(data.total / pageSize);
                page_ctrl(currentPage, totalPage);
                
                // 添加显示顺序说明
                addDisplayOrderHelp();
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '加载公司详情列表失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，加载公司详情列表失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 渲染公司详情列表
 * @param {Array} data 公司详情数据
 */
function render_company_info_list(data) {
    var html = '';
    
    if (!data || data.length === 0) {
        html = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
    } else {
        $.each(data, function (index, item) {
            // 网站类型 - 添加样式
            var langStyle = '';
            if(item.lang === 0) {
                var langText = '<span class="label label-success">中文网站</span>';
                var tr_bg_color = '#d2d2f7'; // 中文内容背景色
            } else {
                var langText = '<span class="label label-info">英文网站</span>';
                var tr_bg_color = 'none'; // 英文内容无背景色
            }
            
            // 信息类型
            var infoTypeText = getInfoTypeText(item.info_type);
            
            // 标题（可能为空）
            var titleDisplay = item.title || '(无标题)';
            
            // 检查是否有图片内容
            var hasImage = item.image_path && item.image_path.trim() !== '';
            
            // 检查是否有文本内容
            var hasText = item.content && item.content.trim() !== '';
            
            // 图片预览和文本预览分开显示
            var imagePreview = '';
            var textPreview = '';
            
            // 处理图片预览
            if (hasImage) {
                // 确保图片路径正确
                var imgSrc = item.image_path;
                
                // 处理绝对路径问题
                if (imgSrc && imgSrc.includes('C:')) {
                    // 如果是Windows绝对路径，提取文件名
                    var fileName = imgSrc.split('\\').pop().split('/').pop();
                    imgSrc = '/uploads/' + fileName;
                } else if (imgSrc && !imgSrc.startsWith('http') && !imgSrc.startsWith('/')) {
                    // 相对路径，添加前导斜杠
                    imgSrc = '/' + imgSrc;
                }
                
                imagePreview = '<div class="preview-image"><img src="' + imgSrc + '" class="company-logo" onerror="this.onerror=null;this.src=\'/admin/media/no-image.png\';this.style.opacity=0.5;"></div>';
            } else {
                // 没有图片时显示占位符
                imagePreview = '<div class="preview-empty">无图片</div>';
            }
            
            // 处理链接地址（主要针对产品方案类型）
            var urlDisplay = '';
            if (item.info_type === 'product_solution' && item.url) {
                urlDisplay = '<br/><small><strong>链接:</strong> <a href="' + item.url + '" target="_blank">' + item.url + '</a></small>';
            }
            
            // 文本预览
            if (hasText) {
                // 文本内容截取显示
                var textContent = item.content.length > 50 ? 
                    item.content.substring(0, 50) + '...' : 
                    item.content;
                
                textPreview = '<div class="preview-text">' + textContent + urlDisplay + '</div>';
            } else {
                // 没有文本时显示占位符，但如果有链接，显示链接
                textPreview = '<div class="preview-empty">无文本' + urlDisplay + '</div>';
            }
            
            // 始终使用两列布局，即使其中一列为空
            var contentPreview = '<div class="preview-container">';
            contentPreview += '<div class="preview-column image-column">' + imagePreview + '</div>';
            contentPreview += '<div class="preview-column text-column">' + textPreview + '</div>';
            contentPreview += '</div>';
            
            // 显示顺序
            var displayOrder = item.display_order || 100;
            
            // 是否显示 - 添加可点击的显示状态标签
            var isShow = item.show === true || item.show === 1 || item.show === "1";
            var showText = isShow ? 
                '<span class="badge badge-success toggle-show-status" data-id="' + item.id + '" data-show="1" style="cursor:pointer" title="点击切换显示状态">显示</span>' : 
                '<span class="badge badge-important toggle-show-status" data-id="' + item.id + '" data-show="0" style="cursor:pointer" title="点击切换显示状态">不显示</span>';
            
            // 格式化时间
            var updateTime = formatDateTime(item.update_time);
            
            // 使用背景色区分中英文内容
            html += '<tr>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + item.id + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + langText + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + infoTypeText + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + titleDisplay + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + contentPreview + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + displayOrder + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + showText + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' + updateTime + '</td>' +
                '<td style="text-align: center; vertical-align: middle; background:' + tr_bg_color + '">' +
                '<button class="btn btn-primary btn-mini" onclick="show_edit_modal(' + item.id + ')">编辑</button> ' +
                '<button class="btn btn-danger btn-mini" onclick="show_delete_modal(' + item.id + ')">删除</button>' +
                '</td>' +
                '</tr>';
        });
    }
    
    $('#data_list').html(html);
    
    // 添加显示状态切换事件
    $('.toggle-show-status').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        var id = $(this).data('id');
        var currentShow = $(this).data('show');
        var newShow = currentShow === 1 ? 0 : 1;
        
        // 调用专门的更新显示状态函数
        updateShowStatus(id, newShow);
    });
}

/**
 * 获取信息类型的显示文本
 * @param {string} infoType 信息类型
 * @returns {string} 显示文本
 */
function getInfoTypeText(infoType) {
    switch (infoType) {
        case 'company_profile':
            return '公司简介';
        case 'company_logo':
            return '公司Logo';
        case 'address':
            return '办公地址';
        case 'contact':
            return '联系方式';
        case 'product_solution':
            return '产品中心';
        case 'hot_products':
            return '热门产品';
        case 'follow_us':
            return '关注我们';
        case 'service_support':
            return '服务与支持';
        case 'about_banner':
            return '关于我们-Banner';
        case 'technology_highlight':
            return '关于我们-公司简介';
        case 'our_technology':
            return '关于我们-我们的技术';
        case 'wiki_banner':
            return '维基教程-大图';
        case 'wiki_detail':
            return '维基教程-详情';
        case 'navbar':
            return '网页导航栏';
        case 'company_news':
            return '贝启科技动态-首页';
        case 'company_news_detail':
            return '贝启科技动态';
        case 'about_company_home':
            return '关于贝启科技-首页';
        case 'toolbar':
            return '工具栏';
        case 'solution_customize':
            return '方案定制';
        case 'solution_customize_detail':
            return '方案定制-详情';
        default:
            return infoType;
    }
}

/**
 * 格式化日期时间
 * @param {string} dateTimeStr 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    
    var date = new Date(dateTimeStr);
    var year = date.getFullYear();
    var month = ('0' + (date.getMonth() + 1)).slice(-2);
    var day = ('0' + date.getDate()).slice(-2);
    var hours = ('0' + date.getHours()).slice(-2);
    var minutes = ('0' + date.getMinutes()).slice(-2);
    var seconds = ('0' + date.getSeconds()).slice(-2);
    
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}

/**
 * 添加公司详情
 */
function add_info() {
    // 获取表单数据
    var show = $('#add_show').val() === 'True';
    var lang = $('#add_lang').val();
    var infoType = $('#add_info_type').val();
    var title = $('#add_title').val().trim();
    var url = $('#add_url').val().trim();
    var displayOrder = $('#add_display_order').val() || 100;
    
    // 获取内容
    var content = $('#add_content').val().trim();
    var file = document.getElementById('add_new_image').files[0];
    var imageRemoved = $('#add_image_removed').val() === 'true';
    
    // 验证必填字段
    if (infoType === 'company_logo' && !file && !imageRemoved) {
        $.gritter.add({
            title: '错误',
            text: '请上传Logo图片',
            sticky: false,
            time: 3000
        });
        return;
    }
    
    // 创建FormData对象
    var formData = new FormData();
    formData.append('show', show);
    formData.append('lang', lang);
    formData.append('info_type', infoType);
    formData.append('title', title); // 即使为空也添加
    formData.append('content', content); // 即使为空也添加
    formData.append('url', url); // 添加链接地址
    formData.append('display_order', displayOrder);
    
    // 添加图片
    if (file && !imageRemoved) {
        formData.append('image', file);
    }
    
    // 发送请求
    $.ajax({
        type: "post",
        url: "/apis/company_info/add",
        data: formData,
        contentType: false,
        processData: false,
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#addInfoAlert').modal('hide');
                
                // 重置表单
                resetAddForm();
                
                // 刷新数据
                company_info_list(currentPage, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '添加公司详情成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '添加公司详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，添加公司详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 修复图片路径
 * @param {string} path 原始路径
 * @returns {string} 修复后的路径
 */
function fixImagePath(path) {
    if (!path) return '';
    
    // 处理绝对路径问题
    if (path.includes('C:')) {
        // 如果是Windows绝对路径，提取文件名
        var fileName = path.split('\\').pop().split('/').pop();
        var result = '/uploads/' + fileName;
        return result;
    } else if (!path.startsWith('http') && !path.startsWith('/')) {
        // 相对路径，添加前导斜杠
        var result = '/' + path;
        return result;
    }
    
    return path;
}

/**
 * 显示编辑模态框
 * @param {number} id 公司详情ID
 */
function show_edit_modal(id) {
    // 重置图片清除标志
    $('#edit_image_removed').val('false');
    
    // 先清空图片预览
    $('#edit_image').attr('src', '');
    
    // 获取详情数据
    $.ajax({
        type: "get",
        url: "/apis/company_info/detail",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                var info = data.data;
                
                // 填充表单
                $('#edit_id').val(info.id);
                $('#edit_show').val(info.show ? 'True' : 'False');
                $('#edit_lang').val(info.lang);
                $('#edit_info_type').val(info.info_type);
                $('#edit_title').val(info.title || '');
                $('#edit_content').val(info.content || '');
                $('#edit_url').val(info.url || '');
                $('#edit_display_order').val(info.display_order || 100);
                
                // 显示文本和图片内容
                $('#edit_content_group').show();
                $('#edit_image_group').show();
                
                // 如果有图片路径，显示图片
                if (info.image_path) {
                    // 修复图片路径
                    var fixedPath = fixImagePath(info.image_path);
                    
                    // 设置图片预览
                    $('#edit_image').attr('src', fixedPath)
                    .on('load', function() {
                    })
                    .on('error', function() {
                        this.onerror = null;
                        this.src = '/admin/media/no-image.png';
                        this.style.opacity = 0.5;
                    });
                } else {
                    // 清空图片
                    $('#edit_image').attr('src', '');
                }
                
                // 显示模态框
                $('#editInfoAlert').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '获取公司详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，获取公司详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 编辑公司详情
 */
function edit_info() {
    // 获取表单数据
    var id = $('#edit_id').val();
    var show = $('#edit_show').val() === 'True';
    var lang = $('#edit_lang').val();
    var infoType = $('#edit_info_type').val();
    var title = $('#edit_title').val().trim();
    var content = $('#edit_content').val().trim();
    var url = $('#edit_url').val().trim();
    var displayOrder = $('#edit_display_order').val() || 100;
    var file = document.getElementById('edit_new_image').files[0];
    var imageRemoved = $('#edit_image_removed').val() === 'true';
    
    // 验证必填字段
    if (infoType === 'company_logo' && !file && imageRemoved && !content) {
        $.gritter.add({
            title: '错误',
            text: '请上传Logo图片或填写图片链接',
            sticky: false,
            time: 3000
        });
        return;
    }
    
    // 创建FormData对象
    var formData = new FormData();
    formData.append('id', id);
    formData.append('show', show);
    formData.append('lang', lang);
    formData.append('info_type', infoType);
    formData.append('title', title); // 即使为空也添加
    formData.append('content', content); // 即使为空也添加
    formData.append('url', url); // 添加链接地址
    formData.append('display_order', displayOrder);
    
    // 处理图片
    if (file && !imageRemoved) {
        // 如果有新上传的图片且没有清除图片，添加到表单
        formData.append('image', file);
    } else if (imageRemoved) {
        // 如果清除了图片，添加标记
        formData.append('remove_image', 'true');
    }
    
    // 发送请求
    $.ajax({
        type: "post",
        url: "/apis/company_info/update",
        data: formData,
        contentType: false,
        processData: false,
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#editInfoAlert').modal('hide');
                
                // 刷新数据
                company_info_list(currentPage, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '更新公司详情成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '更新公司详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，更新公司详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 显示删除确认模态框
 * @param {number} id 公司详情ID
 */
function show_delete_modal(id) {
    // 获取详情数据
    $.ajax({
        type: "get",
        url: "/apis/company_info/detail",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                var info = data.data;
                
                // 填充确认信息
                $('#del_id').val(info.id);
                $('#del_id_span').text(info.id);
                $('#del_title').text(info.title || '(无标题)');
                
                // 显示内容
                if (info.content && info.content.trim() !== '') {
                    $('#del_content').text(info.content);
                } else {
                    $('#del_content').text('(无文本内容)');
                }
                
                // 显示图片
                if (info.image_path && info.image_path.trim() !== '') {
                    $('#del_image_container').show();
                    
                    // 修复图片路径
                    var fixedPath = fixImagePath(info.image_path);
                    $('#del_image').attr('src', fixedPath).show();
                    
                    // 添加错误处理
                    $('#del_image').on('error', function() {
                        this.onerror = null;
                        this.src = '/admin/media/no-image.png';
                        this.style.opacity = 0.5;
                    });
                } else {
                    $('#del_image_container').hide();
                    $('#del_image').hide();
                }
                
                // 显示模态框
                $('#deleteInfoAlert').modal('show');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '获取公司详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，获取公司详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 删除公司详情
 */
function del_info() {
    var id = $('#del_id').val();
    
    $.ajax({
        type: "post",
        url: "/apis/company_info/delete",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                // 关闭模态框
                $('#deleteInfoAlert').modal('hide');
                
                // 刷新数据
                company_info_list(currentPage, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '删除公司详情成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '删除公司详情失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，删除公司详情失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

/**
 * 分页控制
 * @param {number} currentPage 当前页码
 * @param {number} totalPage 总页数
 */
function page_ctrl(currentPage, totalPage) {
    var html = "";
    
    // 首页
    html += "<a tabindex='0' class='first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='company_info_list(1, " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>首页</a>";
    
    // 上一页
    html += "<a tabindex='0' class='previous fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='company_info_list(" + (currentPage - 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>上一页</a>";
    
    // 页码
    var startPage = Math.max(1, currentPage - 2);
    var endPage = Math.min(totalPage, startPage + 4);
    
    for (var i = startPage; i <= endPage; i++) {
        html += "<a tabindex='0' class='fg-button ui-button ui-state-default " + 
            (i === currentPage ? "ui-state-disabled" : "") + 
            "' onclick='company_info_list(" + i + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>" + i + "</a>";
    }
    
    // 下一页
    html += "<a tabindex='0' class='next fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='company_info_list(" + (currentPage + 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>下一页</a>";
    
    // 尾页
    html += "<a tabindex='0' class='last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='company_info_list(" + totalPage + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentInfoType + "\")'>尾页</a>";
    
    $("#page").html(html);
}

/**
 * 重置添加表单
 */
function resetAddForm() {
    // 重置表单字段
    $('#add_title').val('');
    $('#add_content').val('');
    $('#add_image').attr('src', '');
    $('#add_new_image').val('');
    $('#add_display_order').val('100');
    $('#add_image_removed').val('false');
    $('#add_url').val(''); // 重置链接地址
    
    // 重置下拉框
    $('#add_show').val('True');
    $('#add_lang').val('0');
    $('#add_info_type').val('company_profile');
}

// 页面加载完成后执行
$(document).ready(function() {
    // 加载公司详情列表
    company_info_list();
    
    // 绑定图片预览事件
    $('#add_new_image').change(function() {
        previewImage(this, '#add_image');
        // 如果选择了新图片，重置清除标志
        $('#add_image_removed').val('false');
    });
    
    $('#edit_new_image').change(function() {
        previewImage(this, '#edit_image');
        // 如果选择了新图片，重置清除标志
        $('#edit_image_removed').val('false');
    });
    
    // 绑定清除图片按钮事件
    $('#add_clear_image').click(function() {
        // 清除图片预览
        $('#add_image').attr('src', '');
        // 清除文件输入
        $('#add_new_image').val('');
        // 设置清除标志
        $('#add_image_removed').val('true');
    });
    
    $('#edit_clear_image').click(function() {
        // 清除图片预览
        $('#edit_image').attr('src', '');
        // 清除文件输入
        $('#edit_new_image').val('');
        // 设置清除标志
        $('#edit_image_removed').val('true');
    });
    
    // 绑定筛选事件
    $('#lang_filter').change(function() {
        currentLang = $(this).val();
        company_info_list(1, pageSize, currentLang, currentInfoType);
    });
    
    $('#info_type_filter').change(function() {
        currentInfoType = $(this).val();
        company_info_list(1, pageSize, currentLang, currentInfoType);
    });
    
    // 绑定模态框事件
    $('#addInfoAlert').on('show', function() {
        // 重置添加表单
        resetAddForm();
    });
    
    // 修改显示顺序提示文本
    $('.help-block').each(function() {
        var text = $(this).text();
        if (text.includes('对于公司简介：小的在左侧，大的在右侧')) {
            $(this).text(text.replace('对于公司简介：小的在左侧，大的在右侧', '对于公司简介和关注我们：小的在左侧，大的在右侧'));
        }
    });
});

/**
 * 图片预览函数
 * @param {HTMLElement} input 文件输入元素
 * @param {string} previewSelector 预览图片的选择器
 */
function previewImage(input, previewSelector) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            $(previewSelector).attr('src', e.target.result);
        }
        
        reader.onerror = function(e) {
            $(previewSelector).attr('src', '/admin/media/no-image.png');
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

/**
 * 添加显示顺序的帮助提示
 */
function addDisplayOrderHelp() {
    // 检查是否已经添加了帮助提示
    if ($('#display_order_help').length === 0) {
        // 在表格上方添加帮助提示
        var helpText = '<div id="display_order_help" class="alert alert-info" style="margin-top: 10px;">' +
            '<strong>显示顺序说明：</strong>' +
            '<ul>' +
            '<li>数字越小越靠前显示</li>' +
            '<li>对于公司简介、关注我们和服务与支持：数字小的排在左侧，数字大的排在右侧</li>' +
            '<li>对于热门产品：数字小的排在左侧，数字大的排在右侧</li>' +
            '<li>对于产品中心和其他类型：数字小的排在上面，数字大的排在下面</li>' +
            '<li>对于关于我们-技术实力和维基教程-可选内容：数字小的排在左侧，数字大的排在右侧</li>' +
            '<li>对于贝启动科技动态-首页：数字小的排在上面，数字大的排在下面</li>' +
            '<li>对于关于贝启科技-首页：第一个（显示顺序最小）用于文字介绍，第二个用于左侧小图，第三个用于右侧大图</li>' +
            '<li>对于工具栏：显示顺序决定工具栏按钮的排列顺序，从上到下</li>' +
            '<li>对于方案定制：显示顺序决定定制选项的排列顺序，从左到右</li>' +
            '<li>对于方案定制-详情：显示顺序决定详情内容的排列顺序，数字小的排在前面</li>' +
            '<li>可以通过调整显示顺序来控制内容的显示位置</li>' +
            '</ul>' +
            '</div>';
        
        $('.widget-box').first().after(helpText);
    }
}

/**
 * 更新内容的显示状态
 * @param {number} id 内容ID
 * @param {number} showValue 显示状态值 (1=显示, 0=不显示)
 */
function updateShowStatus(id, showValue) {
    // 使用专门的API更新显示状态
    $.ajax({
        type: "post",
        url: "/apis/company_info/updateShowStatusOnly",  // 使用专门的API
        data: {
            id: id,
            show: showValue === 1
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 刷新列表
                company_info_list(currentPage, pageSize, currentLang, currentInfoType);
                
                // 显示成功提示
                $.gritter.add({
                    title: '成功',
                    text: '更新显示状态成功',
                    sticky: false,
                    time: 3000
                });
            } else {
                $.gritter.add({
                    title: '错误',
                    text: data.msg || '更新显示状态失败',
                    sticky: false,
                    time: 3000
                });
            }
        },
        error: function (xhr, status, error) {
            $.gritter.add({
                title: '错误',
                text: '网络错误，更新显示状态失败',
                sticky: false,
                time: 3000
            });
        }
    });
}

// 根据选择的信息类型切换显示图片或内容输入框
function toggleAddImageContent() {
    var infoType = $('#add_info_type').val();
    // 默认显示所有内容
    $('#add_content_group').show();
    $('#add_image_group').show();
    
    // 根据不同类型调整表单
    if (infoType === 'toolbar') {
        // 工具栏需要图标和链接
        $('#add_content_group').show(); // 用于存储链接或功能配置
        $('#add_image_group').show();   // 用于上传工具栏图标
        $('#add_title').attr('placeholder', '工具栏按钮名称');
        $('#add_content').attr('placeholder', '工具栏按钮功能配置（JSON格式）');
    } else if (infoType === 'solution_customize') {
        // 方案定制需要图标、标题和描述
        $('#add_image_group').show();   // 用于上传定制选项图标
        $('#add_content_group').show(); // 用于定制选项描述
        $('#add_title').attr('placeholder', '定制选项名称');
        $('#add_content').attr('placeholder', '定制选项描述');
    } else if (infoType === 'solution_customize_detail') {
        // 方案定制-详情需要更详细的内容描述和可选图片
        $('#add_image_group').show();   // 用于上传相关图片（可选）
        $('#add_content_group').show(); // 用于详细描述
        $('#add_title').attr('placeholder', '详情标题');
        $('#add_content').attr('placeholder', '详细描述内容');
        $('#add_url').attr('placeholder', '相关链接（可选）');
    } else if (infoType === 'about_banner') {
        // Banner主要是图片，内容可选
        $('#add_content_group').show();
    } else if (infoType === 'technology_highlight') {
        // 公司简介主要是标题和文本内容
        $('#add_image_group').show();
    } else if (infoType === 'our_technology') {
        // 我们的技术需要图标、标题和描述
        $('#add_image_group').show();
        $('#add_content_group').show();
    } else if (infoType === 'hot_products') {
        // 热门产品需要图片、标题和可选的内容描述
        $('#add_image_group').show();
        $('#add_content_group').show();
    } else if (infoType === 'company_news') {
        // 贝启动科技动态需要标题、内容描述和可选的图片
        $('#add_image_group').show();
        $('#add_content_group').show();
    } else if (infoType === 'about_company_home') {
        // 关于贝启科技-首页需要标题、内容描述和图片
        $('#add_image_group').show();
        $('#add_content_group').show();
    }
    
    // 重置占位符文本
    if (infoType !== 'toolbar' && infoType !== 'solution_customize' && infoType !== 'solution_customize_detail') {
        $('#add_title').attr('placeholder', '');
        $('#add_content').attr('placeholder', '');
        $('#add_url').attr('placeholder', 'http://...');
    }
} 